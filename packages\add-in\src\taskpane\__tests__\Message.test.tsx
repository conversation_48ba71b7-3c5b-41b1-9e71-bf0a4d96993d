import React from "react";
import { render, screen } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { Message } from "../Message";
import { TableData } from "../chat-types";

jest.mock("../PreviewTable", () => ({
  PreviewTable: ({ tableData }: { tableData: any }) => (
    <div data-testid="preview-table">Table with {tableData.length} rows</div>
  ),
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

const mockTableData: TableData = [
  ["Header 1", "Header 2"],
  ["Row 1 Col 1", "Row 1 Col 2"],
  ["Row 2 Col 1", "Row 2 Col 2"],
];

describe("Message", () => {
  it("renders simple message", () => {
    renderWithProvider(<Message message="Hello world" cardClassName="test-card" containerClassName="test-container" />);

    expect(screen.getByText("Hello world")).toBeInTheDocument();
  });

  it("renders message with table placeholder", () => {
    renderWithProvider(
      <Message
        message="Before table {{TABLE}} After table"
        tableData={mockTableData}
        cardClassName="test-card"
        containerClassName="test-container"
      />
    );

    expect(screen.getByText("Before table")).toBeInTheDocument();
    expect(screen.getByText("After table")).toBeInTheDocument();
    expect(screen.getByTestId("preview-table")).toBeInTheDocument();
  });

  it("renders table without message parts when message is undefined", () => {
    renderWithProvider(
      <Message tableData={mockTableData} cardClassName="test-card" containerClassName="test-container" />
    );

    expect(screen.getByTestId("preview-table")).toBeInTheDocument();
  });

  it("renders loading skeleton when loading is true", () => {
    renderWithProvider(
      <Message message="Loading message" cardClassName="test-card" containerClassName="test-container" loading={true} />
    );

    expect(screen.getByText("Loading message")).toBeInTheDocument();
    expect(screen.getByRole("progressbar")).toBeInTheDocument();
  });

  it("renders footer when provided", () => {
    const footer = <div data-testid="custom-footer">Custom footer content</div>;

    renderWithProvider(
      <Message
        message="Message with footer"
        cardClassName="test-card"
        containerClassName="test-container"
        footer={footer}
      />
    );

    expect(screen.getByText("Message with footer")).toBeInTheDocument();
    expect(screen.getByTestId("custom-footer")).toBeInTheDocument();
  });

  it("applies correct CSS classes", () => {
    const { container } = renderWithProvider(
      <Message message="Test" cardClassName="custom-card-class" containerClassName="custom-container-class" />
    );

    const containerDiv = container.querySelector(".custom-container-class");
    const card = container.querySelector('[class*="custom-card-class"]');

    expect(containerDiv).toBeInTheDocument();
    expect(card).toBeInTheDocument();
  });

  it("handles message with only first part before table", () => {
    renderWithProvider(
      <Message
        message="Only before {{TABLE}}"
        tableData={mockTableData}
        cardClassName="test-card"
        containerClassName="test-container"
      />
    );

    expect(screen.getByText("Only before")).toBeInTheDocument();
    expect(screen.getByTestId("preview-table")).toBeInTheDocument();
  });

  it("handles message with only second part after table", () => {
    renderWithProvider(
      <Message
        message="{{TABLE}} Only after"
        tableData={mockTableData}
        cardClassName="test-card"
        containerClassName="test-container"
      />
    );

    expect(screen.getByText("Only after")).toBeInTheDocument();
    expect(screen.getByTestId("preview-table")).toBeInTheDocument();
  });
});

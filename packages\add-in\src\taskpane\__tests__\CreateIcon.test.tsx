import React from "react";
import { render, screen } from "@testing-library/react";
import createIcon from "../CreateIcon";

describe("createIcon", () => {
  const testPath = "M10 20 L30 40 Z";
  const testTitle = "Test Icon";

  it("creates an icon component with default props", () => {
    const IconComponent = createIcon({ path: testPath });
    render(<IconComponent />);

    const svg = document.querySelector("svg");
    expect(svg).toBeInTheDocument();
    expect(svg).toHaveAttribute("viewBox", "0 0 80 80");
    expect(svg).toHaveAttribute("width", "24");
    expect(svg).toHaveAttribute("height", "24");
  });

  it("creates an icon component with custom props", () => {
    const IconComponent = createIcon({
      path: testPath,
      title: testTitle,
      width: "32",
      height: "32",
      viewBox: "0 0 100 100",
    });
    render(<IconComponent />);

    const svg = document.querySelector("svg");
    expect(svg).toHaveAttribute("viewBox", "0 0 100 100");
    expect(svg).toHaveAttribute("width", "32");
    expect(svg).toHaveAttribute("height", "32");

    expect(screen.getByText(testTitle)).toBeInTheDocument();
  });

  it("renders the path correctly", () => {
    const IconComponent = createIcon({ path: testPath });
    render(<IconComponent />);

    const path = document.querySelector("path");
    expect(path).toHaveAttribute("d", testPath);
    expect(path).toHaveAttribute("fill", "currentColor");
  });

  it("accepts additional SVG props through iconProps", () => {
    const IconComponent = createIcon({ path: testPath });
    render(<IconComponent className="custom-icon" data-testid="icon" />);

    const svg = screen.getByTestId("icon");
    expect(svg).toHaveClass("custom-icon");
  });

  it("does not render title when not provided", () => {
    const IconComponent = createIcon({ path: testPath });
    render(<IconComponent />);

    expect(screen.queryByText(testTitle)).not.toBeInTheDocument();
  });
});

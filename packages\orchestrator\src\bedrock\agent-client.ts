import {
    AgentAliasSummary,
    BedrockAgentClient,
    ListAgentAliasesCommand
} from '@aws-sdk/client-bedrock-agent';
import logger from '../logger';

interface AgentAlias {
    agentAliasName?: string;
    agentAliasId?: string;
}

const ALIASES_CACHE = {
    agentAliases: null as AgentAlias[] | null,
    expiryTime: null as Date | null,
};

const getAgentAliases = async (): Promise<AgentAlias[]> => {
    const currentTime = new Date();

    if (
        ALIASES_CACHE.agentAliases &&
        ALIASES_CACHE.expiryTime &&
        ALIASES_CACHE.expiryTime > currentTime
    ) {       
        const remainingMinutes =
            (ALIASES_CACHE.expiryTime.getTime() - currentTime.getTime()) /
            (1000 * 60);

        logger.debug(
            `Using cached agent aliases. Valid for ${remainingMinutes.toFixed(
                1,
            )} more minutes`,
        );
        
        return ALIASES_CACHE.agentAliases;
    }

    try {
        const client = new BedrockAgentClient({});
        const { agentAliasSummaries } = await client.send(new ListAgentAliasesCommand({
            agentId: process.env.AGENT_ID
        }));
    
        logger.debug({ agentAliasSummaries }, 'Valuations agent aliases fetched');
    
        const withVersionOrOrchestrator = ({ agentAliasName }: AgentAliasSummary) => /^version\d+$/.test(agentAliasName ?? '') || agentAliasName === 'orchestrator';
    
        const versionAliases = agentAliasSummaries?.filter(withVersionOrOrchestrator)
            .map(({ agentAliasName, agentAliasId }) => ({ agentAliasName, agentAliasId })) || [];
    
        logger.info({ versionAliases }, 'Valuations agent aliases filtered');
    
        const expiryTime = new Date();
        expiryTime.setMinutes(expiryTime.getMinutes() + 60);
        ALIASES_CACHE.agentAliases = versionAliases;
        ALIASES_CACHE.expiryTime = expiryTime;
    
        return versionAliases;
     } catch (error) {
        logger.error(error as Error, 'Error fetching valuations agent aliases');
        throw error;
    }
};

export { getAgentAliases };
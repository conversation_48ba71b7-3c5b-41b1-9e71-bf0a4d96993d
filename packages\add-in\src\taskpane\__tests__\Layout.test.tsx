import React from "react";
import { render, screen } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { Layout } from "../Layout";

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("Layout", () => {
  it("renders children correctly", () => {
    renderWithProvider(
      <Layout>
        <div data-testid="child-content">Test content</div>
      </Layout>
    );

    expect(screen.getByTestId("child-content")).toBeInTheDocument();
    expect(screen.getByText("Test content")).toBeInTheDocument();
  });

  it("renders multiple children", () => {
    renderWithProvider(
      <Layout>
        <div data-testid="child-1">First child</div>
        <div data-testid="child-2">Second child</div>
        <span data-testid="child-3">Third child</span>
      </Layout>
    );

    expect(screen.getByTestId("child-1")).toBeInTheDocument();
    expect(screen.getByTestId("child-2")).toBeInTheDocument();
    expect(screen.getByTestId("child-3")).toBeInTheDocument();
  });

  it("renders with layout wrapper styling", () => {
    const { container } = renderWithProvider(
      <Layout>
        <div>Content</div>
      </Layout>
    );

    const wrapper = container.firstChild;
    expect(wrapper).toBeInTheDocument();
    // Layout adds styling classes, we verify the element exists and has some class
    expect(wrapper).toHaveAttribute("class");
  });

  it("handles empty children", () => {
    renderWithProvider(
      <Layout>
        <div />
      </Layout>
    );

    const { container } = renderWithProvider(
      <Layout>
        <div />
      </Layout>
    );
    expect(container.firstChild).toBeInTheDocument();
  });

  it("handles null children", () => {
    renderWithProvider(<Layout>{null}</Layout>);

    const { container } = renderWithProvider(<Layout>{null}</Layout>);
    expect(container.firstChild).toBeInTheDocument();
  });
});

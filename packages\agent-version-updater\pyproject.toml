[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "wmlgv-assistant-agent-version-updater"
version = "0.1.0"
description = "Utility for managing AWS Bedrock agent versions and aliases"
requires-python = ">=3.12"
dependencies = [
    "boto3>=1.34.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black",
    "flake8",
    "mypy",
]
test = [
    "pytest>=6.0",
    "pytest-cov",
]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]

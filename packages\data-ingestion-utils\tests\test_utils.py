from utils import to_snake_case


def test_to_snake_case_camel_case():
    assert to_snake_case("camelCase") == "camel_case"
    assert to_snake_case("anotherCamelCase") == "another_camel_case"
    assert to_snake_case("camelCaseWithNumber123") == "camel_case_with_number123"
    assert to_snake_case("camel123Case") == "camel123_case"


def test_to_snake_case_pascal_case():
    assert to_snake_case("PascalCase") == "pascal_case"
    assert to_snake_case("SimplePascalCase") == "simple_pascal_case"


def test_to_snake_case_acronyms():
    assert to_snake_case("SQLServer") == "sql_server"
    assert to_snake_case("APIResponse") == "api_response"
    assert to_snake_case("HTMLPage") == "html_page"


def test_to_snake_case_spaces():
    assert to_snake_case("text with spaces") == "text_with_spaces"
    assert to_snake_case("  leading spaces") == "leading_spaces"
    assert to_snake_case("trailing spaces  ") == "trailing_spaces"


def test_to_snake_case_hyphens_parentheses():
    assert to_snake_case("foo (bar)") == "foo_bar"


def test_to_snake_case_hyphens_slashes():
    assert to_snake_case("text-with-hyphens") == "text_with_hyphens"
    assert to_snake_case("path/with/slashes") == "path_with_slashes"
    assert (
        to_snake_case("mixed-path/with/hyphens-and/slashes")
        == "mixed_path_with_hyphens_and_slashes"
    )


def test_to_snake_case_combined_patterns():
    assert to_snake_case("CamelCase with spaces") == "camel_case_with_spaces"
    assert to_snake_case("PascalCase-with-hyphens") == "pascal_case_with_hyphens"
    assert to_snake_case("API/Response/URL") == "api_response_url"
    assert to_snake_case("MixedCASE_with_underscores") == "mixed_case_with_underscores"


def test_to_snake_case_edge_cases():
    assert to_snake_case(None) == ""
    assert to_snake_case("") == ""
    assert to_snake_case(" ") == ""
    assert to_snake_case("A") == "a"
    assert to_snake_case("already_snake_case") == "already_snake_case"

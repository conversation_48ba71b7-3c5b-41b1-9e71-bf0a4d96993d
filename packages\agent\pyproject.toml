[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "wmlgv-assistant-agent"
version = "0.1.0"
description = "AWS Lambda function for handling AI agent requests for data queries"
requires-python = ">=3.12"
dependencies = [
    "requests==2.32.3",
    "polars==1.16.0",
    "s3fs==2024.10.0",
    "python-json-logger==3.2.1",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black",
    "flake8",
    "mypy",
]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true

import { NextFunction, Response } from 'express';
import { AuthenticatedRequest } from './auth-middleware';
import { createEntitlementsMiddleware } from './entitlements-middleware';
import * as entitlementsServiceModule from './entitlements-service';

jest.mock('./entitlements-service');
jest.mock('../logger', () => ({
    debug: jest.fn(),
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
}));

describe('EntitlementsMiddleware', () => {
    let mockRequest: Partial<AuthenticatedRequest>;
    let mockResponse: Partial<Response>;
    let mockNext: NextFunction;
    let jsonSpy: jest.Mock;
    let statusSpy: jest.Mock;

    beforeEach(() => {
        jsonSpy = jest.fn();
        statusSpy = jest.fn().mockReturnValue({ json: jsonSpy });

        mockRequest = {
            path: '/test-path',
        };

        mockResponse = {
            status: statusSpy,
            json: jsonSpy,
        };

        mockNext = jest.fn();

        jest.clearAllMocks();
    });

    it('should call next() when user has entitlements', async () => {
        const mockUser = { id: 'user123', email: '<EMAIL>' };
        mockRequest.user = mockUser;

        const mockCheckUserEntitlement = jest.fn().mockResolvedValue(true);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (entitlementsServiceModule.entitlementsService as any) = {
            checkUserEntitlement: mockCheckUserEntitlement,
        };

        const middleware = createEntitlementsMiddleware('vals-chat-assistant','VALSCHATASSIST');
        await middleware(
            mockRequest as AuthenticatedRequest,
            mockResponse as Response,
            mockNext,
        );

        expect(mockCheckUserEntitlement).toHaveBeenCalledWith(
            'user123',
            'vals-chat-assistant',
            'VALSCHATASSIST'
        );
        expect(mockNext).toHaveBeenCalled();
        expect(statusSpy).not.toHaveBeenCalled();
    });

    it('should return 403 when user has no entitlements', async () => {
        const mockUser = { id: 'user123', email: '<EMAIL>' };
        mockRequest.user = mockUser;

        const mockCheckUserEntitlement = jest.fn().mockResolvedValue(false);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (entitlementsServiceModule.entitlementsService as any) = {
            checkUserEntitlement: mockCheckUserEntitlement,
        };

        const middleware = createEntitlementsMiddleware('vals-chat-assistant','VALSCHATASSIST');
        await middleware(
            mockRequest as AuthenticatedRequest,
            mockResponse as Response,
            mockNext,
        );

        expect(statusSpy).toHaveBeenCalledWith(403);
        expect(jsonSpy).toHaveBeenCalledWith({
            message: 'Access denied',
            error: 'Forbidden',
        });
        expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 403 when user ID is missing', async () => {
        mockRequest.user = { email: '<EMAIL>' };

        const middleware = createEntitlementsMiddleware('vals-chat-assistant','VALSCHATASSIST');
        await middleware(
            mockRequest as AuthenticatedRequest,
            mockResponse as Response,
            mockNext,
        );

        expect(statusSpy).toHaveBeenCalledWith(403);
        expect(mockNext).not.toHaveBeenCalled();
    });

    it('should return 403 when user is not authenticated', async () => {
        const middleware = createEntitlementsMiddleware('vals-chat-assistant','VALSCHATASSIST');
        await middleware(
            mockRequest as AuthenticatedRequest,
            mockResponse as Response,
            mockNext,
        );

        expect(statusSpy).toHaveBeenCalledWith(403);
        expect(mockNext).not.toHaveBeenCalled();
    });

    it('should use custom feature parameter', async () => {
        const mockUser = { id: 'user123', email: '<EMAIL>' };
        mockRequest.user = mockUser;

        const mockCheckUserEntitlement = jest.fn().mockResolvedValue(true);
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (entitlementsServiceModule.entitlementsService as any) = {
            checkUserEntitlement: mockCheckUserEntitlement,
        };

        const middleware = createEntitlementsMiddleware('vals-chat-assistant','VALSCHATASSIST');
        await middleware(
            mockRequest as AuthenticatedRequest,
            mockResponse as Response,
            mockNext,
        );

        expect(mockCheckUserEntitlement).toHaveBeenCalledWith(
            'user123',
            'vals-chat-assistant',
            'VALSCHATASSIST'
        );
    });

    it('should return 403 when entitlements service throws error', async () => {
        const mockUser = { id: 'user123', email: '<EMAIL>' };
        mockRequest.user = mockUser;

        const mockCheckUserEntitlement = jest
            .fn()
            .mockRejectedValue(new Error('Service error'));
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        (entitlementsServiceModule.entitlementsService as any) = {
            checkUserEntitlement: mockCheckUserEntitlement,
        };

        const middleware = createEntitlementsMiddleware('vals-chat-assistant','VALSCHATASSIST');
        await middleware(
            mockRequest as AuthenticatedRequest,
            mockResponse as Response,
            mockNext,
        );

        expect(statusSpy).toHaveBeenCalledWith(403);
        expect(mockNext).not.toHaveBeenCalled();
    });
});

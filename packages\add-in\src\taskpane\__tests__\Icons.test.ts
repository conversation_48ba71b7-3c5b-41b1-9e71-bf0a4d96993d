import { Icon24Valuation, Icon24PlayAudio } from "../Icons";

describe("Icons", () => {
  it("exports Icon24Valuation with correct properties", () => {
    expect(Icon24Valuation).toBeDefined();
    expect(typeof Icon24Valuation).toBe("function");
  });

  it("exports Icon24PlayAudio with correct properties", () => {
    expect(Icon24PlayAudio).toBeDefined();
    expect(typeof Icon24PlayAudio).toBe("function");
  });

  it("all icons should be React components", () => {
    const icons = [Icon24Valuation, Icon24PlayAudio];

    icons.forEach((Icon) => {
      expect(typeof Icon).toBe("function");
      const instance = Icon({});
      expect(instance).toBeDefined();
      expect(typeof instance).toBe("object");
    });
  });
});

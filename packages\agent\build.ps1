# Define variables
$packageDir = "package"
$zipFile = "wmlgv-assistant-agent.zip"
$dockerImage = "public.ecr.aws/lambda/python:3.12"

Write-Host "Starting build in Lambda container..."

# Update schema with definitions from CSV
Write-Host "Updating schema with definitions from CSV..."
python update_schema_with_definitions.py

# Create the package directory
New-Item -ItemType Directory -Path $packageDir -Force | Out-Null

# Copy necessary files
Copy-Item -Path "tools" -Destination $packageDir -Recurse -Force
Get-ChildItem -Path "*.py" -File | Copy-Item -Destination $packageDir -Force
Copy-Item -Path "requirements.txt" -Destination $packageDir -Force
Copy-Item -Path "wmlgv-assistant-agent-schema.yaml" -Destination $packageDir -Force

# Run the Docker container to install dependencies
docker run --rm `
  -v "$(Get-Location)/package:/var/task" `
  --entrypoint /bin/bash `
  public.ecr.aws/lambda/python:3.12 `
  -c "pip install --target /var/task -r /var/task/requirements.txt && cd /var/task"

# Create a zip file
Set-Location -Path $packageDir
Compress-Archive -Path * -DestinationPath "../$zipFile" -Force
Set-Location -Path ..

# Clean up the package directory
Remove-Item -Path $packageDir -Recurse -Force

Write-Host "Build complete. $zipFile created."

import axios from 'axios';
import logger from '../logger';
import { SecretsManagerClient, GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';

interface CachedToken {
    token: string;
    expiryTime: Date;
}

interface CachedEntitlement {
    hasAccess: boolean;
    expiryTime: Date;
}

interface CachedSecret {
    secret: string | undefined;
    expiryTime: Date;
}


interface EntitlementResponse {
    totalCount: number;
    count: number;
    results: {
        principalId: string;
        data: {
            val: string;
            typ: string;
        }[];
    }[];
}

const TOKEN_CACHE = new Map<string, CachedToken>();
const ENTITLEMENT_CACHE = new Map<string, CachedEntitlement>();
let SECRET_CACHE: CachedSecret | undefined;

const TOKEN_EXPIRY_BUFFER_MINUTES = 10;
const SECRET_CACHE_DURATION_MINUTES = 10;
const ENTITLEMENT_CACHE_DURATION_MINUTES = 60;

export class EntitlementsService {
    private oauthUrl: string;
    private clientId: string;
    private clientSecretName: string;
    private entitlementsApiUrl: string;
    private isConfigured: boolean;

    constructor() {
        this.oauthUrl = process.env.ENTITLEMENTS_OAUTH_URL || '';
        this.clientId = process.env.ENTITLEMENTS_CLIENT_ID || '';
        this.clientSecretName = process.env.ENTITLEMENTS_CLIENT_SECRET_NAME || '';
        this.entitlementsApiUrl = process.env.ENTITLEMENTS_API_URL || '';

        this.isConfigured = !!(
            this.oauthUrl &&
            this.clientId &&
            this.clientSecretName &&
            this.entitlementsApiUrl
        );

        if (!this.isConfigured) {
            logger.error(
                {
                    oauthUrl: !!this.oauthUrl,
                    clientId: !!this.clientId,
                    clientSecretName: !!this.clientSecretName,
                    entitlementsApiUrl: !!this.entitlementsApiUrl,
                },
                'Missing entitlements configuration',
            );
        }
    }

    clearCaches(): void {
        TOKEN_CACHE.clear();
        ENTITLEMENT_CACHE.clear();
        SECRET_CACHE = undefined;
    }

    private async getClientSecret(): Promise<string | undefined> {
        const cached = SECRET_CACHE;
        const currentTime = new Date();

        if (cached && cached.secret && cached.expiryTime > currentTime) {
            const remainingMinutes =
                (cached.expiryTime.getTime() - currentTime.getTime()) /
                (1000 * 60);

            logger.debug(`Using cached client secret. Valid for ${remainingMinutes.toFixed(
                    1,
                )} more minutes`);

            return cached.secret;
        }

        try {
            const smClient = new SecretsManagerClient({});
            const { SecretString: clientSecret } = await smClient.send(
                new GetSecretValueCommand({
                    SecretId: this.clientSecretName,
                }),
            );
    
            const expiryTime = this.getExpiryTime(SECRET_CACHE_DURATION_MINUTES);

            SECRET_CACHE = {
                secret: clientSecret,
                expiryTime,
            };

            return clientSecret;
        } catch (error) {
            logger.error(
                error as Error,
                'Failed to get client secret for entitlements',
            );
        }
    }

    private getExpiryTime(expiryMinutes: number) {
        const expiryTime = new Date();
        expiryTime.setMinutes(
            expiryTime.getMinutes() + expiryMinutes
        );
        return expiryTime;
    }

    private async getOAuthToken(): Promise<string | null> {
        if (!this.isConfigured) {
            logger.error('Entitlements service not configured');
            return null;
        }

        const cacheKey = `${this.clientId}:user.effective-role:read`;
        const currentTime = new Date();

        const cached = TOKEN_CACHE.get(cacheKey);
        if (cached && cached.expiryTime > currentTime) {
            const remainingMinutes =
                (cached.expiryTime.getTime() - currentTime.getTime()) /
                (1000 * 60);
            logger.debug(
                `Using cached OAuth token. Valid for ${remainingMinutes.toFixed(
                    1,
                )} more minutes`,
            );
            return cached.token;
        }

        try {
            const clientSecret = await this.getClientSecret();

            if (!clientSecret) {
                logger.error('Failed to retrieve client secret for OAuth');
                return null;
            }
            const response = await axios.post(
                this.oauthUrl,
                new URLSearchParams({
                    grant_type: 'client_credentials',
                    scope: 'user.effective-role:read',
                }),
                {
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        Authorization: `Basic ${Buffer.from(
                            `${this.clientId}:${clientSecret}`,
                        ).toString('base64')}`,
                    },
                    timeout: 10000,
                },
            );

            const { access_token, expires_in } = response.data;

            if (!access_token) {
                logger.error('No access_token in OAuth response');
                return null;
            }

            const expiryMinutes = Math.max(
                expires_in / 60 - TOKEN_EXPIRY_BUFFER_MINUTES,
                1,
            );

            const expiryTime = this.getExpiryTime(expiryMinutes);

            TOKEN_CACHE.set(cacheKey, {
                token: access_token,
                expiryTime,
            });

            logger.debug('OAuth token retrieved and cached successfully');
            return access_token;
        } catch (error) {
            logger.error(
                error as Error,
                'Failed to get OAuth token for entitlements',
            );
            return null;
        }
    }

    async checkUserEntitlement(
        userId: string,
        productType: string,
        upstreamEntitlement: string
    ): Promise<boolean> {
        if (!this.isConfigured) {
            logger.error('Entitlements service not configured');
            return false;
        }

        const cacheKey = `${userId}:${productType}:${upstreamEntitlement}`;
        const currentTime = new Date();

        const cached = ENTITLEMENT_CACHE.get(cacheKey);
        if (cached && cached.expiryTime > currentTime) {
            const remainingMinutes =
                (cached.expiryTime.getTime() - currentTime.getTime()) /
                (1000 * 60);
            logger.debug(
                { userId, productType, upstreamEntitlement },
                `Using cached entitlement. Valid for ${remainingMinutes.toFixed(
                    1,
                )} more minutes`,
            );
            return cached.hasAccess;
        }

        const token = await this.getOAuthToken();
        if (!token) {
            logger.error(
                { userId, productType, upstreamEntitlement },
                'Cannot check entitlements: failed to get OAuth token',
            );
            return false;
        }

        try {
            const url = `${this.entitlementsApiUrl}/api/v1/entitlements/select`; 
            const response = await axios.get<EntitlementResponse>(url, {
                headers: {
                    Authorization: `Bearer ${token}`,
                    Accept: 'application/json',
                },
                params: {  principalId: userId, productType: productType },
                timeout: 100000,
            });

            const hasAccess = response.data.totalCount > 0
                && response.data.results?.some(result =>
                    result.data?.some(item => item.val === upstreamEntitlement)
                );

            const expiryTime = this.getExpiryTime(ENTITLEMENT_CACHE_DURATION_MINUTES);

            ENTITLEMENT_CACHE.set(cacheKey, {
                hasAccess,
                expiryTime,
            });

            logger.info(
                {
                    userId,
                    productType,
                    upstreamEntitlement,
                    hasAccess,
                    totalCount: response.data.totalCount || 0,
                    resultsCount: response.data.results?.length || 0,
                },
                'Entitlement check completed',
            );

            return hasAccess;
        } catch (error) {
            logger.error(
                { error: error as Error, userId, productType, upstreamEntitlement },
                'Failed to check user entitlements',
            );
            return false;
        }
    }
}

export const entitlementsService = new EntitlementsService();

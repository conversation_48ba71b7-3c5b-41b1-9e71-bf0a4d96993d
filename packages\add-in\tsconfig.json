{"compilerOptions": {"allowUnusedLabels": false, "emitDecoratorMetadata": true, "esModuleInterop": true, "experimentalDecorators": true, "forceConsistentCasingInFileNames": true, "isolatedModules": true, "jsx": "react", "module": "esnext", "moduleResolution": "node", "noImplicitReturns": true, "noUnusedParameters": true, "outDir": "dist", "removeComments": false, "sourceMap": true, "target": "ES6", "lib": ["es7", "dom"], "pretty": true, "typeRoots": ["node_modules/@types"]}, "exclude": ["node_modules"], "compileOnSave": false, "buildOnSave": false, "ts-node": {"files": true}}
# Python Development Setup Guide

This document outlines the Python version and package management setup for the wmlgv-assistant project.

## Python Version Management

All Python packages in this project require Python 3.12+.

### Version Specification

Each Python package uses:

-   `pyproject.toml` with `requires-python = ">=3.12"`
-   `.python-version` file for pyenv compatibility

### Setup with pyenv + pip

```bash
# Install pyenv if not already installed
curl https://pyenv.run | bash

# Restart your shell or add to ~/.zshrc:
export PATH="$HOME/.pyenv/bin:$PATH"
eval "$(pyenv init -)"
eval "$(pyenv virtualenv-init -)"

# Install Python 3.12.1
pyenv install 3.12.1

# Set as default for this project (already done - .python-version files exist)
cd /path/to/wmlgv-assistant
pyenv local 3.12.1
```

## Package Management

### Standard pip + venv approach

For each Python package, create and activate a virtual environment:

```bash
# Example for the agent package:
cd packages/agent
python -m venv .venv
source .venv/bin/activate

# Install package in development mode
pip install -e .

# Install development dependencies
pip install -e ".[dev]"

# Or install from requirements.txt (legacy compatibility)
pip install -r requirements.txt
```

## Package Structure

Each Python package includes:

-   `pyproject.toml`: Modern Python project configuration with version constraints
-   `requirements.txt`: Production dependencies (keep for AWS Lambda compatibility)
-   `.python-version`: pyenv version specification
-   `.venv/`: Virtual environment (created locally, not committed)

## Workflow

### Daily Development

```bash
# Activate environment for a specific package
cd packages/agent
source .venv/bin/activate

# Install new dependencies
pip install new-package==1.0.0
pip freeze > requirements.txt  # Update requirements.txt

# Run your code
python lambda_function.py

# Deactivate when done
deactivate
```

### Running Tests

```bash
cd packages/data-ingestion-utils
source .venv/bin/activate
python -m pytest tests/
```

## IDE Configuration

The workspace is configured with VS Code settings in `wmlgv-assistant.code-workspace`. Each package will automatically use its `.venv` Python interpreter.

## CI/CD Considerations

-   Use `requirements.txt` for production deployments (especially AWS Lambda)
-   Use `pyproject.toml` for version constraints and development setup
-   Virtual environments (`.venv`) are local only and not committed to git

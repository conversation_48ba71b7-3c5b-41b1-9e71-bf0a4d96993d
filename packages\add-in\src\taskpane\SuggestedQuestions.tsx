import React from "react";
import { SuggestedQuestion } from "./SuggestedQuestion";

type SuggestedQuestionsProps = {
    handleChatMessageEntered: (message: string) => void;
    showSuggestedQuestions?: boolean;
    suggestedQuestions?: string[];
};

const SuggestedQuestions = ({
    showSuggestedQuestions, suggestedQuestions, handleChatMessageEntered
}: SuggestedQuestionsProps) => showSuggestedQuestions && suggestedQuestions?.map(question => (
    <SuggestedQuestion key={question} question={question} onSuggestedClicked={handleChatMessageEntered} />
));

export { SuggestedQuestions };

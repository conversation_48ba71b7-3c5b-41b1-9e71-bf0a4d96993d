import React from "react";
import { Message } from "./Message";
import { useStyles } from "./use-styles";
import { Button } from "@fluentui/react-components";

const UserMessage = ({ message }: { message: string; })  => {
    const { userMessage, userMessageContainer } = useStyles();

    return <Message message={message} cardClassName={userMessage} containerClassName={userMessageContainer} />;
};

export { UserMessage };

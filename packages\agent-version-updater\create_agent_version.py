import boto3
import sys
import argparse


def list_agent_versions(client, agent_id):
    paginator = client.get_paginator("list_agent_versions")
    versions = []

    for page in paginator.paginate(agentId=agent_id):
        versions.extend(page.get("agentVersionSummaries", []))

    return versions


def list_agent_aliases(client, agent_id):
    paginator = client.get_paginator("list_agent_aliases")
    aliases = []

    for page in paginator.paginate(agentId=agent_id):
        aliases.extend(page.get("agentAliasSummaries", []))

    return aliases


def delete_aliases_for_version(client, agent_id, version):
    aliases = list_agent_aliases(client, agent_id)
    aliases_to_delete = [
        alias
        for alias in aliases
        if any(
            route.get("agentVersion") == version
            for route in alias.get("routingConfiguration", [])
        )
    ]
    print(f"Aliases to delete for version {version}: {aliases_to_delete}")

    for alias in aliases_to_delete:
        alias_id = alias.get("agent<PERSON><PERSON>sId")
        alias_name = alias.get("agent<PERSON>liasName")
        print(
            f"Deleting alias '{alias_name}' (ID: {alias_id}) for version {version}..."
        )
        try:
            client.delete_agent_alias(agentId=agent_id, agentAliasId=alias_id)
            print(f"Successfully deleted alias '{alias_name}'")
        except Exception as e:
            print(f"Error deleting alias '{alias_name}': {e}")
            raise


def delete_agent_version(client, agent_id, version):
    print(f"Deleting agent version {version} for agent {agent_id}...")

    # First delete any aliases associated with this version
    delete_aliases_for_version(client, agent_id, version)

    # Then delete the version itself
    try:
        client.delete_agent_version(agentId=agent_id, agentVersion=version)
        print(f"Successfully deleted version {version}")
    except Exception as e:
        print(f"Error deleting version {version}: {e}")
        raise


def create_alias_from_draft(client, agent_id, version_number):
    alias_name = f"version{version_number}"
    print(f"Creating alias '{alias_name}' for agent {agent_id}...")

    client.create_agent_alias(
        agentId=agent_id,
        agentAliasName=alias_name,
        description=f"Alias for version {version_number}",
    )

    print(f"Created alias '{alias_name}' pointing to DRAFT.")


def create_new_alias_if_draft_updated(agent_id, region="us-east-1"):
    client = boto3.client("bedrock-agent", region_name=region)
    versions = list_agent_versions(client, agent_id)
    print(f"Versions for agent {agent_id}:")

    for v in versions:
        print(
            f"- Version: {v.get('agentVersion')}, Status: {v.get('agentStatus')}, Created: {v.get('createdAt', '')}, Updated: {v.get('updatedAt', '')}"
        )

    # Find draft and latest numeric version
    draft = next((v for v in versions if v.get("agentVersion") == "DRAFT"), None)
    numeric_versions = [
        v for v in versions if v.get("agentVersion") and v.get("agentVersion").isdigit()
    ]
    latest_numeric = max(
        numeric_versions, key=lambda x: int(x.get("agentVersion")), default=None
    )

    if draft and latest_numeric:
        draft_updated = draft.get("updatedAt")
        latest_updated = latest_numeric.get("updatedAt")
        latest_version_number = latest_numeric.get("agentVersion")

        if draft_updated > latest_updated:
            print("Draft is newer than the latest published version.")

            # Check if we need to delete oldest version due to 10-version limit
            if len(numeric_versions) >= 10:
                sorted_versions = sorted(
                    numeric_versions, key=lambda x: int(x.get("agentVersion"))
                )
                oldest_version = sorted_versions[0].get("agentVersion")
                print(
                    f"Maximum versions (10) reached. Will delete oldest version: {oldest_version}"
                )
                delete_agent_version(client, agent_id, oldest_version)
            
            create_alias_from_draft(client, agent_id, int(latest_version_number) + 1)
        else:
            print(
                "Draft is not newer than the latest published version. No alias update performed."
            )

    elif draft and not latest_numeric:
        print("Draft exists and no numeric published versions found.")
        create_alias_from_draft(client, agent_id, 1)

    else:
        print("No draft version found.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Create a new alias for a Bedrock agent version if draft is newer than the latest published version."
    )
    parser.add_argument("--agent_id", help="The Bedrock Agent Id")
    parser.add_argument(
        "--region", default="us-east-1", help="AWS region (default: us-east-1)"
    )
    args = parser.parse_args()

    try:
        create_new_alias_if_draft_updated(args.agent_id, args.region)
    except Exception as e:
        print(f"An error occurred: {e}")
        sys.exit(1)

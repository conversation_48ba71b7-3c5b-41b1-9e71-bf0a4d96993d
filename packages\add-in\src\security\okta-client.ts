import { OktaAuth } from '@okta/okta-auth-js';
import { okta } from '../config';

// hack whilst sharing internal modelling infra
const redirectUri = `${window.location.origin}${window.location.pathname.includes("wmlgv-assistant") ? "/wmlgv-assistant" : ""}/login/callback`;

const oktaClient = new OktaAuth({
    ...okta,
    redirectUri,
    scopes: ['openid'],
    pkce: true,
});

const getAccessToken = async (): Promise<string> => {
    const { accessToken } = await oktaClient.tokenManager.getTokens();
    let bearer;

    if (accessToken) {
        const expired = await oktaClient.tokenManager.hasExpired(accessToken);
        let renewed;

        if (expired) {
            renewed = await oktaClient.tokenManager.renew('accessToken');
        }

        bearer = renewed ? renewed.accessToken : accessToken.accessToken;
    }

    return bearer;
};

export {
    oktaClient, getAccessToken
}
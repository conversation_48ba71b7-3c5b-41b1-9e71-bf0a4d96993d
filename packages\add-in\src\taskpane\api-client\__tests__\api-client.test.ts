import { apiClient } from "../api-client";
import { getAccessToken } from "../../../security";

jest.mock("../../../security", () => ({
  getAccessToken: jest.fn(),
}));

jest.mock("../../../config", () => ({
  apiClient: {
    baseUrl: "https://api.test.com",
  },
}));

const mockGetAccessToken = getAccessToken as jest.MockedFunction<typeof getAccessToken>;

describe("api-client", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("exports an axios instance", () => {
    expect(apiClient).toBeDefined();
    expect(typeof apiClient.get).toBe("function");
    expect(typeof apiClient.post).toBe("function");
    expect(typeof apiClient.put).toBe("function");
    expect(typeof apiClient.delete).toBe("function");
  });

  it("has correct base URL", () => {
    expect(apiClient.defaults.baseURL).toBe("https://api.test.com");
  });

  it("has param serializer configured", () => {
    expect(apiClient.defaults.paramsSerializer).toBeDefined();
  });

  it("encodes URL parameters correctly", () => {
    const encoder = (apiClient.defaults.paramsSerializer as any).encode;
    expect(encoder("test param")).toBe("test%20param");
    expect(encoder("special@char")).toBe("special%40char");
    expect(encoder("normal")).toBe("normal");
  });

  it("has request interceptor for authorization", () => {
    expect((apiClient.interceptors.request as any).handlers).toHaveLength(1);
  });

  describe("authorization header interceptor", () => {
    it("adds authorization header when access token is available", async () => {
      mockGetAccessToken.mockResolvedValue("test-token-123");

      const mockConfig = {
        url: "/test",
        method: "get",
        headers: {
          "Content-Type": "application/json",
        },
      };

      // Get the interceptor function
      const interceptor = (apiClient.interceptors.request as any).handlers[0].fulfilled;

      // Call the interceptor with the mock config
      const result = await interceptor(mockConfig);

      expect(mockGetAccessToken).toHaveBeenCalled();
      expect(result).toEqual({
        ...mockConfig,
        headers: {
          "Content-Type": "application/json",
          authorization: "Bearer test-token-123",
        },
      });
    });

    it("does not add authorization header when access token is empty", async () => {
      mockGetAccessToken.mockResolvedValue("");

      const mockConfig = {
        url: "/test",
        method: "get",
        headers: {
          "Content-Type": "application/json",
        },
      };

      // Get the interceptor function
      const interceptor = (apiClient.interceptors.request as any).handlers[0].fulfilled;

      // Call the interceptor with the mock config
      const result = await interceptor(mockConfig);

      expect(mockGetAccessToken).toHaveBeenCalled();
      expect(result).toEqual({
        ...mockConfig,
        headers: {
          "Content-Type": "application/json",
        },
      });
    });

    it("handles config without existing headers", async () => {
      mockGetAccessToken.mockResolvedValue("another-token");

      const mockConfig = {
        url: "/test",
        method: "get",
      };

      // Get the interceptor function
      const interceptor = (apiClient.interceptors.request as any).handlers[0].fulfilled;

      // Call the interceptor with the mock config
      const result = await interceptor(mockConfig);

      expect(mockGetAccessToken).toHaveBeenCalled();
      expect(result).toEqual({
        ...mockConfig,
        headers: {
          authorization: "Bearer another-token",
        },
      });
    });

    it("handles null access token", async () => {
      mockGetAccessToken.mockResolvedValue(null as any);

      const mockConfig = {
        url: "/test",
        method: "get",
        headers: {
          "Content-Type": "application/json",
        },
      };

      // Get the interceptor function
      const interceptor = (apiClient.interceptors.request as any).handlers[0].fulfilled;

      // Call the interceptor with the mock config
      const result = await interceptor(mockConfig);

      expect(mockGetAccessToken).toHaveBeenCalled();
      expect(result).toEqual({
        ...mockConfig,
        headers: {
          "Content-Type": "application/json",
        },
      });
    });
  });
});

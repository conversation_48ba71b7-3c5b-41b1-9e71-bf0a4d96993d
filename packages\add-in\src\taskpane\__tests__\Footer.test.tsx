import React from "react";
import { render, screen } from "@testing-library/react";
import { Footer } from "../Footer";

jest.mock("../use-styles", () => ({
    useFooterStyles: () => ({
        footer: "mock-footer-class",
        link: "mock-link-class",
    }),
}));

describe("Footer", () => {
    it("renders the footer container with correct class", () => {
        render(<Footer />);
        const footerDiv = screen.getByText(/Conditions of use/i).closest("div");
        expect(footerDiv).toHaveClass("mock-footer-class");
    });

    it("renders both footer links with correct text", () => {
        render(<Footer />);
        expect(screen.getByText("Conditions of use")).toBeInTheDocument();
        expect(screen.getByText("Privacy policy")).toBeInTheDocument();
    });

    it("renders both links with correct hrefs and attributes", () => {
        render(<Footer />);
        const links = screen.getAllByRole("link");
        expect(links).toHaveLength(2);

        expect(links[0]).toHaveAttribute("href", "https://www.woodmac.com/conditions-of-use/");
        expect(links[0]).toHaveAttribute("target", "_blank");
        expect(links[0]).toHaveAttribute("rel", "noopener noreferrer");
        expect(links[0]).toHaveClass("mock-link-class");

        expect(links[1]).toHaveAttribute("href", "https://www.woodmac.com/privacy-policy-centre/");
        expect(links[1]).toHaveAttribute("target", "_blank");
        expect(links[1]).toHaveAttribute("rel", "noopener noreferrer");
        expect(links[1]).toHaveClass("mock-link-class");
    });

    it("renders a separator '|' between the links", () => {
        render(<Footer />);
        const footerDiv = screen.getByText(/Conditions of use/i).closest("div");
        expect(footerDiv?.textContent).toContain("|");
    });
});
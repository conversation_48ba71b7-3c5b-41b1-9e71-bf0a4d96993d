{
    "folders": [
        {
            "name": "Root",
            "path": "."
        },
        {
            "name": "Agent",
            "path": "./packages/agent"
        },
        {
            "name": "Data Ingestion Utils",
            "path": "./packages/data-ingestion-utils"
        },
        {
            "name": "E2E Tests",
            "path": "./packages/e2e"
        },
        {
            "name": "Orchestrator",
            "path": "./packages/orchestrator"
        },
        {
            "name": "Add-in",
            "path": "./packages/add-in"
        }
    ],
    "settings": {
        "python.formatting.provider": "black",
        "python.linting.enabled": true,
        "python.linting.flake8Enabled": true,
        "python.testing.pytestEnabled": true,
        "python.testing.pytestArgs": ["."],
        "typescript.preferences.includePackageJsonAutoImports": "on",
        "typescript.suggest.autoImports": true,
        "typescript.updateImportsOnFileMove.enabled": "always",
        "eslint.workingDirectories": [
            "./packages/add-in",
            "./packages/orchestrator",
            "./packages/e2e"
        ],
        "jest.disabledWorkspaceFolders": [
            "Root",
            "Agent",
            "Data Ingestion Utils",
            "E2E Tests",
        ],
        "files.exclude": {
            "**/__pycache__": true,
            "**/*.pyc": true,
            "**/node_modules": true,
            "**/dist": true,
            "**/build": true,
            "**/.venv": true,
            "**/coverage": true,
            "**/test-results": true,
            "**/playwright-report": true
        },
        "[python]": {
            "editor.formatOnSave": true,
            "editor.defaultFormatter": "ms-python.black-formatter"
        },
        "[typescript]": {
            "editor.formatOnSave": true,
            "editor.defaultFormatter": "esbenp.prettier-vscode",
            "editor.codeActionsOnSave": {
                "source.organizeImports": "explicit"
            }
        },
        "[javascript]": {
            "editor.formatOnSave": true,
            "editor.defaultFormatter": "esbenp.prettier-vscode",
            "editor.codeActionsOnSave": {
                "source.organizeImports": "explicit"
            }
        }
    },
    "extensions": {
        "recommendations": [
            "ms-python.python",
            "ms-python.black-formatter",
            "ms-python.flake8",
            "esbenp.prettier-vscode",
            "ms-playwright.playwright"
        ]
    }
}

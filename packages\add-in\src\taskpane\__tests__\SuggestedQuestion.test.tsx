import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { SuggestedQuestion } from "../SuggestedQuestion";

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("SuggestedQuestion", () => {
  const mockOnSuggestedClicked = jest.fn();
  const testQuestion = "What is the best valuation method?";

  beforeEach(() => {
    mockOnSuggestedClicked.mockClear();
  });

  it("renders the question text", () => {
    renderWithProvider(<SuggestedQuestion question={testQuestion} onSuggestedClicked={mockOnSuggestedClicked} />);

    expect(screen.getByText(testQuestion)).toBeInTheDocument();
  });

  it("calls onSuggestedClicked when button is clicked", () => {
    renderWithProvider(<SuggestedQuestion question={testQuestion} onSuggestedClicked={mockOnSuggestedClicked} />);

    const button = screen.getByRole("button", { name: testQuestion });
    fireEvent.click(button);

    expect(mockOnSuggestedClicked).toHaveBeenCalledTimes(1);
    expect(mockOnSuggestedClicked).toHaveBeenCalledWith(testQuestion);
  });

  it("renders as a small button", () => {
    renderWithProvider(<SuggestedQuestion question={testQuestion} onSuggestedClicked={mockOnSuggestedClicked} />);

    const button = screen.getByRole("button", { name: testQuestion });
    expect(button).toBeInTheDocument();
  });
});

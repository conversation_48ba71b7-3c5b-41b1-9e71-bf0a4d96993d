import React from "react";
import { render, screen } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { PreviewTable } from "../PreviewTable";

const mockTableData = [
  ["Name", "Age", "City", "Country", "Extra Column"],
  ["<PERSON>", 30, "New York", "USA", "Extra Data"],
  ["Jane", 25, "London", "UK", "More Data"],
  ["Bob", 35, "Paris", "France", "Additional Info"],
];

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("PreviewTable", () => {
  it("renders table with correct headers", () => {
    renderWithProvider(<PreviewTable tableData={mockTableData} />);

    expect(screen.getByText("Name")).toBeInTheDocument();
    expect(screen.getByText("Age")).toBeInTheDocument();
    expect(screen.getByText("City")).toBeInTheDocument();
    expect(screen.getByText("Country")).toBeInTheDocument();
  });

  it("renders table data correctly", () => {
    renderWithProvider(<PreviewTable tableData={mockTableData} />);

    expect(screen.getByText("John")).toBeInTheDocument();
    expect(screen.getByText("30")).toBeInTheDocument();
    expect(screen.getByText("Jane")).toBeInTheDocument();
    expect(screen.getByText("25")).toBeInTheDocument();
  });

  it("shows ellipsis when more than 4 columns", () => {
    renderWithProvider(<PreviewTable tableData={mockTableData} />);

    expect(screen.getAllByText("...")).toHaveLength(4);
  });

  it("shows column limit note when more than 4 columns", () => {
    renderWithProvider(<PreviewTable tableData={mockTableData} />);

    expect(screen.getByText("Note: Showing only first 4 columns")).toBeInTheDocument();
  });

  it("shows row limit note when more than 10 rows", () => {
    const largeTableData = [
      ["Header1", "Header2"],
      ...Array.from({ length: 12 }, (_, i) => [`Row${i + 1}`, `Data${i + 1}`]),
    ];

    renderWithProvider(<PreviewTable tableData={largeTableData} />);

    expect(screen.getByText("Note: Showing only first 10 rows")).toBeInTheDocument();
  });

  it("does not show notes for small tables", () => {
    const smallTableData = [
      ["Name", "Age"],
      ["John", 30],
      ["Jane", 25],
    ];

    renderWithProvider(<PreviewTable tableData={smallTableData} />);

    expect(screen.queryByText(/Note:/)).not.toBeInTheDocument();
  });
});

import * as React from 'react';
import { ReactNode } from 'react';
import { Login } from '../taskpane/Login';
import { oktaClient } from './okta-client';

const Secured = ({ children }: { children: ReactNode }) => {
    const authState = oktaClient.authStateManager.getAuthState()

    const  [contentVisible, setContentVisible] = React.useState(!!authState?.isAuthenticated)
    const contentVisibleChanged = state => setContentVisible(state.isAuthenticated);

    React.useEffect(() => {
        oktaClient.authStateManager.subscribe(contentVisibleChanged);

        return () => {
                oktaClient.authStateManager.unsubscribe(contentVisibleChanged);
        };
    }, [contentVisibleChanged, oktaClient]);

    return <>{ contentVisible ? children : <Login />} </>;
};

export { Secured };
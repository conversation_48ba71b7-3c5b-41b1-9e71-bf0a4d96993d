# WMLGV Assistant Agent Version Updater

Utility for managing AWS Bedrock agent versions and aliases.

## Overview

This package provides functionality to automatically create new agent aliases when the draft version of a Bedrock agent has been updated more recently than the latest published version.

## Installation

```bash
pip install -e .
```

## Development Installation

```bash
pip install -e ".[dev]"
```

## Usage

```bash
python agent_version_updater.py --agent_id YOUR_AGENT_ID --region us-east-1
```

## Testing

```bash
pytest
```

## Code Formatting

```bash
black .
```

## Type Checking

```bash
mypy .
```

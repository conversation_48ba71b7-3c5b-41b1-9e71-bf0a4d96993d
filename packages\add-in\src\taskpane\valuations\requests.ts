import { apiClient } from "../api-client";
import { AvailableAlias } from "./valuations-types";

const askBot = async (prompt: string, sessionId: string, versionAlias?: string) => {
  const url = "/ask-chatbot";

  const { data } = await apiClient.get(url, {
    params: {
      prompt,
      sessionId,
      versionAlias,
    },
  });

  const { table, answer } = data;

  return { table, answer };
};

const getAvailableAliases = async (): Promise<AvailableAlias[]> => {
  const { data: { versionAliases } } = await apiClient.get("/valuations-agent/aliases");

  return versionAliases;
}

export { askBot, getAvailableAliases };

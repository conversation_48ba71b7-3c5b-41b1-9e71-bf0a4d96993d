import React from 'react';
import { Button } from '@fluentui/react-components';
import { AddRegular } from '@fluentui/react-icons';
import { useStyles } from '../use-styles';
import { useValuationsAgent } from './use-valuations-agent';
import { TableData } from '../chat-types';
import { Agent } from '../Agent';
import { AgentAliasSelector } from './AgentAliasSelector';

const ValuationsAgent = () => {
    const { buttonIcon } = useStyles();
    const { chatHistory, 
        loading, 
        addTableToExcel, 
        handleChatMessageEntered, 
        showSuggestedQuestions, 
        suggestedQuestions, 
        availableAliases, 
        selectedAlias,  
        onSetSelectedAlias,
    } = useValuationsAgent();
    
    return (
        <Agent
            header={<AgentAliasSelector 
                availableAliases={availableAliases} 
                selectedAlias={selectedAlias} 
                onSetSelectedAlias={onSetSelectedAlias}
            />}
            howCanIHelpMessage="How can I help you with Valuations?"
            chatHistory={chatHistory}
            loading={loading}
            handleChatMessageEntered={handleChatMessageEntered}
            showSuggestedQuestions={showSuggestedQuestions}
            suggestedQuestions={suggestedQuestions}
            botMessageFooterComponentsCreator={table => [table && [
                <Button onClick={() => addTableToExcel(table as TableData)}><AddRegular className={buttonIcon} /> Add to a new sheet</Button>, 
                <Button onClick={() => addTableToExcel(table as TableData, true)}><AddRegular className={buttonIcon} /> Add chart to a new sheet</Button>
            ]].flat().filter(Boolean)}
        />
    );
};

export { ValuationsAgent };

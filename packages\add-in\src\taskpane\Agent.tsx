import React from "react";
import { useStyles } from "./use-styles";
import { BotMessage } from "./BotMessage";
import { UserMessage } from "./UserMessage";
import { ScrollToBottom } from "./ScrollToBottom";
import { ChatEntry } from "./ChatEntry";
import { SuggestedQuestions } from "./SuggestedQuestions";
import { ChatMessage } from "./chat-types";

type AgentProps = {
    howCanIHelpMessage: string;
    chatHistory: ChatMessage[];
    loading: boolean;
    currentMessage?: string;
    handleChatMessageEntered: (message: string) => void;
    showSuggestedQuestions?: boolean;
    suggestedQuestions?: string[];
    botMessageFooterComponentsCreator: (additionalData: any) => React.ReactNode[];
    header?: React.ReactNode;
};

const Agent = ({
    howCanIHelpMessage,
    chatHistory, 
    loading, 
    currentMessage, 
    handleChatMessageEntered,
    showSuggestedQuestions,
    suggestedQuestions,
    botMessageFooterComponentsCreator,
    header,
 }: AgentProps) => {
    const { messageArea } = useStyles();
    
    return (
        <>
            <div className={messageArea}>
                {header}
                <BotMessage message={howCanIHelpMessage} hideAiWarning />
                <SuggestedQuestions
                    showSuggestedQuestions={showSuggestedQuestions} 
                    suggestedQuestions={suggestedQuestions} 
                    handleChatMessageEntered={handleChatMessageEntered}
                />
                {
                    chatHistory.map(({ message, role, additionalData }, index) => role === "assistant" 
                        ? <BotMessage key={index} tableData={typeof additionalData !== "string" ? additionalData : null} message={message} footerComponents={botMessageFooterComponentsCreator(additionalData)} /> 
                        : <UserMessage key={index} message={message} />
                    )
                }
                {loading && (
                    <BotMessage message={currentMessage} loading hideAiWarning/>
                )}
                <ScrollToBottom trigger={`${chatHistory.length}_${currentMessage}`} />
            </div>
            <ChatEntry onChatMessageEntered={handleChatMessageEntered} />
        </>
    );
};

export { Agent };

import { askBot, getAvailableAliases } from "../requests";
import { apiClient } from "../../api-client";

jest.mock("../../api-client", () => ({
  apiClient: {
    get: jest.fn(),
  },
}));

const mockApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe("valuations requests", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("askBot", () => {
    it("makes API call with correct parameters", async () => {
      const mockResponse = {
        data: {
          table: "mock-table-data",
          answer: "mock-answer",
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await askBot("What is the value?", "session-123");

      expect(mockApiClient.get).toHaveBeenCalledWith("/ask-chatbot", {
        params: {
          prompt: "What is the value?",
          sessionId: "session-123",
        },
      });

      expect(result).toEqual({
        table: "mock-table-data",
        answer: "mock-answer",
      });
    });

    it("passes the agent alias id if provided", async () => {
      const mockResponse = {
        data: {
          table: "mock-table-data",
          answer: "mock-answer",
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await askBot("What is the value?", "session-123", "v1");

      expect(mockApiClient.get).toHaveBeenCalledWith("/ask-chatbot", {
        params: {
          prompt: "What is the value?",
          sessionId: "session-123",
          versionAlias: "v1",
        },
      });

      expect(result).toEqual({
        table: "mock-table-data",
        answer: "mock-answer",
      });
    });

    it("returns table and answer from response data", async () => {
      const mockResponse = {
        data: {
          table: "|Name|Value|\n|Asset A|100|",
          answer: "Here is the data you requested",
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await askBot("Show me assets", "session-456");

      expect(result.table).toBe("|Name|Value|\n|Asset A|100|");
      expect(result.answer).toBe("Here is the data you requested");
    });

    it("handles response with null values", async () => {
      const mockResponse = {
        data: {
          table: null,
          answer: null,
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await askBot("Unknown query", "session-789");

      expect(result.table).toBeNull();
      expect(result.answer).toBeNull();
    });

    it("propagates API errors", async () => {
      const apiError = new Error("Network error");
      mockApiClient.get.mockRejectedValue(apiError);

      await expect(askBot("Test prompt", "session-error")).rejects.toThrow("Network error");

      expect(mockApiClient.get).toHaveBeenCalledWith("/ask-chatbot", {
        params: {
          prompt: "Test prompt",
          sessionId: "session-error",
        },
      });
    });

    it("handles empty prompt and sessionId", async () => {
      const mockResponse = {
        data: {
          table: null,
          answer: "Please provide a valid question",
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await askBot("", "");

      expect(mockApiClient.get).toHaveBeenCalledWith("/ask-chatbot", {
        params: {
          prompt: "",
          sessionId: "",
        },
      });

      expect(result.answer).toBe("Please provide a valid question");
    });
  });

  describe("getAvailableAliases", () => {
    it("fetches available aliases from API", async () => {
      const mockResponse = {
        data: {
          versionAliases: [
            { agentAliasId: "v1", agentAliasName: "Version 1" },
            { agentAliasId: "v2", agentAliasName: "Version 2" },
          ],
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await getAvailableAliases();

      expect(mockApiClient.get).toHaveBeenCalledWith("/valuations-agent/aliases");
      expect(result).toEqual(mockResponse.data.versionAliases);
    });

    it("returns empty array if no aliases are available", async () => {
      const mockResponse = {
        data: {
          versionAliases: [],
        },
      };

      mockApiClient.get.mockResolvedValue(mockResponse);

      const result = await getAvailableAliases();

      expect(result).toEqual([]);
    });

    it("handles API errors gracefully", async () => {
      const apiError = new Error("Network error");
      mockApiClient.get.mockRejectedValue(apiError);

      await expect(getAvailableAliases()).rejects.toThrow("Network error");
    });
  });
});

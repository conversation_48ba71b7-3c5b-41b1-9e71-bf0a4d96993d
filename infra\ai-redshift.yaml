#Not used in the project, but one day we might go back to this and use it
# This is a CloudFormation template for creating a Redshift Serverless database
AWSTemplateFormatVersion: '2010-09-09'
Description: Redshift Serverless Database for Woodmac Assistant - Valuations

Parameters:
  Environment:
    Description: The environment in which the stack is deployed
    Type: String
    Default: aidev
    AllowedValues: [aidev, aiint, aiuat, aiprod]

  BusinessUnit:
    Type: String
    Default: Woodmac

  Contact:
    Description: Contact for stack owner(s)
    Type: String
    Default: '<EMAIL>'
    ConstraintDescription: Must be a valid email address.

  ProductCode:
    Description: Product code
    Type: String
    Default: 'wmlgv'

  ProjectCode:
    Description: Project code
    Type: String
    Default: 'DEV-VAL-SERV'

  Application:
    Description: Application code
    Type: String
    Default: 'valuations-ai'

  BaseCapacity:
    Description: Base RPUs for Redshift Serverless
    Type: Number
    Default: 128
    AllowedValues: [32, 64, 128, 256]

Resources:
  CustomMapping:
    Type: Custom::Lookup
    Properties:
      ServiceToken: !Join
        - ':'
        - - 'arn:aws:lambda'
          - !Ref 'AWS::Region'
          - !Ref 'AWS::AccountId'
          - 'function:liveservices-environment-mappings-lambda'
      environment: !Ref Environment
      region: !Ref 'AWS::Region'

  RedshiftNamespace:
    Type: AWS::RedshiftServerless::Namespace
    Properties:
      NamespaceName: !Sub ${ProductCode}-${Environment}-woodmac-assistant-valuations-namespace
      DbName: !Sub ${ProductCode}-${Environment}-woodmac-assistant-valuations-database
      DefaultIamRoleArn: 
        Fn::Sub:
          - arn:aws:iam::${AWS::AccountId}:role/wmlgv-assistant-redshift-${environment}-role
          - environment:
              Ref: Environment
      IamRoles: 
        - Fn::Sub:
          - arn:aws:iam::${AWS::AccountId}:role/wmlgv-assistant-redshift-${environment}-role
          - environment:
              Ref: Environment
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: Application
          Value: !Ref Application

  RedshiftWorkgroup:
    Type: AWS::RedshiftServerless::Workgroup
    DependsOn:
      - RedshiftNamespace
    Properties:
      WorkgroupName: !Sub ${ProductCode}-${Environment}-woodmac-assistant-valuations-workgroup
      NamespaceName: !Sub ${ProductCode}-${Environment}-woodmac-assistant-valuations-namespace
      BaseCapacity: !Ref BaseCapacity
      PubliclyAccessible: false
      SecurityGroupIds: 
        - !Ref RedshiftSecurityGroup
      SubnetIds: 
        - !GetAtt CustomMapping.privateWebSubnet1 # 3 subnets are needed in 3 different AZs for redshift
        - !GetAtt CustomMapping.privateWebSubnet2
        - !GetAtt CustomMapping.privateWebSubnet3
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: Application
          Value: !Ref Application

  RedshiftSecurityGroup:
    Type: AWS::EC2::SecurityGroup
    Properties:
      GroupDescription: Security group for Redshift Serverless
      VpcId: !GetAtt CustomMapping.vpc
      SecurityGroupIngress:
        - IpProtocol: tcp
          FromPort: 5439
          ToPort: 5439
          CidrIp: 10.0.0.0/8
          Description: Allow Redshift access
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: Application
          Value: !Ref Application

Outputs:  
  RedshiftWorkgroupArn:
    Description: ARN of the Redshift Serverless Workgroup
    Value: !GetAtt RedshiftWorkgroup.Workgroup.WorkgroupArn
    Export:
      Name: !Sub "${ProductCode}-${Environment}-redshift-workgroup-arn"
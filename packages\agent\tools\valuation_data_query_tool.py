import os
import polars as pl
import json
import csv


class Tools:
    def __init__(self, use_local_files: bool = False):
        # Set the default number of rows and columns to display when printing a DataFrame
        pl.Config(tbl_rows=25, tbl_cols=25)
        self.definitions = self._load_definitions()

        if use_local_files:
            self.company_asset_discrete_table = pl.read_csv(
                "exxon_global_asset_request.csv", infer_schema_length=10000
            )
            self.asset_discrete_table = pl.read_csv(
                "uk_canada_assets.csv", infer_schema_length=10000
            )
            self.company_asset_timeseries_table = pl.read_csv(
                "exxon_global_asset_timeseries.csv", infer_schema_length=10000
            )
            self.asset_timeseries_table = pl.read_csv(
                "uk_canada_assets_timeseries.csv", infer_schema_length=10000
            )
        else:
            environment = os.getenv("ENVIRONMENT", "aidev")
            base_s3_path = f"s3://wmlgv-{environment}-assistant-valuations-data/parquet"
            storage_options = {"region": "us-east-1"}

            self.company_asset_discrete_table = pl.scan_parquet(
                f"{base_s3_path}/all_upstream_companies_discrete_combined.parquet",
                storage_options=storage_options,
            )
            self.asset_discrete_table = pl.scan_parquet(
                f"{base_s3_path}/all_upstream_assets_discrete_combined.parquet",
                storage_options=storage_options,
            )
            self.company_asset_timeseries_table = pl.scan_parquet(
                f"{base_s3_path}/all_upstream_companies_timeseries_combined.parquet",
                storage_options=storage_options,
            )
            self.asset_timeseries_table = pl.scan_parquet(
                f"{base_s3_path}/all_upstream_assets_timeseries_combined.parquet",
                storage_options=storage_options,
            )

    def _load_definitions(self) -> dict:
        """Load metric definitions from CSV file"""
        definitions = {}
        try:
            csv_path = os.path.join(
                os.path.dirname(__file__), "data_definitions.csv"
            )
            with open(csv_path, "r", encoding="utf-8-sig") as file:
                reader = csv.DictReader(file)
                for row in reader:
                    metric_key = row.get("Metric", "")
                    definition_key = row.get("Definition") or row.get("Description", "")
                    if metric_key and definition_key:
                        definitions[metric_key.strip()] = definition_key.strip()
        except Exception as e:
            print(f"Warning: Could not load definitions from CSV: {e}")
        return definitions

    def get_relevant_definitions(self, query_text: str) -> str:
        """Get relevant definitions based on query text"""
        if not self.definitions:
            return ""

        relevant_defs = []
        query_lower = query_text.lower()

        for metric, definition in self.definitions.items():
            metric_lower = metric.lower()
            if (
                metric_lower in query_lower
                or any(word in query_lower for word in metric_lower.split())
                or any(
                    word in definition.lower()
                    for word in query_lower.split()
                    if len(word) > 3
                )
            ):
                relevant_defs.append(f"**{metric}**: {definition}")

        if relevant_defs:
            return "\n\t### Relevant Metric Definitions:\n" + "\n\t".join(
                relevant_defs[:8]
            )
        return ""

    def get_company_asset_discrete_schema(self) -> str:
        """
        Returns the schema of the company discrete metrics table. Refer to this schema to understand the structure
        of the table and what columns it has.
        """
        example = self.company_asset_discrete_table.head(1).collect().write_json()
        definitions = self.get_relevant_definitions("company asset discrete")
        schema_info = f"""
        ### Schema Information
        The table name is company_asset_discrete_table. You must never request more than 25 rows and 25 columns. 
        This table contains discrete metrics for company assets with the following schema: {self.company_asset_discrete_table.collect_schema()}.

        {definitions}

        ### Example structure
        {example}
        """

        return schema_info

    def get_asset_discrete_schema(self) -> str:
        """
        Returns the schema of the asset discrete metrics table. Refer to this schema to understand the structure
        of the table and what columns it has.
        """
        example = self.asset_discrete_table.head(1).collect().write_json()
        definitions = self.get_relevant_definitions("asset discrete")
        schema_info = f"""
        ### Schema Information
        The table name is asset_discrete_table. You must never request more than 25 rows and 25 columns. 
        This table contains discrete metrics for assets with the following schema: {self.asset_discrete_table.collect_schema()}.
        
        {definitions}

        ### Example structure
        {example}
        """

        return schema_info

    def get_company_asset_timeseries_schema(self) -> str:
        """
        Returns the schema of the company timeseries metrics table. Refer to this schema to understand the structure
        of the table and what columns it has.
        """
        example = self.company_asset_timeseries_table.head(1).collect().write_json()
        schema_info = f"""
        ### Schema Information
        The table name is company_asset_timeseries_table. You must never request more than 100 rows and 10 columns. 
        This table contains timeseries metrics for company assets with the following schema: {self.company_asset_timeseries_table.collect_schema()}.

        ### Example structure
        {example}
        """

        # Add relevant definitions
        definitions = self.get_relevant_definitions("company asset timeseries")
        return schema_info + definitions

    def get_asset_timeseries_schema(self) -> str:
        """
        Returns the schema of the asset timeseries metrics table. Refer to this schema to understand the structure
        of the table and what columns it has.
        """
        example = self.asset_timeseries_table.head(1).collect().write_json()
        schema_info = f"""
        ### Schema Information
        The table name is asset_timeseries_table. You must never request more than 100 rows and 10 columns. 
        This table contains timeseries metrics for assets with the following schema: {self.asset_timeseries_table.collect_schema()}.

        ### Example structure
        {example}
        """

        # Add relevant definitions
        definitions = self.get_relevant_definitions("asset timeseries")
        return schema_info + definitions

    def query_dataset(self, sql_query: str) -> str:
        """
        Query the dataset. The query should be a valid SQL query. It must only read the table and never make modifications to it.

        Available tables:
        - company_asset_discrete_table: Discrete metrics for company assets
        - asset_discrete_table: Discrete metrics for non-company assets
        - company_asset_timeseries_table: Timeseries metrics for company assets
        - asset_timeseries_table: Timeseries metrics for non-company assets

        The tables contain 3 variants of each asset based on price_scenario: LOW, BASE, HIGH.
        If scenario is not specified in the query then use BASE as default.

        Use the schema methods to understand the structure of each table.

        :param sql_query: a query to run on the dataset
        """

        try:
            # These variables are used in the query FROM statement
            company_asset_discrete_table = self.company_asset_discrete_table  # noqa: F841
            asset_discrete_table = self.asset_discrete_table  # noqa: F841
            company_asset_timeseries_table = self.company_asset_timeseries_table  # noqa: F841
            asset_timeseries_table = self.asset_timeseries_table  # noqa: F841

            result = pl.sql(sql_query).collect().write_json()
            return {"status": "ok", "result": json.loads(result)}
        except Exception as e:
            return {"status": "Query failed", "error": str(e)}


if __name__ == "__main__":
    from dotenv import load_dotenv

    load_dotenv()

    tools = Tools()
    print("=======company asset discrete metrics===========")
    print(tools.get_company_asset_discrete_schema())
    print("======company asset timeseries metrics==========")
    print(tools.get_company_asset_timeseries_schema())
    print("===========asset discrete metrics===============")
    print(tools.get_asset_discrete_schema())
    print("==========asset timeseries metrics==============")
    print(tools.get_asset_timeseries_schema())
    print("=================Example Query==================")
    print(
        tools.query_dataset("""
            SELECT asset_name, gross_revenue FROM asset_timeseries_table WHERE price_scenario = 'BASE' AND asset_name = 'Thunder Horse' AND year >= 2020 AND year <= 2025
        """)
    )
    # print(
    #     tools.query_dataset("""
    #         WITH top_assets AS (
    #             SELECT asset_name, SUM(cash_flow) AS total_cash_flow
    #             FROM company_asset_timeseries_table
    #             WHERE company = 'BP' AND country = 'United Kingdom' AND price_scenario = 'BASE' AND year BETWEEN 2025 AND 2040
    #             GROUP BY asset_name
    #             ORDER BY total_cash_flow DESC
    #             LIMIT 5
    #         )
    #         SELECT t.asset_name, t.year, t.cash_flow, t.gross_revenue
    #         FROM company_asset_timeseries_table t
    #         JOIN top_assets a ON t.asset_name = a.asset_name
    #         WHERE t.company = 'BP' AND t.country = 'United Kingdom' AND t.price_scenario = 'BASE' AND t.year BETWEEN 2025 AND 2040
    #         ORDER BY t.asset_name, t.year
    #     """)
    # )

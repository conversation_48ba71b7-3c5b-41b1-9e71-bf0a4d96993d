import { test, expect } from '@playwright/test';
import login from './actions/login';

test.setTimeout(5 * 60 * 1000);

test('Contains our workflows', async ({ page }) => {
    await login({ page }); // need a nicer way of getting the auth token
    await page.goto(
        'https://lens-internal-modelling-dev.woodmac.com/wmlgv-assistant/taskpane.html',
    );

    // Expect a title "to contain" a substring.
    await expect(page).toHaveTitle(/Woodmac Assistant Add-in/);
    await expect(
        page.getByText(
            "Welcome, where would you like to start today?",
        ),
    ).toBeVisible();
    await expect(
        page.getByRole('button', { name: 'Valuations Calculate' }),
    ).toBeVisible();
    await expect(
        page.getByRole('button', { name: 'Lens Direct Explore and Query' }),
    ).toBeVisible();
    await expect(
        page.getByRole('button', { name: 'Written Content Find and' }),
    ).toBeVisible();
    await expect(page.getByText('Note:')).toBeVisible();
    await expect(page.getByText('for written content, place')).toBeVisible();
    await expect(page.getByText('for lens direct, place lens')).toBeVisible();

    // check the valuations workflow
    await page.getByRole('button', { name: 'Valuations Calculate' }).click();
    await expect(
        page.getByRole('group').filter({ hasText: 'How can I help you with' }),
    ).toBeVisible();
    await expect(
        page.getByRole('textbox', { name: 'ask question here' }),
    ).toBeVisible();
    await page
        .getByRole('textbox', { name: 'ask question here' })
        .fill("what's the value of asset Clair? Output result as a table.");
    await page.keyboard.press('Enter');
    
    const slowExpect = expect.configure({ timeout: 30 * 1000 });
    await slowExpect(
        page.getByRole('button', { name: 'Add data to a new worksheet' }),
    ).toBeVisible();
    await expect(
        page.getByRole('button', { name: 'Add chart to a new worksheet' }),
    ).toBeVisible();
    await expect(page.getByText('AI generated content may be')).toBeVisible();
});

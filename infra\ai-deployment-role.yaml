AWSTemplateFormatVersion: '2010-09-09'
Description: Jenkins Agent Role Setup
Parameters:
  Environment:
    Description: The environment in which the stack is deployed
    Type: String
    Default: aidev
    AllowedValues: [aidev, aiint, aiuat, aiprod]
  PermissionsBoundaryPolicy:
    Type: String
    Default: VA-PB-Standard
  BaseJenkinsAgentPolicyArn:
    Description: "The Arn of the base jenkins agent policy arn fetched from parameter store"
    Type: "AWS::SSM::Parameter::Value<String>"
    Default: "/aidev/liveservices/jenkins-ecs-agents/base-jenkins-agent-policy-arn"

Resources:
  CustomMapping:
    Type: Custom::Lookup
    Properties:
      ServiceToken: !Join
        - ':'
        - - 'arn:aws:lambda'
          - !Ref 'AWS::Region'
          - !Ref 'AWS::AccountId'
          - 'function:liveservices-environment-mappings-lambda'
      environment: !Ref Environment
      region: !Ref 'AWS::Region'

  # Role to create new roles
  AgentRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: 'ecs-tasks.amazonaws.com'
            Action: ['sts:AssumeRole']
      Path: /
      ManagedPolicyArns:
        - !Ref BaseJenkinsAgentPolicyArn
      PermissionsBoundary: !Sub 'arn:aws:iam::${AWS::AccountId}:policy/${PermissionsBoundaryPolicy}'
      Policies:
        - PolicyName: !Sub 'wmlgv-assistant-deploy-policy-${Environment}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow #To Remove just for POC
                Action:
                  - iam:AttachRolePolicy
                  - iam:DetachRolePolicy
                Resource:
                  - arn:aws:iam::aws:policy/AmazonS3FullAccess
                  - arn:aws:iam::aws:policy/AmazonKinesisFirehoseFullAccess
                  - arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess
              - Effect: Allow
                Action:
                  - s3:*
                Resource: #Todo: tighten this up to only allow the specific buckets later
                  - arn:aws:s3:::wmlgv*
                  - arn:aws:s3:::wmlgv-*/*
              - Effect: Allow
                Action:
                  - cloudformation:Create*
                  - cloudformation:Describe*
                  - cloudformation:Execute*
                  - cloudformation:Get*
                Resource:
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/wmlgv-assistant-*/*'
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stackset/wmlgv-assistant-*/*'
              - Effect: Allow
                Action:
                  - ec2:Describe*
                  - ec2:CreateSecurityGroup
                  - ec2:DeleteSecurityGroup
                  - ec2:AuthorizeSecurityGroupIngress
                  - ec2:RevokeSecurityGroupIngress
                  - ec2:createTags
                Resource: '*'
              - Effect: Allow
                Action:
                  - lambda:*
                Resource:
                  - !Sub 'arn:aws:lambda:*:${AWS::AccountId}:function:liveservices-environment-mappings-lambda'
              - Effect: Allow
                Action:
                  - logs:CreateLogGroup
                  - logs:PutRetentionPolicy
                  - logs:DeleteLogGroup
                  - logs:Describe*
                  - logs:ListTagsForResource
                  - logs:TagResource
                  - logs:UntagResource
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/aws/lambda/wmlgv-assistant-*'
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group:/ecs/wmlgv-assistant-*'
              - Effect: Allow
                Action:
                  - logs:Describe*
                  - logs:ListTagsForResource
                  - logs:TagResource
                  - logs:UntagResource
                Resource:
                  - !Sub 'arn:aws:logs:${AWS::Region}:${AWS::AccountId}:log-group::log-stream:*'
              - Effect: Allow
                Action:
                  - lambda:GetFunction
                  - lambda:GetFunctionConfiguration
                  - lambda:GetFunctionConcurrency
                  - lambda:ListFunctions
                  - lambda:ListTags
                  - lambda:CreateFunction
                  - lambda:AddPermission
                  - lambda:UpdateFunctionCode
                  - lambda:UpdateFunctionConfiguration
                  - lambda:DeleteFunction
                  - lambda:RemovePermission
                  - lambda:GetFunctionConfiguration
                  - lambda:TagResource
                Resource: !Sub 'arn:aws:lambda:${AWS::Region}:${AWS::AccountId}:function:wmlgv-${Environment}-assistant-*'
              - Effect: Allow
                Action:
                  - lambda:GetLayerVersion
                  - lambda:ListLayerVersions
                Resource: 
                  - !Sub 'arn:aws:lambda:${AWS::Region}:************:layer:splunk-apm*'
              - Effect: Allow
                Action:
                  - iam:PassRole
                Resource:
                  - !GetAtt AgentLambdaRole.Arn
                  - !GetAtt BedrockRole.Arn
                  - !Sub 'arn:aws:iam::${AWS::AccountId}:role/wmlgv-assistant-bedrock-${Environment}-role'
              - Effect: Allow
                Action:
                  - bedrock:ListFoundationModels 
                  - bedrock:GetFoundationModel 
                  - bedrock:TagResource  
                  - bedrock:UntagResource  
                  - bedrock:ListTagsForResource  
                  - bedrock:CreateAgent  
                  - bedrock:UpdateAgent  
                  - bedrock:GetAgent  
                  - bedrock:ListAgents  
                  - bedrock:DeleteAgent 
                  - bedrock:CreateAgentActionGroup  
                  - bedrock:UpdateAgentActionGroup  
                  - bedrock:GetAgentActionGroup  
                  - bedrock:ListAgentActionGroups  
                  - bedrock:DeleteAgentActionGroup 
                  - bedrock:GetAgentVersion 
                  - bedrock:ListAgentVersions  
                  - bedrock:DeleteAgentVersion 
                  - bedrock:CreateAgentAlias  
                  - bedrock:UpdateAgentAlias                
                  - bedrock:GetAgentAlias 
                  - bedrock:ListAgentAliases 
                  - bedrock:DeleteAgentAlias 
                  - bedrock:AssociateAgentKnowledgeBase 
                  - bedrock:DisassociateAgentKnowledgeBase 
                  - bedrock:GetKnowledgeBase 
                  - bedrock:ListKnowledgeBases 
                  - bedrock:ListAgentKnowledgeBases
                  - bedrock:PrepareAgent 
                  - bedrock:InvokeAgent
                  - bedrock:CreateGuardrail
                  - bedrock:GetGuardrail
                  - bedrock:UpdateGuardrail
                  - bedrock:ListGuardrails
                  - bedrock:DeleteGuardrail
                  - bedrock:CreateGuardrailVersion
                  - bedrock:ApplyGuardrail
                  - bedrock:CreateInferenceProfile
                  - bedrock:GetInferenceProfile
                  - bedrock:ListInferenceProfiles
                  - bedrock:DeleteInferenceProfile
                  - bedrock:TagResource
                  - bedrock:UntagResource
                  - bedrock:ListTagsForResource
                Resource:
                  - !Sub 'arn:aws:bedrock:${AWS::Region}:${AWS::AccountId}:agent*'
                  - !Sub 'arn:aws:bedrock:${AWS::Region}:${AWS::AccountId}:agent*'
                  - 'arn:aws:bedrock:*::foundation-model/*'
                  - !Sub 'arn:aws:bedrock:${AWS::Region}:${AWS::AccountId}:knowledge-base/*'
                  - !Sub 'arn:aws:bedrock:${AWS::Region}:${AWS::AccountId}:guardrail/*'
                  - !Sub 'arn:aws:bedrock:*:${AWS::AccountId}:inference-profile/*'
                  - !Sub 'arn:aws:bedrock:*:${AWS::AccountId}:application-inference-profile/*'
              - Effect: Allow
                Action:
                  - redshift-serverless:CreateNamespace
                  - redshift-serverless:DeleteNamespace
                  - redshift-serverless:GetNamespace
                  - redshift-serverless:UpdateNamespace
                  - redshift-serverless:CreateWorkgroup
                  - redshift-serverless:DeleteWorkgroup 
                  - redshift-serverless:GetWorkgroup
                  - redshift-serverless:UpdateWorkgroup
                  - redshift-serverless:TagResource
                  - redshift-serverless:ListTagsForResource
                Resource:
                  - !Sub 'arn:aws:redshift-serverless:${AWS::Region}:${AWS::AccountId}:namespace/*'
                  - !Sub 'arn:aws:redshift-serverless:${AWS::Region}:${AWS::AccountId}:workgroup/*'
              - Effect: Allow
                Action:
                  - 'iam:CreateServiceLinkedRole'
                Resource: !Sub 'arn:aws:iam::${AWS::AccountId}:role/aws-service-role/redshift.amazonaws.com/AWSServiceRoleForRedshift'
                Condition:
                  StringLike:
                    iam:AWSServiceName: redshift.amazonaws.com
              - Effect: Allow
                Action:
                  - 'iam:GetRole'
                  - 'iam:PassRole'
                Resource: 
                  - !GetAtt RedshiftRole.Arn
              - Sid: ECS
                Effect: Allow
                Action:
                  - ecs:CreateCluster
                  - ecs:DescribeClusters
                  - ecs:UpdateCluster
                  - ecs:CreateService
                  - ecs:DescribeServices
                  - ecs:DeleteService
                  - ecs:UpdateService
                  - ecs:UntagResource
                  - ecs:TagResource
                  - ecs:DeleteCluster
                Resource:
                  - !Sub arn:aws:ecs:${AWS::Region}:${AWS::AccountId}:service/*
                  - !Sub arn:aws:ecs:${AWS::Region}:${AWS::AccountId}:cluster/* 
                  - !Sub arn:aws:ecs:${AWS::Region}:${AWS::AccountId}:task/*/*
                  - !Sub arn:aws:ecs:${AWS::Region}:${AWS::AccountId}:task-definition/*
              - Sid: ECSTasks
                Effect: Allow
                Action:
                  - ecs:RegisterTaskDefinition
                  - ecs:DeregisterTaskDefinition
                  - ecs:Describe*
                  - ecs:List*
                Resource:
                  - "*"
              - Sid: Route53Permissions
                Effect: Allow
                Action:
                  - route53:GetHostedZone
                  - route53:GetChange
                  - route53:ChangeResourceRecordSets
                Resource:
                  - arn:aws:route53:::hostedzone/*
                  - arn:aws:route53:::change/*
              - Sid: LoadBalancerPermissions
                Effect: Allow
                Action:
                  - elasticloadbalancing:CreateLoadBalancer
                  - elasticloadbalancing:DeleteLoadBalancer
                  - elasticloadbalancing:ConfigureHealthCheck
                  - elasticloadbalancing:DeregisterInstancesFromLoadBalancer
                  - elasticloadbalancing:RegisterInstancesWithLoadBalancer
                  - elasticloadbalancing:DeregisterTargets
                  - elasticloadbalancing:RegisterTargets
                  - elasticloadbalancing:ModifyLoadBalancerAttributes
                  - elasticloadbalancing:SetLoadBalancerPoliciesOfListener
                  - elasticloadbalancing:CreateTargetGroup
                  - elasticloadbalancing:DeleteTargetGroup
                  - elasticloadbalancing:ModifyTargetGroup
                  - elasticloadbalancing:AddTags
                  - elasticloadbalancing:RemoveTags
                  - elasticloadbalancing:CreateListener
                  - elasticloadbalancing:DeleteListener
                  - elasticloadbalancing:ModifyListener
                  - elasticloadbalancing:SetSecurityGroups
                  - elasticloadbalancing:ModifyTargetGroupAttributes
                Resource:
                  - !Sub arn:aws:elasticloadbalancing:${AWS::Region}:${AWS::AccountId}:loadbalancer/lb-wmlgv-assistant-*
                  - !Sub arn:aws:elasticloadbalancing:${AWS::Region}:${AWS::AccountId}:targetgroup/*
                  - !Sub arn:aws:elasticloadbalancing:${AWS::Region}:${AWS::AccountId}:loadbalancer/app/*
                  - !Sub arn:aws:elasticloadbalancing:${AWS::Region}:${AWS::AccountId}:listener/app/*
              - Sid: DescribeLoadBalancerPermissions
                Effect: Allow
                Action:
                  - elasticloadbalancing:Describe*
                Resource:
                  - "*"
              - Sid: ParamStoreDescribePermissions
                Effect: Allow
                Action:
                  - ssm:Describe*
                Resource:
                  - "*"
              - Sid: ParamStoreWritePermissions
                Effect: Allow
                Action:
                  - ssm:PutParameter
                  - ssm:DeleteParameter
                Resource:
                  - !Sub arn:aws:ssm:${AWS::Region}:${AWS::AccountId}:parameter/${Environment}/wmlgv-assistant-orchestrator/alb-arn
              - Sid: RolePermissions
                Effect: Allow
                Action:
                  - iam:CreateRole
                  - iam:DeleteRole
                  - iam:GetRole
                  - iam:PassRole
                  - iam:AttachRolePolicy
                  - iam:DetachRolePolicy
                  - iam:GetRolePolicy
                  - iam:PutRolePolicy
                  - iam:DeleteRolePolicy
                  - iam:TagRole
                Resource:
                  - !Sub arn:aws:iam::${AWS::AccountId}:role/role-wmlgv-*
              - Sid: SecretsManagerPermissions
                Effect: Allow
                Action:
                  - secretsmanager:CreateSecret
                  - secretsmanager:DeleteSecret
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                  - secretsmanager:UpdateSecret
                  - secretsmanager:TagResource
                  - secretsmanager:UntagResource
                Resource:
                  - !Sub arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:wmlgv-assistant-orchestrator-${Environment}/*

  AgentLambdaRole:
    Type: AWS::IAM::Role
    Properties:
      PermissionsBoundary: !Sub 'arn:aws:iam::${AWS::AccountId}:policy/${PermissionsBoundaryPolicy}'
      RoleName: !Sub
        - 'wmlgv-assistant-lambda-${environment}-role'
        - { environment: !Ref Environment }
      AssumeRolePolicyDocument:
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - 'lambda.amazonaws.com'
            Action: sts:AssumeRole
      Path: /
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/service-role/AWSLambdaBasicExecutionRole
      Policies:
        - PolicyName: ReportDataAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:ListBucket
                Resource: #Todo: tighten this up to only allow the specific buckets later
                  - arn:aws:s3:::wmlgv*
                  - arn:aws:s3:::wmlgv-*/*
        - PolicyName: BedrockAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action: bedrock:InvokeModel
                Resource: !Sub arn:aws:bedrock:${AWS::Region}:${AWS::AccountId}:agent/*
        - PolicyName: RedshiftAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - 'redshift:DescribeClusters'
                  - 'redshift:DescribeClusterSnapshots'
                  - 'redshift:GetClusterCredentials'
                  - 'redshift:DescribeClusterParameters'
                  - 'redshift:DescribeClusterParameterGroups'
                  - 'redshift:DescribeTableRestoreStatus'
                  - 'redshift:DescribeClusterSecurityGroups'
                  - 'redshift:DescribeEventSubscriptions'
                  - 'redshift:DescribeEventCategories'
                  - 'redshift:DescribeEvents'
                  - 'redshift-data:GetStatementResult'
                  - 'redshift-data:DescribeStatement'
                  - 'redshift-data:ExecuteStatement'
                Resource: 
                  - !Sub 'arn:aws:redshift:${AWS::Region}:${AWS::AccountId}:namespace:wmlgv-${Environment}-woodmac-assistant-valuations-namespace:*'

  BedrockRole:
    Type: AWS::IAM::Role
    Properties:
      PermissionsBoundary: !Sub 'arn:aws:iam::${AWS::AccountId}:policy/${PermissionsBoundaryPolicy}'
      RoleName: !Sub
        - 'wmlgv-assistant-bedrock-${environment}-role'
        - { environment: !Ref Environment }
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service:
                - 'bedrock.amazonaws.com'
            Action: sts:AssumeRole
      Path: /
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonBedrockFullAccess
      Policies:
        - PolicyName: SchemaDataAccess
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Effect: Allow
                Action:
                  - s3:GetObject
                  - s3:ListBucket
                Resource:
                  - arn:aws:s3:::wmlgv*
                  - arn:aws:s3:::wmlgv-*/*

  RedshiftRole:
    Type: AWS::IAM::Role
    Properties:
      PermissionsBoundary: !Sub 'arn:aws:iam::${AWS::AccountId}:policy/${PermissionsBoundaryPolicy}'
      RoleName: !Sub
        - 'wmlgv-assistant-redshift-${environment}-role'
        - { environment: !Ref Environment }
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: redshift.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonRedshiftAllCommandsFullAccess


  FirehoseExecutionRole: #to remove just for POC
    Type: AWS::IAM::Role
    Properties:
      PermissionsBoundary: !Sub 'arn:aws:iam::${AWS::AccountId}:policy/${PermissionsBoundaryPolicy}'
      RoleName: !Sub 'firehose-role-full-access-${Environment}-poc'
      Path: /
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: firehose.amazonaws.com
            Action: sts:AssumeRole
      ManagedPolicyArns:
        - arn:aws:iam::aws:policy/AmazonS3FullAccess
        - arn:aws:iam::aws:policy/AWSGlueConsoleFullAccess
        - arn:aws:iam::aws:policy/AmazonKinesisFirehoseFullAccess
        - arn:aws:iam::aws:policy/CloudWatchLogsFullAccess

  AgentRoleParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Description: The name of the Agent Role
      Type: String
      Name: !Sub /${Environment}/wmlgv-assistant-agent/deployment-agent-role-name
      Value: !Ref AgentRole

Outputs:
  AgentLambdaRole:
    Description: Role for API lambda execution
    Value: !GetAtt AgentLambdaRole.Arn
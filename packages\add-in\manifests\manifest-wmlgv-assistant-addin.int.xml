<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<OfficeApp xmlns="http://schemas.microsoft.com/office/appforoffice/1.1" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:bt="http://schemas.microsoft.com/office/officeappbasictypes/1.0" xmlns:ov="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="TaskPaneApp">
  <Id>1a8d2973-b765-4fa0-b81a-c7e53f160c04</Id>
  <Version>*******</Version>
  <ProviderName>Woodmac</ProviderName>
  <DefaultLocale>en-US</DefaultLocale>
  <DisplayName DefaultValue="Woodmac Excel AI Assistant"/>
  <Description DefaultValue="Woodmac valuations api connector add-in."/>
  <IconUrl DefaultValue="https://wmlgv-assistant-addin-int.woodmac.com/assets/icon-32.png"/>
  <HighResolutionIconUrl DefaultValue="https://wmlgv-assistant-addin-int.woodmac.com/assets/icon-64.png"/>
  <SupportUrl DefaultValue="https://www.woodmac.com/help"/>
  <AppDomains>
    <AppDomain>https://www.woodmac.com</AppDomain>
  </AppDomains>
  <Hosts>
    <Host Name="Workbook"/>
  </Hosts>
  <DefaultSettings>
    <SourceLocation DefaultValue="https://wmlgv-assistant-addin-int.woodmac.com/taskpane.html"/>
  </DefaultSettings>
  <Permissions>ReadWriteDocument</Permissions>
  <VersionOverrides xmlns="http://schemas.microsoft.com/office/taskpaneappversionoverrides" xsi:type="VersionOverridesV1_0">
    <Hosts>
      <Host xsi:type="Workbook">
        <DesktopFormFactor>
          <GetStarted>
            <Title resid="GetStarted.Title"/>
            <Description resid="GetStarted.Description"/>
            <LearnMoreUrl resid="GetStarted.LearnMoreUrl"/>
          </GetStarted>
          <FunctionFile resid="Commands.Url"/>
          <ExtensionPoint xsi:type="PrimaryCommandSurface">
            <OfficeTab id="TabHome">
              <Group id="CommandsGroup">
                <Label resid="CommandsGroup.Label"/>
                <Icon>
                  <bt:Image size="16" resid="Icon.16x16"/>
                  <bt:Image size="32" resid="Icon.32x32"/>
                  <bt:Image size="80" resid="Icon.80x80"/>
                </Icon>
                <Control xsi:type="Button" id="TaskpaneButton">
                  <Label resid="TaskpaneButton.Label"/>
                  <Supertip>
                    <Title resid="TaskpaneButton.Label"/>
                    <Description resid="TaskpaneButton.Tooltip"/>
                  </Supertip>
                  <Icon>
                    <bt:Image size="16" resid="Icon.16x16"/>
                    <bt:Image size="32" resid="Icon.32x32"/>
                    <bt:Image size="80" resid="Icon.80x80"/>
                  </Icon>
                  <Action xsi:type="ShowTaskpane">
                    <TaskpaneId>ButtonId1</TaskpaneId>
                    <SourceLocation resid="Taskpane.Url"/>
                  </Action>
                </Control>
              </Group>
            </OfficeTab>
          </ExtensionPoint>
        </DesktopFormFactor>
      </Host>
    </Hosts>
    <Resources>
      <bt:Images>
        <bt:Image id="Icon.16x16" DefaultValue="https://wmlgv-assistant-addin-int.woodmac.com/assets/icon-16.png"/>
        <bt:Image id="Icon.32x32" DefaultValue="https://wmlgv-assistant-addin-int.woodmac.com/assets/icon-32.png"/>
        <bt:Image id="Icon.80x80" DefaultValue="https://wmlgv-assistant-addin-int.woodmac.com/assets/icon-80.png"/>
      </bt:Images>
      <bt:Urls>
        <bt:Url id="GetStarted.LearnMoreUrl" DefaultValue="https://go.microsoft.com/fwlink/?LinkId=276812"/>
        <bt:Url id="Commands.Url" DefaultValue="https://wmlgv-assistant-addin-int.woodmac.com/commands.html"/>
        <bt:Url id="Taskpane.Url" DefaultValue="https://wmlgv-assistant-addin-int.woodmac.com/taskpane.html"/>
      </bt:Urls>
      <bt:ShortStrings>
        <bt:String id="GetStarted.Title" DefaultValue="Get interacting with Woodmac Valuations!"/>
        <bt:String id="CommandsGroup.Label" DefaultValue="Valuations"/>
        <bt:String id="TaskpaneButton.Label" DefaultValue="INT: Woodmac Assistant"/>
      </bt:ShortStrings>
      <bt:LongStrings>
        <bt:String id="GetStarted.Description" DefaultValue="Get interacting with Woodmac Valuations!"/>
        <bt:String id="TaskpaneButton.Tooltip" DefaultValue="Valuations"/>
      </bt:LongStrings>
    </Resources>
  </VersionOverrides>
</OfficeApp>
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "wmlgv-assistant-e2e"
version = "0.1.0"
description = "End-to-end tests for the wmlgv-assistant application"
requires-python = ">=3.12"
dependencies = [
    "boto3==1.37.7",
    "sentence_transformers==3.4.1",
    "pandas==2.2.0",
    "pyarrow==15.0.1",
    "pytest",
    "pytest-asyncio",
    "pytest-xdist",
]

[project.optional-dependencies]
dev = [
    "black",
    "flake8",
    "mypy",
]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true

[tool.pytest.ini_options]
testpaths = ["src/tests"]
python_files = ["test_*.py"]
addopts = "-v --tb=short"

#!/bin/bash

echo "Starting build in Lambda container..."

# Update schema with definitions from CSV
echo "Updating schema with definitions from CSV..."
python3 update_schema_with_definitions.py

mkdir -p package

cp -r tools package/
cp *.py package/
cp requirements.txt package/
cp wmlgv-assistant-agent-schema.yaml package/

docker run --rm \
  -v "$(pwd)/package:/var/task" \
  --entrypoint /bin/bash \
  public.ecr.aws/lambda/python:3.12 \
  -c "pip install --target /var/task -r /var/task/requirements.txt && \
      cd /var/task"

cd package
zip -r ../wmlgv-assistant-agent.zip .
cd ..

rm -rf package

echo "Build complete. wmlgv-assistant-agent.zip created."

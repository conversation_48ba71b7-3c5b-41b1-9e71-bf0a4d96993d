import React from "react";

interface IconProps extends React.SVGProps<SVGSVGElement> {
  title?: string;
  path: string;
  width?: string;
  height?: string;
  viewBox?: string;
}

const createIcon = ({ title, path, width = "24", height = "24", viewBox = "0 0 80 80", ...props }: IconProps) => {
  return function IconComponent(iconProps: React.SVGProps<SVGSVGElement>) {
    return (
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox={viewBox}
        width={width}
        height={height}
        {...props}
        {...iconProps}
      >
        {title && <title>{title}</title>}
        <path d={path} fill="currentColor" />
      </svg>
    );
  };
};

export default createIcon;

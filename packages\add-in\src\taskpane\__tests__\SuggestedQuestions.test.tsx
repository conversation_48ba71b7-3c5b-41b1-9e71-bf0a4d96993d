import React from "react";
import { render, screen } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { SuggestedQuestions } from "../SuggestedQuestions";

jest.mock("../SuggestedQuestion", () => ({
  SuggestedQuestion: ({
    question,
    onSuggestedClicked,
  }: {
    question: string;
    onSuggestedClicked: (message: string) => void;
  }) => (
    <div data-testid={`suggested-question-${question}`}>
      <button onClick={() => onSuggestedClicked(question)}>{question}</button>
    </div>
  ),
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("SuggestedQuestions", () => {
  const mockHandleChatMessageEntered = jest.fn();
  const mockQuestions = ["Question 1", "Question 2", "Question 3"];

  beforeEach(() => {
    mockHandleChatMessageEntered.mockClear();
  });

  it("renders nothing when showSuggestedQuestions is false", () => {
    renderWithProvider(
      <SuggestedQuestions
        handleChatMessageEntered={mockHandleChatMessageEntered}
        showSuggestedQuestions={false}
        suggestedQuestions={mockQuestions}
      />
    );

    expect(screen.queryByTestId("suggested-question-Question 1")).not.toBeInTheDocument();
    expect(screen.queryByTestId("suggested-question-Question 2")).not.toBeInTheDocument();
    expect(screen.queryByTestId("suggested-question-Question 3")).not.toBeInTheDocument();
  });

  it("renders nothing when suggestedQuestions is undefined", () => {
    renderWithProvider(
      <SuggestedQuestions
        handleChatMessageEntered={mockHandleChatMessageEntered}
        showSuggestedQuestions={true}
        suggestedQuestions={undefined}
      />
    );

    expect(screen.queryByTestId("suggested-question-Question 1")).not.toBeInTheDocument();
  });

  it("renders all suggested questions when showSuggestedQuestions is true", () => {
    renderWithProvider(
      <SuggestedQuestions
        handleChatMessageEntered={mockHandleChatMessageEntered}
        showSuggestedQuestions={true}
        suggestedQuestions={mockQuestions}
      />
    );

    expect(screen.getByTestId("suggested-question-Question 1")).toBeInTheDocument();
    expect(screen.getByTestId("suggested-question-Question 2")).toBeInTheDocument();
    expect(screen.getByTestId("suggested-question-Question 3")).toBeInTheDocument();
  });

  it("renders empty list when suggestedQuestions is empty array", () => {
    renderWithProvider(
      <SuggestedQuestions
        handleChatMessageEntered={mockHandleChatMessageEntered}
        showSuggestedQuestions={true}
        suggestedQuestions={[]}
      />
    );

    expect(screen.queryByTestId("suggested-question-Question 1")).not.toBeInTheDocument();
  });

  it("handles single question correctly", () => {
    renderWithProvider(
      <SuggestedQuestions
        handleChatMessageEntered={mockHandleChatMessageEntered}
        showSuggestedQuestions={true}
        suggestedQuestions={["Single Question"]}
      />
    );

    expect(screen.getByTestId("suggested-question-Single Question")).toBeInTheDocument();
    expect(screen.getByText("Single Question")).toBeInTheDocument();
  });
});

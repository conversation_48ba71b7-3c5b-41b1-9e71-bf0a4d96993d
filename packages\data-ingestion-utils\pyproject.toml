[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "wmlgv-assistant-data-ingestion-utils"
version = "0.1.0"
description = "Utilities for ingesting, processing, and flattening valuation data"
requires-python = ">=3.12"
dependencies = [
    "requests==2.32.3",
    "pandas==2.2.3",
    "python-dotenv==1.0.1",
    "fastparquet==2024.11.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "black",
    "flake8",
    "mypy",
]
test = [
    "pytest>=6.0",
    "pytest-cov",
]

[tool.black]
line-length = 88
target-version = ['py312']

[tool.mypy]
python_version = "3.12"
warn_return_any = true
warn_unused_configs = true

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]

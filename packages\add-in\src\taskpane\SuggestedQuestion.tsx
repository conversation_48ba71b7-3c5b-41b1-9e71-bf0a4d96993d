import React from 'react';
import { Button } from '@fluentui/react-components';
import { useStyles } from './use-styles';

const SuggestedQuestion = ({ question, onSuggestedClicked }: { question: string; onSuggestedClicked: (question: string) => void; }) => {
    const { userMessageContainer, suggestedQuestion } = useStyles();

    return (<div className={userMessageContainer}>
        <Button size="small" className={suggestedQuestion} key={question} onClick={() => onSuggestedClicked(question)}>{question}</Button>
    </div>)
};

export { SuggestedQuestion };

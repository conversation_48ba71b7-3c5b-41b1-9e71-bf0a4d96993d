import React from "react";
import { render, screen } from "@testing-library/react";
import { LoginWithOkta } from "../LoginWithOkta";
import { oktaClient } from "../../security";

jest.mock("../../security");

const mockOktaClient = oktaClient as jest.Mocked<typeof oktaClient>;

describe("LoginWithOkta", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockOktaClient.signInWithRedirect = jest.fn();
  });

  it("renders loading message", () => {
    mockOktaClient.signInWithRedirect.mockResolvedValue();

    render(<LoginWithOkta />);

    expect(screen.getByText("Loading sign-in form")).toBeInTheDocument();
  });

  it("calls signInWithRedirect on mount", () => {
    mockOktaClient.signInWithRedirect.mockResolvedValue();

    render(<LoginWithOkta />);

    expect(mockOktaClient.signInWithRedirect).toHaveBeenCalledTimes(1);
  });

  it("handles signInWithRedirect error", async () => {
    const consoleErrorSpy = jest.spyOn(console, "error").mockImplementation();
    const error = new Error("Sign in failed");
    mockOktaClient.signInWithRedirect.mockRejectedValue(error);

    render(<LoginWithOkta />);

    expect(mockOktaClient.signInWithRedirect).toHaveBeenCalledTimes(1);

    await new Promise((resolve) => setTimeout(resolve, 0));

    expect(consoleErrorSpy).toHaveBeenCalledWith(error);

    consoleErrorSpy.mockRestore();
  });
});

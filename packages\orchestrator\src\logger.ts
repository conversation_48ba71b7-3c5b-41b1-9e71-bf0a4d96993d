import {
    createLogger,
    LogLevel,
    requestLoggingMiddleware,
} from '@woodmac-sdk/logging';

const app = 'wmlgv-orchestrator';
const component = 'api';
const logger = createLogger('service', {
    app,
    component,
    level: LogLevel.DEBUG,
    redact: {
        paths: [
            '*.bearerToken',
            'bearerToken',
        ],
    },
});

export const requestLogger = requestLoggingMiddleware({
    app,
    component,
});

export default logger;

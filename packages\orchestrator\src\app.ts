import 'express-async-errors';
import express, { NextFunction, Request, Response } from 'express';
import { AxiosError } from 'axios';
import cors from 'cors';
import {
    BedrockAgentRuntimeClient,
    InvokeAgentCommand,
} from '@aws-sdk/client-bedrock-agent-runtime';
import { createAuthMiddleware } from './auth/auth-middleware';
import { entitlementsMiddleware } from './auth/entitlements-middleware';
import logger, { requestLogger } from './logger';
import { getAgentAliases } from './bedrock/agent-client';
import { validateAlias } from './validation/alias-validator';

const issuer = process.env.TOKEN_ISSUER;
const audience = process.env.TOKEN_AUDIENCE;

const authMiddleware = createAuthMiddleware(issuer, audience);

const app = express();
app.use(express.json());
app.use(cors());

// allow health check without token
app.get('/health', (_req: Request, res: Response) => {
    logger.debug('Health check');
    res.status(200).json({ status: 'ok' });
});

app.get('/favicon.ico', (req, res) => res.status(204).end());
app.use(requestLogger);
app.use(authMiddleware);
if (process.env.USE_ENTITLEMENTS === 'true') {
    logger.info('Entitlements middleware enabled');
    app.use(entitlementsMiddleware);
} else {
    logger.info('Entitlements middleware disabled');
}
// eslint-disable-next-line @typescript-eslint/no-require-imports
const requestContext = require('request-context');
app.use((req: Request, res: Response, next: NextFunction) => {
    if ('user' in req && req.user) {
        requestContext.set('woodmac:user', req.user);
    }
    next();
});

const tableDelimiter = '=====';
const columnDelimiter = '|';
const tableLocationMarker = '{{TABLE}}';

const hasTable = (text: string) => text.includes(columnDelimiter);
const hasTableSection = (text: string) =>
    text.includes(tableDelimiter) && hasTable(text.split(tableDelimiter)[0]);

app.get('/ask-chatbot', async ({ query }: Request, res: Response) => {
    const { prompt, sessionId, versionAlias } = query;
    const agentAlias = versionAlias as string ?? process.env.AGENT_ALIAS;

    logger.debug(
        { prompt, sessionId, versionAlias, agentAlias },
        'Ask chatbot parameters',
    );

    if (!await validateAlias(agentAlias)) {
        return res.status(400).json({
            error: 'Invalid alias',
            message: 'The provided agent alias id does not exist.',
        });
    }

    const bedrockAgent = {
        sessionId,
        agentId: process.env.AGENT_ID,
        agentAlias,
    };

    logger.info({ sessionId, prompt, bedrockAgent }, 'Chatbot question');

    const client = new BedrockAgentRuntimeClient({});

    const command = new InvokeAgentCommand({
        agentId: process.env.AGENT_ID,
        agentAliasId: (versionAlias as string | undefined) ?? process.env.AGENT_ALIAS,
        sessionId: sessionId as string,
        inputText: prompt as string,
        enableTrace: true,
    });

    try {
        let textResponse = '',
            llmInvocations = 0,
            totalInputTokens = 0,
            totalOutputTokens = 0;
        const response = await client.send(command);

        if (response.completion === undefined) {
            throw new Error('Completion is undefined');
        }

        for await (const chunkEvent of response.completion) {
            if (chunkEvent.chunk) {
                const chunk = chunkEvent.chunk;
                const decodedResponse = new TextDecoder('utf-8').decode(
                    chunk?.bytes,
                );
                textResponse += decodedResponse;
            } else if (chunkEvent.trace) {
                const { orchestrationTrace } = chunkEvent.trace.trace || {};
                if (orchestrationTrace?.modelInvocationInput) {
                    llmInvocations++;
                }

                if (orchestrationTrace?.modelInvocationOutput) {
                    const { metadata } =
                        orchestrationTrace.modelInvocationOutput;
                    if (metadata?.usage) {
                        const usage = metadata.usage;
                        totalInputTokens += usage.inputTokens || 0;
                        totalOutputTokens += usage.outputTokens || 0;
                    }
                }
            }
        }

        let table: string | undefined;
        let answer: string | undefined;

        if (hasTableSection(textResponse)) {
            [table, answer] = textResponse.split(tableDelimiter);
            answer = `${tableLocationMarker}${answer.trim()}`;
        } else if (hasTable(textResponse)) {
            const firstIndex = textResponse.indexOf(columnDelimiter);
            const lastIndex = textResponse.lastIndexOf(columnDelimiter);

            table = textResponse.slice(firstIndex, lastIndex + 1);
            answer = `${textResponse
                .slice(0, firstIndex)
                .trim()}${tableLocationMarker}${textResponse
                .slice(lastIndex + 1)
                .trim()}`;
        } else {
            answer = textResponse;
        }

        logger.info(
            {
                textResponse,
                table,
                answer,
                bedrockAgent,
                llmMetrics: {
                    llmInvocations,
                    totalInputTokens,
                    totalOutputTokens,
                    totalTokens: totalInputTokens + totalOutputTokens,
                },
            },
            'Chatbot response',
        );

        res.status(200).json({
            table: table?.trim(),
            answer: answer?.replace(tableDelimiter, '').trim(),
        });
    } catch (error) {
        logger.error(
            { error: error as AxiosError, bedrockAgent },
            'Error invoking agent',
        );
        throw error;
    }
});

app.get('/valuations-agent/aliases', async (_req: Request, res: Response) => {
    const versionAliases = await getAgentAliases();
    res.status(200).json({ versionAliases });
});

// eslint-disable-next-line @typescript-eslint/no-unused-vars
app.use((err: Error, _req: Request, res: Response, _: NextFunction) => {
    logger.error(err, 'Request error');
    res.status(500).json({ error:'Something has broken!' });
});

export { app };

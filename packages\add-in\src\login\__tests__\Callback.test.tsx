import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import { Callback } from "../Callback";
import { oktaClient } from "../../security";

jest.mock("../../security");
jest.mock("@okta/okta-react", () => ({
  LoginCallback: () => <div>Login Callback Component</div>,
}));

const mockOktaClient = oktaClient as jest.Mocked<typeof oktaClient>;

global.Office = {
  context: {
    ui: {
      messageParent: jest.fn(),
    },
  },
} as any;

describe("Callback", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.useFakeTimers();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it("renders LoginCallback component and logged in message", () => {
    mockOktaClient.authStateManager = {
      getAuthState: jest.fn().mockReturnValue({
        isAuthenticated: false,
      }),
    } as any;

    render(<Callback />);

    expect(screen.getByText("Login Callback Component")).toBeInTheDocument();
    expect(screen.getByText("Logged in")).toBeInTheDocument();
  });

  it("notifies parent when user is authenticated immediately", async () => {
    const authState = {
      isAuthenticated: true,
      accessToken: "test-token",
      idToken: "test-id-token",
    };

    mockOktaClient.authStateManager = {
      getAuthState: jest.fn().mockReturnValue(authState),
    } as any;

    render(<Callback />);

    jest.advanceTimersByTime(500);

    await waitFor(() => {
      expect(Office.context.ui.messageParent).toHaveBeenCalledWith(JSON.stringify(authState));
    });
  });

  it("retries notification when user is not authenticated initially", async () => {
    let callCount = 0;
    const authState = {
      isAuthenticated: false,
    };

    mockOktaClient.authStateManager = {
      getAuthState: jest.fn().mockImplementation(() => {
        callCount++;
        if (callCount > 2) {
          return { isAuthenticated: true, accessToken: "test-token" };
        }
        return authState;
      }),
    } as any;

    render(<Callback />);

    jest.advanceTimersByTime(500);
    jest.advanceTimersByTime(100);
    jest.advanceTimersByTime(100);

    await waitFor(() => {
      expect(Office.context.ui.messageParent).toHaveBeenCalledWith(
        JSON.stringify({ isAuthenticated: true, accessToken: "test-token" })
      );
    });

    expect(mockOktaClient.authStateManager.getAuthState).toHaveBeenCalledTimes(3);
  });

  it("continues retrying until authentication is successful", async () => {
    mockOktaClient.authStateManager = {
      getAuthState: jest.fn().mockImplementation(() => {
        return { isAuthenticated: false };
      }),
    } as any;

    render(<Callback />);

    jest.advanceTimersByTime(500);

    for (let i = 0; i < 5; i++) {
      jest.advanceTimersByTime(100);
    }

    expect(mockOktaClient.authStateManager.getAuthState).toHaveBeenCalledTimes(6);
    expect(Office.context.ui.messageParent).not.toHaveBeenCalled();
  });

  it("handles undefined auth state", async () => {
    mockOktaClient.authStateManager = {
      getAuthState: jest.fn().mockReturnValue(undefined),
    } as any;

    render(<Callback />);

    jest.advanceTimersByTime(500);
    jest.advanceTimersByTime(100);

    expect(mockOktaClient.authStateManager.getAuthState).toHaveBeenCalledTimes(2);
    expect(Office.context.ui.messageParent).not.toHaveBeenCalled();
  });

  it("handles null auth state", async () => {
    mockOktaClient.authStateManager = {
      getAuthState: jest.fn().mockReturnValue(null),
    } as any;

    render(<Callback />);

    jest.advanceTimersByTime(500);
    jest.advanceTimersByTime(100);

    expect(mockOktaClient.authStateManager.getAuthState).toHaveBeenCalledTimes(2);
    expect(Office.context.ui.messageParent).not.toHaveBeenCalled();
  });
});

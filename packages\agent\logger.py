import logging
import sys
import uuid
import os
from typing import Dict, Any, Optional
from pythonjsonlogger import jsonlogger
from datetime import datetime, timezone
from opentelemetry import trace

_current_correlation_id = None
_current_bedrock_info = None

# Suppress boto3 and botocore unnecessary logs
logging.getLogger("botocore.credentials").setLevel(logging.WARNING)
logging.getLogger("aiobotocore.credentials").setLevel(logging.WARNING)


def set_global_correlation_id(context=None) -> None:
    """Set the correlation ID globally for all logger instances."""
    if context and context.aws_request_id:
        global _current_correlation_id
        _current_correlation_id = context.aws_request_id


def set_bedrock_agent_info(event: Dict[str, Any]) -> None:
    """Extract and set Bedrock agent information from the event."""
    global _current_bedrock_info

    session_id = event.get("sessionId")
    agent = event.get("agent")

    if session_id and agent:
        _current_bedrock_info = {
            "sessionId": session_id,
            "agentId": agent.get("id"),
            "agentName": agent.get("name"),
            "agentVersion": agent.get("version"),
            "agentAlias": agent.get("alias"),
        }
    else:
        _current_bedrock_info = None


def set_trace_attributes(api_path: str = None, action: str = None) -> None:
    """Add custom attributes to the current OpenTelemetry trace span using global context and request details."""
    global _current_correlation_id, _current_bedrock_info

    try:
        current_span = trace.get_current_span()
        if current_span.is_recording():
            if api_path:
                current_span.update_name(api_path)
                current_span.set_attribute("http.route", api_path)
            if _current_correlation_id:
                current_span.set_attribute("correlation.id", _current_correlation_id)
            if _current_bedrock_info:
                if session_id := _current_bedrock_info.get("sessionId"):
                    current_span.set_attribute("session.id", session_id)
                if agent_id := _current_bedrock_info.get("agentId"):
                    current_span.set_attribute("agentId", agent_id)
                if agent_name := _current_bedrock_info.get("agentName"):
                    current_span.set_attribute("agentName", agent_name)
                if agent_version := _current_bedrock_info.get("agentVersion"):
                    current_span.set_attribute("agentVersion", agent_version)
                if action:
                    current_span.set_attribute("actionGroup", action)
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.debug(f"Failed to set trace attributes: {e}")


def set_trace_response_attributes(status_code: int) -> None:
    """Add response attributes to the current OpenTelemetry trace span."""
    try:
        current_span = trace.get_current_span()
        if current_span.is_recording():
            current_span.set_attribute("http.status_code", status_code)
            # Update span status to reflect the response status
            if status_code >= 400:
                from opentelemetry.trace import Status, StatusCode

                current_span.set_status(Status(StatusCode.ERROR))
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.debug(f"Failed to set trace response attributes: {e}")


class CustomJsonFormatter(jsonlogger.JsonFormatter):
    def add_fields(self, log_record, record, message_dict):
        super(CustomJsonFormatter, self).add_fields(log_record, record, message_dict)

        # Basic fields
        if not log_record.get("message"):
            log_record["message"] = record.getMessage()

        if not log_record.get("level"):
            log_record["level"] = record.levelname

        if not log_record.get("time"):
            log_record["time"] = (
                datetime.fromtimestamp(record.created, tz=timezone.utc).strftime(
                    "%Y-%m-%dT%H:%M:%S.%fZ"
                )[:-4]
                + "Z"
            )

        # Add standard fields if provided in extras
        if hasattr(record, "appName"):
            log_record["appName"] = record.appName

        if hasattr(record, "componentName"):
            log_record["componentName"] = record.componentName

        if hasattr(record, "aws") and record.aws:
            log_record["aws"] = record.aws

        if hasattr(record, "name"):
            log_record["name"] = record.name

        if hasattr(record, "correlationId"):
            log_record["correlationId"] = record.correlationId

        if hasattr(record, "user") and record.user:
            log_record["user"] = record.user

        if hasattr(record, "payload") and record.payload:
            log_record["payload"] = record.payload

        # Add error info if available
        if not log_record.get("error") and (record.stack_info or record.exc_info):
            log_record["error"] = {
                "message": record.getMessage(),
                "stacktrace": self.formatStack(record.stack_info),
                "traceback": self.formatException(record.exc_info),
            }


class Logger:
    DEFAULT_LOG_LEVEL = logging.INFO

    def __init__(self, name: str = None, level: int = DEFAULT_LOG_LEVEL):
        self.logger = logging.getLogger(name or __name__)
        self.logger.setLevel(level)
        self.component_name = "api"
        self.app_name = os.environ.get(
            "AWS_LAMBDA_FUNCTION_NAME", "wmlgv-assistant-agent-lambda"
        )

        # Remove all handlers to avoid duplicate logs
        self.logger.propagate = False
        if self.logger.handlers:
            self.logger.handlers.clear()

        handler = logging.StreamHandler(sys.stdout)
        formatter = CustomJsonFormatter("%(message)s")
        handler.setFormatter(formatter)
        self.logger.addHandler(handler)

        self.user_info = None

    @property
    def correlation_id(self) -> str:
        """Get the current correlation ID, using the global one if set."""
        global _current_correlation_id
        return _current_correlation_id or str(uuid.uuid4())

    @property
    def bedrock_info(self) -> Optional[Dict[str, Any]]:
        global _current_bedrock_info
        return _current_bedrock_info

    def set_component_name(self, component_name: str) -> None:
        self.component_name = component_name

    def set_app_name(self, app_name: str) -> None:
        self.app_name = app_name

    def _get_aws_info(self) -> Dict[str, str]:
        """Get AWS environment information."""
        return {
            "region": os.environ.get("AWS_REGION", "unknown"),
            "executionEnv": os.environ.get("AWS_EXECUTION_ENV", "unknown"),
            "memoryLimit": os.environ.get("AWS_LAMBDA_FUNCTION_MEMORY_SIZE", "unknown"),
        }

    def set_user_info(self, user_id: str, email: str, account_id: str) -> None:
        """Set user information for logging."""
        self.user_info = {
            "id": user_id,
            "email": email,
            "accountId": account_id,
        }

    def _prepare_extra(self, extra: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        log_extra = {
            "appName": self.app_name,
            "componentName": self.component_name,
            "aws": self._get_aws_info(),
            "correlationId": self.correlation_id,
        }

        if self.user_info:
            log_extra["user"] = self.user_info

        if self.bedrock_info:
            log_extra["bedrockAgent"] = self.bedrock_info

        if extra:
            log_extra.update(extra)

        return log_extra

    def debug(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        self.logger.debug(message, extra=self._prepare_extra(extra))

    def info(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        self.logger.info(message, extra=self._prepare_extra(extra))

    def warning(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        self.logger.warning(message, extra=self._prepare_extra(extra))

    def error(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        self.logger.error(message, extra=self._prepare_extra(extra))

    def exception(self, message: str, extra: Optional[Dict[str, Any]] = None) -> None:
        prepared_extra = self._prepare_extra(extra)

        if not prepared_extra.get("payload"):
            import traceback

            exc_type, exc_value, exc_tb = sys.exc_info()
            if exc_type:
                prepared_extra["payload"] = {
                    "context": {
                        "responseBody": {
                            "error": exc_type.__name__,
                            "message": str(exc_value),
                            "statusCode": 500,
                        }
                    },
                    "stacktrace": {
                        "message": str(exc_value),
                        "name": exc_type.__name__,
                        "stack": "".join(
                            traceback.format_exception(exc_type, exc_value, exc_tb)
                        ),
                        "config": {},
                    },
                }

        self.logger.exception(message, extra=prepared_extra)


def get_logger(name: str = None, level: int = None) -> Logger:
    return Logger(name, level or Logger.DEFAULT_LOG_LEVEL)

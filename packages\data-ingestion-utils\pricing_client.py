import requests
from typing import Dict, List, Optional


class PricingClient:
    def __init__(self, pricing_url: str, token: str):
        self.pricing_url = pricing_url
        self.token = token
        self._price_decks_cache = None

    def get_latest_price_decks(self, refresh_cache: bool = False) -> List[Dict]:
        if self._price_decks_cache is None or refresh_cache:
            query = """
                query {
                    priceDecks {
                        name
                        id
                    }
                }
            """

            response = requests.post(
                f"{self.pricing_url}/graphql",
                json={"query": query},
                headers={"Cookie": f"iPlanetDirectoryPro={self.token}"},
            )

            response.raise_for_status()
            response_data = response.json()

            self._price_decks_cache = response_data["data"]["priceDecks"]

        return self._price_decks_cache

    def get_wm_price_decks(self) -> Dict[str, str]:
        price_decks = {}
        all_decks = self.get_latest_price_decks()

        for deck in all_decks:
            name = deck["name"]
            if name == "Base" and not price_decks.get("BASE"):
                price_decks["BASE"] = deck["id"]
            elif name == "Low" and not price_decks.get("LOW"):
                price_decks["LOW"] = deck["id"]
            elif name == "High" and not price_decks.get("HIGH"):
                price_decks["HIGH"] = deck["id"]

        print(f"Latest Woodmac price decks: {price_decks}", flush=True)

        assert len(price_decks) == 3, "Not all Woodmac price decks found!"

        return price_decks

    def get_price_deck_by_name(self, name: str) -> Optional[str]:
        all_decks = self.get_latest_price_decks()

        for deck in all_decks:
            if deck["name"].lower() == name.lower():
                return deck["id"]

        return None

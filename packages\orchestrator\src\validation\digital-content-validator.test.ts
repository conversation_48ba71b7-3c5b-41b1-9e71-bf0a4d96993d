import { Message, validateMessages } from './digital-content-validator';

describe('validateMessages', () => {
    it('returns valid for alternating user and assistant messages with non-empty content', () => {
        const messages = [
            { content: 'Hello', role: 'user' },
            { content: 'Hi there!', role: 'assistant' },
            { content: 'How are you?', role: 'user' },
            { content: 'I am fine.', role: 'assistant' },
        ] as Message[];
        expect(validateMessages(messages)).toEqual({ valid: true });
    });

    it('returns invalid if a message has empty content', () => {
        const messages = [
            { content: 'Hello', role: 'user' },
            { content: '', role: 'assistant' },
        ] as Message[];
        expect(validateMessages(messages)).toEqual({
            valid: false,
            error: 'Each message must have non-empty content',
        });
    });

    it('returns invalid if a message has only whitespace content', () => {
        const messages = [
            { content: 'Hello', role: 'user' },
            { content: '   ', role: 'assistant' },
        ] as Message[];
        expect(validateMessages(messages)).toEqual({
            valid: false,
            error: 'Each message must have non-empty content',
        });
    });

    it('returns invalid if a message is missing the role', () => {
        const messages = [
            { content: 'Hello', role: 'user' as const },
            { content: 'Hi there!' },
        ] as Message[];
        
        expect(validateMessages(messages)).toEqual({
            valid: false,
            error: 'Each message must have a role of either "user" or "assistant"',
        });
    });

    it('returns invalid if a message has an invalid role', () => {
        const messages = [
            { content: 'Hello', role: 'user' },
            { content: 'Hi there!', role: 'system' },
        ] as Message[];
        expect(validateMessages(messages)).toEqual({
            valid: false,
            error: 'Each message must have a role of either "user" or "assistant"',
        });
    });

    it('returns invalid if there are consecutive messages with the same role', () => {
        const messages = [
            { content: 'Hello', role: 'user' },
            { content: 'How are you?', role: 'user' },
        ] as Message[];
        expect(validateMessages(messages)).toEqual({
            valid: false,
            error: 'Cannot have consecutive messages with the same role',
        });
    });

    it('returns valid for a single message', () => {
        const messages = [
            { content: 'Hello', role: 'user' },
        ] as Message[];
        expect(validateMessages(messages)).toEqual({ valid: true });
    });

    it('returns valid for an empty array', () => {
        expect(validateMessages([])).toEqual({ valid: true });
    });
});
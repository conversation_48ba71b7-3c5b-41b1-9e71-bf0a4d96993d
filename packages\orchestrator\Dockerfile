FROM harbor.prod.woodmac.com/docker-hub/library/node:20-alpine AS build

# Set the working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package.json /app
COPY package-lock.json /app

# Install dependencies
RUN npm ci

# Copy the rest of the application code
COPY ./src /app/src
COPY tsconfig.json /app

# Build
RUN npm run build:ci

FROM harbor.prod.woodmac.com/docker-hub/library/node:20-alpine
COPY --from=build /app/**/*.cjs ./
COPY --from=build /app/**/*.map ./

# Expose the port the app runs on
EXPOSE 5001

# Command to run the application
CMD ["node", "server.cjs"]
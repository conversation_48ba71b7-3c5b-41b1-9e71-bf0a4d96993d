# Data Ingestion Utilities

This package contains utilities for ingesting, processing, and flattening valuation data. It's used to generate data sets that can be consumed by the wmlgv assistant.

## Features

-   Generate valuation data for different scenarios (e.g. upstream assets, top 50 companies)
-   Process and flatten valuation data
-   Combine multiple parquet files
-   Integration with the Valuations API

## Local Development

### Prerequisites

-   Python 3.12+
-   pip

### Setup

1. Create a `.env` file in the root directory from `.env.dist`:

```bash
cp .env.dist .env
```

2. Install dependencies:

```bash
pip install -r requirements.txt
```

3. Run the generator script:

```bash
python generator.py --scenario all_upstream_assets
```

Available scenarios:

-   `all_upstream_assets`: Generates data for all upstream assets by region
-   `all_upstream_companies`: Generates data for all upstream companies
### Output

The script will generate parquet files in the `output` directory. These files can be used for data analysis or loaded into the GV Assistant's data store.

### Command Instructions
<pre>
usage:
    generator.py [-h] [--scenario {all_upstream_assets,all_upstream_companies}] [--version VERSION] [--max-retries MAX_RETRIES] [--combine] [--valuation-ids VALUATION_IDS [VALUATION_IDS ...] |
                    --reports-dir REPORTS_DIR]

Generate data for different scenarios

options:
  -h, --help            show this help message and exit
  --scenario {all_upstream_assets,all_upstream_companies}
                        Scenario to generate data for (choices: all_upstream_assets, all_upstream_companies)
  --version VERSION     Version to use (default: latest)
  --max-retries MAX_RETRIES
                        Maximum number of retry attempts for each valuation request (default: 1)
  --combine             Combine output files after generation
  --no-include-lens-metadata
                        Do not include lens metadata (default: include lens metadata)
  --valuation-ids VALUATION_IDS [VALUATION_IDS ...]
                        List of valuation IDs to combine (if provided, skips generation)
  --reports-dir REPORTS_DIR
                        Directory containing valuation reports to combine (mutually exclusive with --valuation-ids)

</pre>

### Usage examples

```bash
python.exe generator.py --scenario all_upstream_assets --combine
python.exe generator.py --scenario all_upstream_companies --combine
```
#!/usr/bin/env groovy
@Library('utils') _

AWS_REGION = 'us-east-1'
BUILD_ENVIRONMENT = woodmac.getJenkinsEnvironment()

String getEnvironment() {
    if (params.TARGET_ENVIRONMENT != null) {
        return params.TARGET_ENVIRONMENT
    }
    if (woodmac.getJenkinsEnvironment() == 'prod') {
        return 'aiint'
    }

    return 'aidev'
}

def getAvailableEnvironments() {
    if (woodmac.getJenkinsEnvironment() == 'dev') {
        return ['aidev']
    }
    return ['aiint', 'aiuat', 'aiprod']
}

def getAgentId() {
    def agentId = sh(
        script: "aws bedrock-agent list-agents | jq -r '.agentSummaries[] | select(.agentName == \"wmlgv-${AWS_ENVIRONMENT}-assistant-agent\") | .agentId'",
        returnStdout: true
    ).trim()

    println "Agent ID: ${agentId}"
    return agentId
}

def getAvailableAgentVersions() {
    def agentId = getAgentId()

    def versions = sh(
        script: "aws bedrock-agent list-agent-versions --agent-id ${agentId} | jq -r '[.agentVersionSummaries[] | select(.agentVersion|test(\"^[0-9]+\$\") )] | sort_by(.updatedAt) | .[].agentVersion'",
        returnStdout: true
    ).trim()

    println "Available Agent Versions: ${versions}"

    // Convert the newline-separated string to a Groovy list and return
    return versions.tokenize('\n')
}

environmentName = getEnvironment()
AGENT_IAM_ROLE_PARAMETER_NAME = "/${environmentName}/wmlgv-assistant-agent/deployment-agent-role-name"

properties([
    parameters([
        choice(name: 'TARGET_ENVIRONMENT', description: 'Environment to deploy', choices: getAvailableEnvironments()),
    ])
])

jenkinsAgent = "harbor.prod.woodmac.com/liveservices/jenkins-node-jnlp-cloudformation:3283.v92c105e0f819-1-alpine-jdk21-aws2"
def selectedVersion

pipeline {
    agent {
        ecs {
            inheritFrom "dynamic-us-east-1-${environmentName}"
            taskrole woodmac.getAgentRole(
                region: "us-east-1",
                environment: "${environmentName}",
                parameterName: "${AGENT_IAM_ROLE_PARAMETER_NAME}",
            )
            image "${jenkinsAgent}"
        }
    }
    options {
        disableConcurrentBuilds()
        ansiColor('xterm')
        timeout(time: 5, unit: 'MINUTES')
    }
    environment {
        AWS_ENVIRONMENT = getEnvironment()
    }
    stages {
        stage('Select version') {
            steps {
                script {
                    selectedVersion = input(
                        id: 'userInput', message: 'Select Agent Version', parameters: [
                            [$class: 'ChoiceParameterDefinition', name: 'AGENT_VERSION', choices: getAvailableAgentVersions(), description: 'Select the Agent Version to deploy']
                        ]
                    )
                }

                echo "Selected Agent Version: ${selectedVersion}"
            }
        }

        stage('Set Agent Version') {
            steps {
                script {
                    def agentId = getAgentId()

                    def aliasId = sh(
                        script: "aws bedrock-agent list-agent-aliases --agent-id ${agentId} | jq -r '.agentAliasSummaries[] | select(.agentAliasName == \"orchestrator\") | .agentAliasId'",
                        returnStdout: true
                    ).trim()
                    
                    echo "Setting Agent Version for Agent ID: ${agentId} Alias Id ${aliasId} to ${selectedVersion}"

                    sh """
                        aws bedrock-agent update-agent-alias \
                            --agent-id ${agentId} \
                            --agent-alias-id ${aliasId} \
                            --agent-alias-name orchestrator \
                            --routing-configuration agentVersion=${selectedVersion}
                    """
                }
            }
        }
    }
}

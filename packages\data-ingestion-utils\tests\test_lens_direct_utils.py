import pytest
import os
from unittest.mock import Mock, patch
import requests
from requests.exceptions import HTTPError

from lens_direct_utils import (
    LensDirectQueryBuilder,
    get_lens_direct_url_and_token,
)


class TestLensDirectQueryBuilder:
    """
    Test suite for the LensDirectQueryBuilder class.
    
    This class tests the OData query builder functionality for the Lens Direct API,
    including URL construction, parameter handling, HTTP request execution, and
    pagination support.
    """
    
    def setup_method(self):
        """Set up test fixtures with a sample query builder instance."""
        self.base_url = "https://api.example.com/data"
        self.api_key = "test_api_key"
        self.builder = LensDirectQueryBuilder(self.base_url, self.api_key)

    def test_init(self):
        """
        Test LensDirectQueryBuilder initialization.
        
        Verifies that:
        1. Base URL and API key are correctly stored
        2. All query parameters are initialized to empty/default values
        3. The builder is ready for method chaining
        """
        assert self.builder.base_url == self.base_url
        assert self.builder.api_key == self.api_key
        assert self.builder.select == []
        assert self.builder.filter == ""
        assert self.builder.orderby == ""
        assert self.builder.top is None
        assert self.builder.apply == ""

    def test_with_select(self):
        """
        Test the with_select method for specifying OData $select parameters.
        
        Verifies that:
        1. Field list is correctly stored in the select property
        2. Method returns self for fluent interface chaining
        3. Field names are preserved exactly as provided
        """
        fields = ["field1", "field2", "field3"]
        result = self.builder.with_select(fields)
        assert result is self.builder
        assert self.builder.select == fields

    def test_with_filter(self):
        """
        Test the with_filter method for OData $filter parameter handling.
        
        Verifies that:
        1. Filter expressions are correctly stored
        2. Special characters are properly escaped (& becomes %26, ` becomes ')
        3. Method returns self for fluent interface chaining
        4. URL encoding is applied for safe transmission
        """
        filter_expr = "field1 eq 'value1' & field2 ne `test`"
        expected = "field1 eq 'value1' %26 field2 ne 'test'"
        result = self.builder.with_filter(filter_expr)
        assert result is self.builder
        assert self.builder.filter == expected

    def test_with_order_by(self):
        """
        Test the with_order_by method for OData $orderby parameter.
        
        Verifies that:
        1. Order expressions are correctly stored
        2. Method returns self for fluent interface chaining
        3. Both field names and sort directions (asc/desc) are preserved
        """
        order_expr = "field1 desc"
        result = self.builder.with_order_by(order_expr)
        assert result is self.builder
        assert self.builder.orderby == order_expr

    def test_with_top(self):
        """
        Test the with_top method for OData $top parameter (result limiting).
        
        Verifies that:
        1. Limit values are correctly stored
        2. Method returns self for fluent interface chaining
        3. Numeric limits are preserved for pagination control
        """
        limit = 100
        result = self.builder.with_top(limit)
        assert result is self.builder
        assert self.builder.top == limit

    def test_with_aggregation(self):
        """
        Test the with_aggregation method for OData $apply parameter construction.
        
        Verifies that:
        1. Group by fields are correctly formatted in the apply expression
        2. Aggregation operations (sum, count, etc.) are properly structured
        3. Field aliases are correctly applied
        4. Complex groupby/aggregate syntax is generated correctly
        5. Method returns self for fluent interface chaining
        """
        group_by_fields = ["company_name", "id_company"]
        aggregations = [
            {"field": "asset_count", "operation": "sum", "alias": "total_assets"},
            {"field": "field_name", "operation": "countdistinct", "alias": "unique_fields"},
        ]
        result = self.builder.with_aggregation(group_by_fields, aggregations)
        expected_apply = (
            "groupby((company_name,id_company),"
            "aggregate(asset_count with sum as total_assets,"
            "field_name with countdistinct as unique_fields))"
        )
        assert result is self.builder
        assert self.builder.apply == expected_apply

    def test_build_url_empty(self):
        """
        Test URL building with no parameters.
        
        Verifies that:
        1. Base URL is returned unchanged when no parameters are set
        2. No query string is appended for empty queries
        """
        url = self.builder.build_url()
        assert url == self.base_url

    def test_build_url_with_select(self):
        """
        Test URL building with $select parameter.
        
        Verifies that:
        1. $select parameter is correctly appended to URL
        2. Multiple fields are properly comma-separated
        3. Query string syntax is correct
        """
        self.builder.with_select(["field1", "field2"])
        url = self.builder.build_url()
        assert url == f"{self.base_url}?$select=field1,field2"

    def test_build_url_with_filter(self):
        """
        Test URL building with $filter parameter.
        
        Verifies that:
        1. $filter parameter is correctly appended to URL
        2. Filter expressions are properly encoded
        3. Query string syntax is correct
        """
        self.builder.with_filter("field1 eq 'value1'")
        url = self.builder.build_url()
        assert url == f"{self.base_url}?$filter=field1 eq 'value1'"

    def test_build_url_with_all_params(self):
        """
        Test URL building with multiple OData parameters.
        
        Verifies that:
        1. All OData parameters ($select, $filter, $orderby, $top) are included
        2. Parameters are properly joined with & separators
        3. Parameter order and formatting is correct
        4. Complex queries are built correctly
        """
        self.builder.with_select(["field1", "field2"]) \
                   .with_filter("field1 eq 'value1'") \
                   .with_order_by("field1 desc") \
                   .with_top(50)
        
        url = self.builder.build_url()
        expected_params = [
            "$select=field1,field2",
            "$filter=field1 eq 'value1'",
            "$orderby=field1 desc",
            "$top=50"
        ]
        for param in expected_params:
            assert param in url

    @patch('lens_direct_utils.requests.get')
    def test_request_success(self, mock_get):
        """
        Test successful HTTP request execution.
        
        Verifies that:
        1. HTTP GET requests are made with correct URL and headers
        2. API key is properly included in request headers
        3. Successful responses (200 OK) return parsed JSON data
        4. Response structure matches expected OData format
        """
        mock_response = Mock()
        mock_response.status_code = 200
        mock_response.json.return_value = {"value": [{"id": 1}]}
        mock_get.return_value = mock_response

        result = self.builder._request("http://test.com")
        
        assert result == {"value": [{"id": 1}]}
        mock_get.assert_called_once_with("http://test.com", headers={"apiKey": self.api_key})

    @patch('lens_direct_utils.requests.get')
    @patch('lens_direct_utils.time.sleep')
    def test_request_retry_success(self, mock_sleep, mock_get):
        """
        Test HTTP request retry mechanism for recoverable errors.
        
        Verifies that:
        1. Retryable status codes (500, 502, 503, 504, 429) trigger retry logic
        2. Exponential backoff is implemented correctly (2^attempt seconds)
        3. Successful retry returns the correct result
        4. Sleep delays are applied between retry attempts
        5. Request count matches expected retry attempts
        """
        # First call fails, second succeeds
        mock_response_fail = Mock()
        mock_response_fail.status_code = 500
        mock_response_success = Mock()
        mock_response_success.status_code = 200
        mock_response_success.json.return_value = {"value": [{"id": 1}]}
        
        mock_get.side_effect = [mock_response_fail, mock_response_success]

        result = self.builder._request("http://test.com", max_retries=1)
        
        assert result == {"value": [{"id": 1}]}
        assert mock_get.call_count == 2
        mock_sleep.assert_called_once_with(1)  # 2^0 = 1 second wait

    @patch('lens_direct_utils.requests.get')
    def test_request_http_error_no_retry(self, mock_get):
        """
        Test HTTP error handling for non-retryable errors.
        
        Verifies that:
        1. Non-retryable status codes (404, 400, 401, etc.) raise exceptions immediately
        2. HTTPError exceptions are properly propagated
        3. No retry attempts are made for non-retryable errors
        4. Error messages are preserved for debugging
        """
        mock_response = Mock()
        mock_response.status_code = 404
        mock_response.raise_for_status.side_effect = HTTPError("Not found")
        mock_get.return_value = mock_response

        with pytest.raises(HTTPError):
            self.builder._request("http://test.com")

    @patch('lens_direct_utils.requests.get')
    def test_request_network_error_retry_exhausted(self, mock_get):
        """
        Test network error handling when all retries are exhausted.
        
        Verifies that:
        1. Network errors (connection failures, timeouts) are caught
        2. Retry logic is applied for network errors
        3. Original exception is re-raised when retries are exhausted
        4. RequestException types are properly handled
        """
        mock_get.side_effect = requests.exceptions.RequestException("Network error")

        with pytest.raises(requests.exceptions.RequestException):
            self.builder._request("http://test.com", max_retries=1)

    @patch.object(LensDirectQueryBuilder, '_request')
    def test_execute_single_page(self, mock_request):
        """
        Test single-page query execution.
        
        Verifies that:
        1. Single-page responses are handled correctly
        2. Data extraction from OData "value" property works
        3. No pagination logic is triggered for single-page results
        4. Request method is called exactly once
        """
        mock_request.return_value = {"value": [{"id": 1}, {"id": 2}]}
        
        result = self.builder.execute()
        
        assert result == [{"id": 1}, {"id": 2}]
        mock_request.assert_called_once()

    @patch.object(LensDirectQueryBuilder, '_request')
    def test_execute_multiple_pages(self, mock_request):
        """
        Test multi-page query execution with OData pagination.
        
        Verifies that:
        1. @odata.nextLink URLs are followed automatically
        2. Data from all pages is aggregated correctly
        3. Pagination continues until no nextLink is present
        4. Request count matches the number of pages
        5. Data ordering is preserved across pages
        """
        page1_data = {
            "value": [{"id": 1}, {"id": 2}],
            "@odata.nextLink": "http://test.com/page2"
        }
        page2_data = {
            "value": [{"id": 3}, {"id": 4}],
            "@odata.nextLink": "http://test.com/page3"
        }
        page3_data = {
            "value": [{"id": 5}]
        }
        
        mock_request.side_effect = [page1_data, page2_data, page3_data]
        
        result = self.builder.execute()
        
        assert result == [{"id": 1}, {"id": 2}, {"id": 3}, {"id": 4}, {"id": 5}]
        assert mock_request.call_count == 3


class TestEnvironmentFunctions:
    """
    Test suite for environment variable configuration functions.
    
    Tests the retrieval and validation of required environment variables
    for Lens Direct API access, including proper error handling for
    missing or invalid configurations.
    """
    
    @patch.dict(os.environ, {'LENS_DIRECT_URL': 'https://api.test.com', 'LENS_DIRECT_API_KEY': 'test_key'})
    def test_get_lens_direct_url_and_token_success(self):
        """
        Test successful retrieval of environment variables.
        
        Verifies that:
        1. LENS_DIRECT_URL environment variable is correctly retrieved
        2. LENS_DIRECT_API_KEY environment variable is correctly retrieved
        3. Both values are returned as a tuple in the correct order
        4. No exceptions are raised for valid configuration
        """
        url, key = get_lens_direct_url_and_token()
        assert url == 'https://api.test.com'
        assert key == 'test_key'

    @patch.dict(os.environ, {}, clear=True)
    def test_get_lens_direct_url_and_token_missing_url(self):
        """
        Test error handling for missing LENS_DIRECT_URL environment variable.
        
        Verifies that:
        1. ValueError is raised when LENS_DIRECT_URL is not set
        2. Error message clearly identifies the missing variable
        3. Function fails fast with descriptive error message
        """
        with pytest.raises(ValueError, match="LENS_DIRECT_URL environment variable is not set"):
            get_lens_direct_url_and_token()

    @patch.dict(os.environ, {'LENS_DIRECT_URL': 'https://api.test.com'}, clear=True)
    def test_get_lens_direct_url_and_token_missing_key(self):
        """
        Test error handling for missing LENS_DIRECT_API_KEY environment variable.
        
        Verifies that:
        1. ValueError is raised when LENS_DIRECT_API_KEY is not set
        2. Error message clearly identifies the missing variable
        3. Function fails fast even when URL is present but key is missing
        4. Both environment variables are required for successful execution
        """
        with pytest.raises(ValueError, match="LENS_DIRECT_API_KEY environment variable is not set"):
            get_lens_direct_url_and_token()
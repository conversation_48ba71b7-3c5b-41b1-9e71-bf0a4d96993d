import { start } from '@splunk/otel';
import { HttpInstrumentation } from '@opentelemetry/instrumentation-http';

const EXCLUDED_URLS = new Set(['/health', '/favicon.ico', '/']);

if (process.env.OTEL_SERVICE_NAME) {
    start({
        tracing: {
            instrumentations: [
                new HttpInstrumentation({
                    ignoreIncomingRequestHook: (req) => {
                        return (
                            EXCLUDED_URLS.has(req.url ?? '') ||
                            req.method === 'OPTIONS'
                        );
                    },
                    requestHook: (span, request) => {
                        if (request.method && 'url' in request) {
                            const url = new URL(
                                `${request.url}`,
                                'http://localhost',
                            );
                            // Update the span name based on the request URL
                            span.updateName(url.pathname);

                            // Add custom attributes based on the request URL
                            if (url.pathname === '/ask-chatbot') {
                                const params = url.searchParams;
                                const sessionId = params.get('sessionId');
                                if (sessionId) {
                                    span.setAttribute('sessionId', sessionId);
                                }
                            }
                        }
                    },
                }),
            ],
        },
    });
}

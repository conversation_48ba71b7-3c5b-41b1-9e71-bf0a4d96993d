import { oktaClient } from "../security/okta-client";

const dialogOptions = {
    height: 75,
    width: 40,
    displayInIframe: false,
    promptBeforeOpen: false,
}

const dialogUrl = `${window.location.origin}${window.location.pathname.includes("wmlgv-assistant") ? "/wmlgv-assistant" : ""}/login.html`;

const setTokensFromMessage = (dialog: Office.Dialog) => (args : { message: string, origin?: string } | { error: number }) => {
    const { message } = args as { message: string, origin?: string };
    
    if (message) {
        const tokens = JSON.parse(message);
        oktaClient.tokenManager.setTokens(tokens);
    }
    
    dialog.close();
};

const displayLoginDialog = () => {
    Office.context.ui.displayDialogAsync(dialogUrl, {...dialogOptions }, ({ value: dialog }) => {
        dialog.addEventHandler(Office.EventType.DialogMessageReceived, setTokensFromMessage(dialog));
    });
};

export { displayLoginDialog };

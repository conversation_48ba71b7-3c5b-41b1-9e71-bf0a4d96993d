import React from "react";
import { render, screen } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { UserMessage } from "../UserMessage";

jest.mock("../Message", () => ({
  Message: ({
    message,
    cardClassName,
    containerClassName,
  }: {
    message: string;
    cardClassName: string;
    containerClassName: string;
  }) => (
    <div data-testid="message" data-card-class={cardClassName} data-container-class={containerClassName}>
      {message}
    </div>
  ),
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("UserMessage", () => {
  it("renders message using Message component", () => {
    renderWithProvider(<UserMessage message="Hello world!" />);

    expect(screen.getByTestId("message")).toBeInTheDocument();
    expect(screen.getByText("Hello world!")).toBeInTheDocument();
  });

  it("passes correct styling classes to Message component", () => {
    renderWithProvider(<UserMessage message="Test message" />);

    const messageElement = screen.getByTestId("message");
    expect(messageElement).toHaveAttribute("data-card-class");
    expect(messageElement).toHaveAttribute("data-container-class");
  });

  it("handles empty message", () => {
    renderWithProvider(<UserMessage message="" />);

    expect(screen.getByTestId("message")).toBeInTheDocument();
  });

  it("handles long message", () => {
    const longMessage =
      "This is a very long message that should be handled properly by the UserMessage component without any issues";
    renderWithProvider(<UserMessage message={longMessage} />);

    expect(screen.getByText(longMessage)).toBeInTheDocument();
  });
});

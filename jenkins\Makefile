AWS_REGION ?= us-east-1
PROJECT_PREFIX ?= wmlgv-assistant
PROJECT_CODE = DEV-VAL-SERV

STACK_NAME ?= jenkins-deployment-role-$(PROJECT_PREFIX)-$(AWS_ENVIRONMENT)
AWS_ENVIRONMENT ?= aidev
CONTACT ?= <EMAIL>
BASE_AGENT_POLICY_ARN ?= /${AWS_ENVIRONMENT}/liveservices/jenkins-ecs-agents/base-jenkins-agent-policy-arn
VALUATION_ENVIRONMENT ?= dev

target:
	$(info $(HELP_MESSAGE))
	@exit 0

lint: ## Run cfn-lint
	@echo "Lint CF Templates"
	@cfn-lint $(if $(TEMPLATE_FILE),$(TEMPLATE_FILE),../infra/*.yaml) -I
	@echo "Linting for CF Templates Successful"

deploy-role: ## Deploy CF Template
	@echo "Deploy Policy CF Template to $(AWS_ENVIRONMENT) $(AWS_REGION)"
	@aws cloudformation deploy \
		--region $(AWS_REGION) \
		--template-file ${TEMPLATE_FILE} \
		--stack-name $(STACK_NAME) \
		--no-fail-on-empty-changeset \
		--capabilities CAPABILITY_IAM CAPABILITY_NAMED_IAM \
		--parameter-overrides \
			"Environment=$(AWS_ENVIRONMENT)" \
			"BaseJenkinsAgentPolicyArn=$(BASE_AGENT_POLICY_ARN)" \
		--tags \
			"Contact=$(CONTACT)" \
			"Environment=$(AWS_ENVIRONMENT)" \
			"Name=$(STACK_NAME)" \
			"BusinessUnit=Woodmac" \
			"ProjectCode=$(PROJECT_CODE)"

deploy: ## Deploy CF Template
	@echo "Deploy Policy CF Template to $(AWS_ENVIRONMENT) $(AWS_REGION)"
	@aws cloudformation deploy \
		--region $(AWS_REGION) \
		--template-file ${TEMPLATE_FILE} \
		--stack-name $(STACK_NAME) \
		--no-fail-on-empty-changeset \
		--capabilities CAPABILITY_IAM CAPABILITY_NAMED_IAM \
		--parameter-overrides \
			"Environment=$(AWS_ENVIRONMENT)" \
			"ValuationEnvironment=$(VALUATION_ENVIRONMENT)" \
            $(EXTRA_PARAMS) \

help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'

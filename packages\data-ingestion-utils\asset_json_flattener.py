import json
import math
import os
import pandas as pd
from typing import List, Union, Optional, Callable, Dict, Any
from pathlib import Path
from datetime import datetime
from utils import to_snake_case
from functools import lru_cache
from lens_direct_metadata_provider import LensDirectMetadataProvider


SUPPORTED_REPORTS = [
    "Asset Summary Discount Rate 6",
    "Standard Cash Flow",
    # "Entitlement Cash Flow",
    # "Expanded Cash Flow",
    # "Pre-Tax Cash Flow",
    # "Summary Cash Flow",
]

SUPPORTED_ASSET_TYPES = [
    "FIELD",  # fields + "integrated" lng plants
    "PLANT",  # lng plants
    "TRANSPORT",  # pipelines-ish
    "PLAY_COMPANY_GEM",  # basins/plays-ish
]
SUPPORTED_ASSET_TYPES_SET = set(SUPPORTED_ASSET_TYPES)


@lru_cache(maxsize=300)
def cached_to_snake_case(name):
    return to_snake_case(name)


def value_without_invalid_values(value):
    if isinstance(value, str):
        if value == "No Production" or value == "INVALID_CELL_VALUE":
            return None
    return value


def update_static_column_types(df: pd.DataFrame) -> pd.DataFrame:
    df['country'] = df['country'].astype('category')
    df['business_area'] = df['business_area'].astype('category')
    df['regime'] = df['regime'].astype('category')
    df['valuation_type'] = df['valuation_type'].astype('category')
    df['price_scenario'] = df['price_scenario'].astype('category')
    df['asset_type'] = df['asset_type'].astype('category')

    if 'year' in df.columns:
        df['year'] = df['year'].astype('uint16')

    return df


def flatten_discrete_metrics(
    countries,
    company_name: str = None,
    scenario_name: str = None,
    asset_info_getter=None,
    lens_direct_metadata_provider: LensDirectMetadataProvider = None,
):
    data = []

    for country in countries:
        country_name = country["name"]
        for business_area in country["businessAreas"]:
            business_area_name = business_area["name"]
            for regime in business_area["regimes"]:
                regime_name = regime["name"]
                for asset in regime["assets"]:
                    asset_name = asset["name"]

                    asset_type, asset_id = (
                        asset_info_getter(
                            asset_name=asset_name,
                            country_name=country_name,
                            regime_name=regime_name,
                        )
                        if asset_info_getter
                        else ("unknown", None)
                    )
                    if asset_type not in SUPPORTED_ASSET_TYPES_SET:
                        continue

                    for valuation in asset["valuations"]:
                        valuation_type = valuation["type"]
                        for scenario in valuation["scenarios"]:
                            scenario_name_to_use = scenario_name or scenario["name"]

                            base_record = {
                                "country": country_name,
                                "business_area": business_area_name,
                                "regime": regime_name,
                                "asset_name": asset_name,
                                "asset_type": asset_type,
                                "asset_id": asset_id,
                                "valuation_type": valuation_type,
                                "price_scenario": scenario_name_to_use,
                            }

                            if company_name:
                                base_record["company"] = company_name
                                if lens_direct_metadata_provider:
                                    company_metadata = lens_direct_metadata_provider.get_company_asset_metadata(
                                        asset_id=asset_id,
                                        company_name=company_name,
                                        asset_type=asset_type,
                                    )
                                    if company_metadata:
                                        base_record.update(company_metadata) 

                            if scenario["result"]["reports"]:
                                for report in scenario["result"]["reports"]:
                                    if (
                                        "discreteMetrics" in report
                                        and report["name"] in SUPPORTED_REPORTS
                                    ):
                                        for k, v in report["discreteMetrics"].items():
                                            base_record[cached_to_snake_case(k)] = (
                                                value_without_invalid_values(v)
                                            )
                            
                            if lens_direct_metadata_provider:
                                asset_metadata = lens_direct_metadata_provider.get_asset_metadata(
                                    asset_id=asset_id,
                                    asset_type=asset_type,
                                )
                                if asset_metadata:
                                    base_record.update(asset_metadata)

                            data.append(base_record)

    if not data:
        return pd.DataFrame()

    return update_static_column_types(pd.DataFrame(data))


def extract_values_from_repeat_pattern(values: list | dict, start_year: int) -> dict:
    result = {}
    if isinstance(values, dict) and "data" in values and "repeat" in values:
        value = value_without_invalid_values(values["data"])
        for i in range(values["repeat"]):
            result[start_year + i] = value
        return result

    if isinstance(values, list):
        current_index = 0
        for item in values:
            if isinstance(item, dict) and "data" in item and "repeat" in item:
                value = value_without_invalid_values(item["data"])
                for i in range(item["repeat"]):
                    result[start_year + current_index + i] = value
                current_index += item["repeat"]
            else:
                result[start_year + current_index] = value_without_invalid_values(item)
                current_index += 1
        return result

    raise ValueError(
        '"values" is neither a list nor a dict with "data" and "repeat" keys'
    )


def flatten_timeseries_metrics(
    countries,
    company_name: str = None,
    scenario_name: str = None,
    asset_info_getter=None,
    lens_direct_metadata_provider: LensDirectMetadataProvider = None,
):
    data = []

    for country in countries:
        country_name = country["name"]
        for business_area in country["businessAreas"]:
            business_area_name = business_area["name"]
            for regime in business_area["regimes"]:
                regime_name = regime["name"]
                for asset in regime["assets"]:
                    asset_name = asset["name"]

                    asset_type, asset_id = (
                        asset_info_getter(
                            asset_name=asset_name,
                            country_name=country_name,
                            regime_name=regime_name,
                        )
                        if asset_info_getter
                        else ("unknown", None)
                    )
                    if asset_type not in SUPPORTED_ASSET_TYPES_SET:
                        continue

                    asset_metadata = {}
                    if lens_direct_metadata_provider:
                                asset_metadata = lens_direct_metadata_provider.get_asset_metadata(
                                    asset_id=asset_id,
                                    asset_type=asset_type,
                                )
            
                    for valuation in asset["valuations"]:
                        valuation_type = valuation["type"]

                        for scenario in valuation["scenarios"]:
                            scenario_name_to_use = scenario_name or scenario["name"]

                            if not scenario["result"]["reports"]:
                                continue

                            metric_values = {}
                            for report in scenario["result"]["reports"]:
                                if (
                                    "continuousMetrics" in report
                                    and report["name"] in SUPPORTED_REPORTS
                                    and "Year" in report["continuousMetrics"]
                                ):
                                    continuous_metrics = report["continuousMetrics"]
                                    years = [int(y) for y in continuous_metrics["Year"]]

                                    for (
                                        metric_name,
                                        values,
                                    ) in continuous_metrics.items():
                                        if metric_name == "Year":
                                            continue
                                        metric_values[metric_name] = (
                                            extract_values_from_repeat_pattern(
                                                values, start_year=years[0]
                                            )
                                        )

                            for report in scenario["result"]["reports"]:
                                if (
                                    "continuousMetrics" in report
                                    and report["name"] in SUPPORTED_REPORTS
                                    and "Year" in report["continuousMetrics"]
                                ):
                                    continuous_metrics = report["continuousMetrics"]
                                    years = [int(y) for y in continuous_metrics["Year"]]

                                    company_metadata = {}
                                    if company_name and lens_direct_metadata_provider:
                                        company_metadata = lens_direct_metadata_provider.get_company_asset_metadata(
                                            asset_id=asset_id,
                                            company_name=company_name,
                                            asset_type=asset_type,
                                        )

                                    for year in years:
                                        base_data = {
                                            "country": country_name,
                                            "business_area": business_area_name,
                                            "regime": regime_name,
                                            "asset_name": asset_name,
                                            "asset_type": asset_type,
                                            "asset_id": asset_id,
                                            "valuation_type": valuation_type,
                                            "price_scenario": scenario_name_to_use,
                                            "year": year,
                                        }

                                        if company_name:
                                            base_data["company"] = company_name
                                            if company_metadata:
                                                base_data.update(company_metadata) 

                                        for metric_name in metric_values.keys():
                                            base_data[
                                                cached_to_snake_case(metric_name)
                                            ] = metric_values[metric_name][year]

                                        if asset_metadata:
                                            base_data.update(asset_metadata)

                                        data.append(base_data)

    if not data:
        return pd.DataFrame()

    return update_static_column_types(pd.DataFrame(data))


def _process_json_data(
    json_data: Dict[str, Any],
    metrics_function: Callable,
    scenario_name: Optional[str] = None,
    asset_info_getter=None,
    lens_direct_metadata_provider: LensDirectMetadataProvider = None,
) -> pd.DataFrame:
    """Process JSON data and return a DataFrame using the provided metrics function"""
    if "body" in json_data:
        json_data = json_data["body"]

    dfs = []
    if "companies" in json_data:
        for company in json_data["companies"]:
            result = metrics_function(
                company["countries"],
                company["name"],
                scenario_name=scenario_name,
                asset_info_getter=asset_info_getter,
                lens_direct_metadata_provider=lens_direct_metadata_provider,
            )
            if isinstance(result, pd.DataFrame) and not result.empty:
                dfs.append(result)
    else:
        result = metrics_function(
            json_data["countries"],
            scenario_name=scenario_name,
            asset_info_getter=asset_info_getter,
            lens_direct_metadata_provider=lens_direct_metadata_provider,
        )
        if isinstance(result, pd.DataFrame) and not result.empty:
            dfs.append(result)

    if dfs:
        return pd.concat(dfs, ignore_index=True)

    return pd.DataFrame()


def _save_to_format(df: pd.DataFrame, output_file: str, format_type: str):
    """Save DataFrame to specified format"""
    if df.empty:
        return None

    if format_type == "json":
        with open(output_file, "w") as f:
            df.to_json(f, orient="records", indent=2)
        return df.to_dict("records")
    elif format_type == "csv":
        df.to_csv(output_file, index=False)
        return df
    elif format_type == "parquet":
        df.to_parquet(output_file)
        return df


def flatten_discrete_json(
    json_data, output_file=None, scenario_name: str = None, asset_info_getter=None, lens_direct_metadata_provider=None
):
    df = _process_json_data(
        json_data, flatten_discrete_metrics, scenario_name, asset_info_getter, lens_direct_metadata_provider
    )

    if output_file and not df.empty:
        return _save_to_format(df, output_file, "json")

    return df.to_dict("records") if not df.empty else []


def flatten_discrete_csv(
    json_data, output_file, scenario_name: str = None, asset_info_getter=None, lens_direct_metadata_provider=None
) -> None:
    df = _process_json_data(
        json_data, flatten_discrete_metrics, scenario_name, asset_info_getter, lens_direct_metadata_provider
    )
    return _save_to_format(df, output_file, "csv")


def flatten_discrete_parquet(
    json_data, output_file, scenario_name: str = None, asset_info_getter=None, lens_direct_metadata_provider=None
) -> None:
    df = _process_json_data(
        json_data, flatten_discrete_metrics, scenario_name, asset_info_getter, lens_direct_metadata_provider
    )
    return _save_to_format(df, output_file, "parquet")


def flatten_timeseries_json(
    json_data, output_file=None, scenario_name: str = None, asset_info_getter=None, lens_direct_metadata_provider=None
):
    df = _process_json_data(
        json_data, flatten_timeseries_metrics, scenario_name, asset_info_getter, lens_direct_metadata_provider
    )

    if output_file and not df.empty:
        return _save_to_format(df, output_file, "json")

    return df.to_dict("records") if not df.empty else []


def flatten_timeseries_csv(
    json_data, output_file, scenario_name: str = None, asset_info_getter=None, lens_direct_metadata_provider=None
) -> None:
    df = _process_json_data(
        json_data, flatten_timeseries_metrics, scenario_name, asset_info_getter, lens_direct_metadata_provider
    )
    return _save_to_format(df, output_file, "csv")


def flatten_timeseries_parquet(
    json_data, output_file, scenario_name: str = None, asset_info_getter=None, lens_direct_metadata_provider=None
) -> None:
    df = _process_json_data(
        json_data, flatten_timeseries_metrics, scenario_name, asset_info_getter, lens_direct_metadata_provider
    )
    return _save_to_format(df, output_file, "parquet")


def flatt_all_json_dir(dir_path, asset_info_getter=None, lens_direct_metadata_provider=None):
    for file in os.listdir(dir_path):
        if file.endswith(".json"):
            with open(file, "r") as f:
                json_data = json.load(f)
            flatten_discrete_csv(
                json_data,
                f"{file.strip('.json')}.csv",
                asset_info_getter=asset_info_getter,
                lens_direct_metadata_provider=lens_direct_metadata_provider,
            )


def combine_parquet_files(
    file_paths: List[str],
    output_path: Union[str, Path],
    include_timestamp: bool = True,
) -> None:
    if not file_paths:
        raise ValueError("No input files provided")

    input_paths = [Path(p) for p in file_paths]
    for path in input_paths:
        if not path.exists():
            raise FileNotFoundError(f"File not found: {path}")

    # this is much faster but requires a lot of memory
    dfs = [pd.read_parquet(str(path)) for path in input_paths]
    combined_df = pd.concat(dfs, ignore_index=True)

    # slower but can fit in a normal agent's memory
    # combined_df = pd.read_parquet(str(input_paths[0]))
    # for path in input_paths[1:]:
    #     df = pd.read_parquet(str(path))
    #     combined_df = pd.concat([combined_df, df], ignore_index=True)

    index_cols = ["country", "business_area", "regime", "asset_name", "year"] if "year" in combined_df.columns else ["country", "business_area", "regime", "asset_name"]
    combined_df.sort_values(index_cols, ignore_index=True, inplace=True)

    if include_timestamp:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = Path(f"output/{output_path}_combined_{timestamp}.parquet")
    else:
        output_file = Path(f"output/{output_path}_combined.parquet")

    combined_df.to_parquet(
        output_file,
        row_group_offsets=math.ceil(combined_df.size / (combined_df.columns.size * 100)),
        engine="fastparquet",
        compression="zstd",
    )

    return output_file

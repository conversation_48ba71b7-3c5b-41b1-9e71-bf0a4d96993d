{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["## login to lens to get a new token ##\n", "TOKEN=\"{YOUR_IDENTITY_TOKEN}\"\n", "ld_url = 'https://data.dev.woodmac.com'\n", "cookies = {'iPlanetDirectoryPro': TOKEN}\n"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [], "source": ["import requests\n", "\n", "response = requests.get(f'{ld_url}/registry/$registry?includeResources=true', cookies=cookies)\n", "registry_entries = response.json()"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [{"data": {"text/plain": ["'ld_markets_dev_2025_02_10_14_29_40'"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["registry_dict = {entry['name']: entry for entry in registry_entries}\n", "markets_table = registry_dict['markets']['database']\n", "markets_sub_tables = [table['name'] for table in registry_dict['markets']['resources']]\n", "markets_sub_tables\n", "markets_table"]}, {"cell_type": "code", "execution_count": 34, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Tables in database ld_markets_dev_2025_02_10_14_29_40:\n", "- brm_lithium_demand_product__kt_lce\n", "- brm_lithium_demand_sector_product__kt_lce\n", "- brm_lithium_price_annual__usd_t\n", "- coal_demand_annual__mt\n", "- coal_imports_exports_annual__mt\n", "- coal_price_annual__usd_t\n", "- coal_supply_annual__mt\n", "- copper_china_market_balance\n", "- copper_combined_tcrc_monthly\n", "- copper_conc_blister_stock_change__kt\n", "- copper_global_market_balance\n", "- copper_premia_monthly\n", "- copper_semis_production__kt\n", "- cumulative_charging_ports\n", "- gas_balance_annual__bcf_386\n", "- gas_balance_annual__bcf_40\n", "- gas_balance_annual__bcfd_386\n", "- gas_balance_annual__bcfd_40\n", "- gas_balance_annual__bcm_386\n", "- gas_balance_annual__bcm_40\n", "- gas_balance_annual__mcf_386\n", "- gas_balance_annual__mcf_40\n", "- gas_balance_annual__mcm_386\n", "- gas_balance_annual__mcm_40\n", "- gas_balance_annual__mmcf_386\n", "- gas_balance_annual__mmcf_40\n", "- gas_balance_annual__mmcfd_386\n", "- gas_balance_annual__mmcfd_40\n", "- gas_balance_annual__tcf_386\n", "- gas_balance_annual__tcf_40\n", "- gas_balance_annual__tj\n", "- gas_balance_monthly__bcf_386\n", "- gas_balance_monthly__bcf_40\n", "- gas_balance_monthly__bcfd_386\n", "- gas_balance_monthly__bcfd_40\n", "- gas_balance_monthly__bcm_386\n", "- gas_balance_monthly__bcm_40\n", "- gas_balance_monthly__mcf_386\n", "- gas_balance_monthly__mcf_40\n", "- gas_balance_monthly__mcm_386\n", "- gas_balance_monthly__mcm_40\n", "- gas_balance_monthly__mmcf_386\n", "- gas_balance_monthly__mmcf_40\n", "- gas_balance_monthly__mmcfd_386\n", "- gas_balance_monthly__mmcfd_40\n", "- gas_balance_monthly__tcf_386\n", "- gas_balance_monthly__tcf_40\n", "- gas_balance_monthly__tj\n", "- gas_balance_quarterly__bcf_386\n", "- gas_balance_quarterly__bcf_40\n", "- gas_balance_quarterly__bcfd_386\n", "- gas_balance_quarterly__bcfd_40\n", "- gas_balance_quarterly__bcm_386\n", "- gas_balance_quarterly__bcm_40\n", "- gas_balance_quarterly__mcf_386\n", "- gas_balance_quarterly__mcf_40\n", "- gas_balance_quarterly__mcm_386\n", "- gas_balance_quarterly__mcm_40\n", "- gas_balance_quarterly__mmcf_386\n", "- gas_balance_quarterly__mmcf_40\n", "- gas_balance_quarterly__mmcfd_386\n", "- gas_balance_quarterly__mmcfd_40\n", "- gas_balance_quarterly__tcf_386\n", "- gas_balance_quarterly__tcf_40\n", "- gas_balance_quarterly__tj\n", "- gas_demand_annual__bcf_386\n", "- gas_demand_annual__bcf_40\n", "- gas_demand_annual__bcfd_386\n", "- gas_demand_annual__bcfd_40\n", "- gas_demand_annual__bcm_386\n", "- gas_demand_annual__bcm_40\n", "- gas_demand_annual__mcf_386\n", "- gas_demand_annual__mcf_40\n", "- gas_demand_annual__mcm_386\n", "- gas_demand_annual__mcm_40\n", "- gas_demand_annual__mmcf_386\n", "- gas_demand_annual__mmcf_40\n", "- gas_demand_annual__mmcfd_386\n", "- gas_demand_annual__mmcfd_40\n", "- gas_demand_annual__tcf_386\n", "- gas_demand_annual__tcf_40\n", "- gas_demand_annual__tj\n", "- gas_demand_monthly__bcf_386\n", "- gas_demand_monthly__bcf_40\n", "- gas_demand_monthly__bcfd_386\n", "- gas_demand_monthly__bcfd_40\n", "- gas_demand_monthly__bcm_386\n", "- gas_demand_monthly__bcm_40\n", "- gas_demand_monthly__mcf_386\n", "- gas_demand_monthly__mcf_40\n", "- gas_demand_monthly__mcm_386\n", "- gas_demand_monthly__mcm_40\n", "- gas_demand_monthly__mmcf_386\n", "- gas_demand_monthly__mmcf_40\n", "- gas_demand_monthly__mmcfd_386\n", "- gas_demand_monthly__mmcfd_40\n", "- gas_demand_monthly__tcf_386\n", "- gas_demand_monthly__tcf_40\n", "- gas_demand_monthly__tj\n", "- gas_demand_quarterly__bcf_386\n"]}], "source": ["import boto3\n", "\n", "glue_client = boto3.client('glue')\n", "\n", "# Get all tables in the markets database\n", "try:\n", "    response = glue_client.get_tables(\n", "        DatabaseName=markets_table\n", "    )\n", "    \n", "    # Print table names\n", "    tables = [table['Name'] for table in response['TableList']]\n", "    print(f\"Tables in database {markets_table}:\")\n", "    for table in tables:\n", "        print(f\"- {table}\")\n", "        \n", "except Exception as e:\n", "    print(f\"Error getting tables: {str(e)}\")"]}, {"cell_type": "code", "execution_count": 40, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded copper_china_market_balance.parquet to tables/markets/copper_china_market_balance/copper_china_market_balance.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__mcf_386.parquet to tables/markets/gas_balance_quarterly__mcf_386/gas_balance_quarterly__mcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__bcm_40.parquet to tables/markets/gas_demand_annual__bcm_40/gas_demand_annual__bcm_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__mmcfd_40.parquet to tables/markets/gas_demand_monthly__mmcfd_40/gas_demand_monthly__mmcfd_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__tcf_40.parquet to tables/markets/gas_demand_monthly__tcf_40/gas_demand_monthly__tcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__bcm_40.parquet to tables/markets/gas_balance_annual__bcm_40/gas_balance_annual__bcm_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded copper_semis_production__kt.parquet to tables/markets/copper_semis_production__kt/copper_semis_production__kt.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__mcm_386.parquet to tables/markets/gas_balance_quarterly__mcm_386/gas_balance_quarterly__mcm_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__mmcf_386.parquet to tables/markets/gas_balance_annual__mmcf_386/gas_balance_annual__mmcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__bcfd_40.parquet to tables/markets/gas_balance_annual__bcfd_40/gas_balance_annual__bcfd_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded coal_supply_annual__mt.parquet to tables/markets/coal_supply_annual__mt/coal_supply_annual__mt.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__tj.parquet to tables/markets/gas_balance_quarterly__tj/gas_balance_quarterly__tj.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__bcfd_386.parquet to tables/markets/gas_demand_monthly__bcfd_386/gas_demand_monthly__bcfd_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__tj.parquet to tables/markets/gas_demand_annual__tj/gas_demand_annual__tj.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded cumulative_charging_ports.parquet to tables/markets/cumulative_charging_ports/cumulative_charging_ports.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__mmcf_40.parquet to tables/markets/gas_balance_monthly__mmcf_40/gas_balance_monthly__mmcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_quarterly__bcf_386.parquet to tables/markets/gas_demand_quarterly__bcf_386/gas_demand_quarterly__bcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded metals_global_market_balance.parquet to tables/markets/copper_global_market_balance/metals_global_market_balance.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__mmcfd_386.parquet to tables/markets/gas_balance_quarterly__mmcfd_386/gas_balance_quarterly__mmcfd_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__tj.parquet to tables/markets/gas_balance_annual__tj/gas_balance_annual__tj.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__mcm_40.parquet to tables/markets/gas_demand_monthly__mcm_40/gas_demand_monthly__mcm_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__mmcf_386.parquet to tables/markets/gas_balance_quarterly__mmcf_386/gas_balance_quarterly__mmcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded lme_average_copper_cash_price.parquet to tables/markets/copper_combined_tcrc_monthly/lme_average_copper_cash_price.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__mmcf_40.parquet to tables/markets/gas_balance_annual__mmcf_40/gas_balance_annual__mmcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__mcf_386.parquet to tables/markets/gas_demand_annual__mcf_386/gas_demand_annual__mcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded copper_conc_blister_stock_change__kt.parquet to tables/markets/copper_conc_blister_stock_change__kt/copper_conc_blister_stock_change__kt.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__mmcfd_386.parquet to tables/markets/gas_balance_monthly__mmcfd_386/gas_balance_monthly__mmcfd_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__bcm_40.parquet to tables/markets/gas_balance_quarterly__bcm_40/gas_balance_quarterly__bcm_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__bcfd_40.parquet to tables/markets/gas_balance_monthly__bcfd_40/gas_balance_monthly__bcfd_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__mcm_386.parquet to tables/markets/gas_demand_annual__mcm_386/gas_demand_annual__mcm_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__bcm_40.parquet to tables/markets/gas_balance_monthly__bcm_40/gas_balance_monthly__bcm_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded brm_lithium_demand_sector_product__kt_lce.parquet to tables/markets/brm_lithium_demand_sector_product__kt_lce/brm_lithium_demand_sector_product__kt_lce.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__bcm_386.parquet to tables/markets/gas_balance_quarterly__bcm_386/gas_balance_quarterly__bcm_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__bcf_40.parquet to tables/markets/gas_balance_monthly__bcf_40/gas_balance_monthly__bcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded brm_lithium_price_annual__usd_t.parquet to tables/markets/brm_lithium_price_annual__usd_t/brm_lithium_price_annual__usd_t.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__mmcf_40.parquet to tables/markets/gas_demand_monthly__mmcf_40/gas_demand_monthly__mmcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__mcf_40.parquet to tables/markets/gas_demand_monthly__mcf_40/gas_demand_monthly__mcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__tcf_386.parquet to tables/markets/gas_demand_annual__tcf_386/gas_demand_annual__tcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__mmcf_386.parquet to tables/markets/gas_demand_annual__mmcf_386/gas_demand_annual__mmcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__bcf_386.parquet to tables/markets/gas_balance_quarterly__bcf_386/gas_balance_quarterly__bcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__bcf_40.parquet to tables/markets/gas_balance_quarterly__bcf_40/gas_balance_quarterly__bcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__bcfd_386.parquet to tables/markets/gas_balance_monthly__bcfd_386/gas_balance_monthly__bcfd_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded coal_imports_exports_annual__mt.parquet to tables/markets/coal_imports_exports_annual__mt/coal_imports_exports_annual__mt.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded coal_demand_annual__mt.parquet to tables/markets/coal_demand_annual__mt/coal_demand_annual__mt.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__bcfd_40.parquet to tables/markets/gas_demand_monthly__bcfd_40/gas_demand_monthly__bcfd_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded brm_lithium_demand_product__kt_lce.parquet to tables/markets/brm_lithium_demand_product__kt_lce/brm_lithium_demand_product__kt_lce.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__mmcfd_386.parquet to tables/markets/gas_demand_monthly__mmcfd_386/gas_demand_monthly__mmcfd_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded coal_price_annual__usd_t.parquet to tables/markets/coal_price_annual__usd_t/coal_price_annual__usd_t.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__bcm_386.parquet to tables/markets/gas_demand_annual__bcm_386/gas_demand_annual__bcm_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__bcf_40.parquet to tables/markets/gas_demand_annual__bcf_40/gas_demand_annual__bcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__tcf_386.parquet to tables/markets/gas_balance_quarterly__tcf_386/gas_balance_quarterly__tcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__mmcfd_40.parquet to tables/markets/gas_balance_monthly__mmcfd_40/gas_balance_monthly__mmcfd_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__bcf_386.parquet to tables/markets/gas_demand_annual__bcf_386/gas_demand_annual__bcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__bcf_40.parquet to tables/markets/gas_balance_annual__bcf_40/gas_balance_annual__bcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__mmcf_40.parquet to tables/markets/gas_balance_quarterly__mmcf_40/gas_balance_quarterly__mmcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__tcf_40.parquet to tables/markets/gas_balance_monthly__tcf_40/gas_balance_monthly__tcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__mcm_386.parquet to tables/markets/gas_balance_monthly__mcm_386/gas_balance_monthly__mcm_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__bcf_386.parquet to tables/markets/gas_demand_monthly__bcf_386/gas_demand_monthly__bcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__mmcf_386.parquet to tables/markets/gas_balance_monthly__mmcf_386/gas_balance_monthly__mmcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__bcfd_40.parquet to tables/markets/gas_demand_annual__bcfd_40/gas_demand_annual__bcfd_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__mcm_40.parquet to tables/markets/gas_balance_annual__mcm_40/gas_balance_annual__mcm_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__mcf_386.parquet to tables/markets/gas_balance_monthly__mcf_386/gas_balance_monthly__mcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__tcf_40.parquet to tables/markets/gas_balance_quarterly__tcf_40/gas_balance_quarterly__tcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__bcm_386.parquet to tables/markets/gas_demand_monthly__bcm_386/gas_demand_monthly__bcm_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__mcm_40.parquet to tables/markets/gas_demand_annual__mcm_40/gas_demand_annual__mcm_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__tcf_386.parquet to tables/markets/gas_demand_monthly__tcf_386/gas_demand_monthly__tcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__mcf_386.parquet to tables/markets/gas_balance_annual__mcf_386/gas_balance_annual__mcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__mmcf_40.parquet to tables/markets/gas_demand_annual__mmcf_40/gas_demand_annual__mmcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__mcm_40.parquet to tables/markets/gas_balance_monthly__mcm_40/gas_balance_monthly__mcm_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__tj.parquet to tables/markets/gas_demand_monthly__tj/gas_demand_monthly__tj.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__bcfd_386.parquet to tables/markets/gas_demand_annual__bcfd_386/gas_demand_annual__bcfd_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__mmcfd_40.parquet to tables/markets/gas_demand_annual__mmcfd_40/gas_demand_annual__mmcfd_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__bcfd_40.parquet to tables/markets/gas_balance_quarterly__bcfd_40/gas_balance_quarterly__bcfd_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__tcf_40.parquet to tables/markets/gas_demand_annual__tcf_40/gas_demand_annual__tcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__mcm_386.parquet to tables/markets/gas_balance_annual__mcm_386/gas_balance_annual__mcm_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__mmcfd_386.parquet to tables/markets/gas_demand_annual__mmcfd_386/gas_demand_annual__mmcfd_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__mcm_40.parquet to tables/markets/gas_balance_quarterly__mcm_40/gas_balance_quarterly__mcm_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__tcf_40.parquet to tables/markets/gas_balance_annual__tcf_40/gas_balance_annual__tcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__bcm_40.parquet to tables/markets/gas_demand_monthly__bcm_40/gas_demand_monthly__bcm_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__mmcfd_386.parquet to tables/markets/gas_balance_annual__mmcfd_386/gas_balance_annual__mmcfd_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__mcm_386.parquet to tables/markets/gas_demand_monthly__mcm_386/gas_demand_monthly__mcm_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__bcf_386.parquet to tables/markets/gas_balance_monthly__bcf_386/gas_balance_monthly__bcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__mcf_40.parquet to tables/markets/gas_balance_quarterly__mcf_40/gas_balance_quarterly__mcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__bcf_40.parquet to tables/markets/gas_demand_monthly__bcf_40/gas_demand_monthly__bcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__mmcfd_40.parquet to tables/markets/gas_balance_quarterly__mmcfd_40/gas_balance_quarterly__mmcfd_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_quarterly__bcfd_386.parquet to tables/markets/gas_balance_quarterly__bcfd_386/gas_balance_quarterly__bcfd_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__mcf_386.parquet to tables/markets/gas_demand_monthly__mcf_386/gas_demand_monthly__mcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__bcm_386.parquet to tables/markets/gas_balance_monthly__bcm_386/gas_balance_monthly__bcm_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__tcf_386.parquet to tables/markets/gas_balance_annual__tcf_386/gas_balance_annual__tcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__mcf_40.parquet to tables/markets/gas_balance_monthly__mcf_40/gas_balance_monthly__mcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded copper_premia_monthly.parquet to tables/markets/copper_premia_monthly/copper_premia_monthly.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__tj.parquet to tables/markets/gas_balance_monthly__tj/gas_balance_monthly__tj.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__mcf_40.parquet to tables/markets/gas_balance_annual__mcf_40/gas_balance_annual__mcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_monthly__mmcf_386.parquet to tables/markets/gas_demand_monthly__mmcf_386/gas_demand_monthly__mmcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__bcfd_386.parquet to tables/markets/gas_balance_annual__bcfd_386/gas_balance_annual__bcfd_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_monthly__tcf_386.parquet to tables/markets/gas_balance_monthly__tcf_386/gas_balance_monthly__tcf_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__bcm_386.parquet to tables/markets/gas_balance_annual__bcm_386/gas_balance_annual__bcm_386.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_demand_annual__mcf_40.parquet to tables/markets/gas_demand_annual__mcf_40/gas_demand_annual__mcf_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__mmcfd_40.parquet to tables/markets/gas_balance_annual__mmcfd_40/gas_balance_annual__mmcfd_40.parquet\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n", "/Users/<USER>/.pyenv/versions/3.12.1/lib/python3.12/site-packages/urllib3/connectionpool.py:1103: InsecureRequestWarning: Unverified HTTPS request is being made to host 'ld-core-external-dev-data.s3.amazonaws.com'. Adding certificate verification is strongly advised. See: https://urllib3.readthedocs.io/en/latest/advanced-usage.html#tls-warnings\n", "  warnings.warn(\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Downloaded gas_balance_annual__bcf_386.parquet to tables/markets/gas_balance_annual__bcf_386/gas_balance_annual__bcf_386.parquet\n"]}], "source": ["import os\n", "import json\n", "import boto3\n", "\n", "os.makedirs('tables/markets', exist_ok=True)\n", "\n", "schema_files = [f for f in os.listdir('schemas/markets') if f.endswith('.json')]\n", "\n", "s3_client = boto3.client(\n", "    's3', \n", "    verify=False # zscaler faff\n", ")\n", "\n", "for schema_file in schema_files:\n", "    with open(os.path.join('schemas/markets', schema_file)) as f:\n", "        schema = json.load(f)\n", "    \n", "    s3_path = schema['storage_location'].replace('s3://', '')\n", "    bucket = s3_path.split('/')[0]\n", "    prefix = '/'.join(s3_path.split('/')[1:])\n", "    \n", "    table_name = schema['table_name']\n", "    local_dir = f'tables/markets/{table_name}'\n", "    os.makedirs(local_dir, exist_ok=True)\n", "    \n", "    try:\n", "        # List all objects under this prefix\n", "        objects = s3_client.list_objects_v2(Bucket=bucket, Prefix=prefix)\n", "        \n", "        for obj in objects.get('Contents', []):\n", "            key = obj['Key']\n", "            filename = os.path.basename(key)\n", "            if filename:\n", "                local_path = os.path.join(local_dir, filename)\n", "                s3_client.download_file(bucket, key, local_path)\n", "                print(f\"Downloaded {filename} to {local_path}\")\n", "                \n", "    except Exception as e:\n", "        print(f\"Error processing {table_name}: {str(e)}\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Schema saved for brm_lithium_demand_product__kt_lce\n", "Schema saved for brm_lithium_demand_sector_product__kt_lce\n", "Schema saved for brm_lithium_price_annual__usd_t\n", "Schema saved for coal_demand_annual__mt\n", "Schema saved for coal_imports_exports_annual__mt\n", "Schema saved for coal_price_annual__usd_t\n", "Schema saved for coal_supply_annual__mt\n", "Schema saved for copper_china_market_balance\n", "Schema saved for copper_combined_tcrc_monthly\n", "Schema saved for copper_conc_blister_stock_change__kt\n", "Schema saved for copper_global_market_balance\n", "Schema saved for copper_premia_monthly\n", "Schema saved for copper_semis_production__kt\n", "Schema saved for cumulative_charging_ports\n", "Schema saved for gas_balance_annual__bcf_386\n", "Schema saved for gas_balance_annual__bcf_40\n", "Schema saved for gas_balance_annual__bcfd_386\n", "Schema saved for gas_balance_annual__bcfd_40\n", "Schema saved for gas_balance_annual__bcm_386\n", "Schema saved for gas_balance_annual__bcm_40\n", "Schema saved for gas_balance_annual__mcf_386\n", "Schema saved for gas_balance_annual__mcf_40\n", "Schema saved for gas_balance_annual__mcm_386\n", "Schema saved for gas_balance_annual__mcm_40\n", "Schema saved for gas_balance_annual__mmcf_386\n", "Schema saved for gas_balance_annual__mmcf_40\n", "Schema saved for gas_balance_annual__mmcfd_386\n", "Schema saved for gas_balance_annual__mmcfd_40\n", "Schema saved for gas_balance_annual__tcf_386\n", "Schema saved for gas_balance_annual__tcf_40\n", "Schema saved for gas_balance_annual__tj\n", "Schema saved for gas_balance_monthly__bcf_386\n", "Schema saved for gas_balance_monthly__bcf_40\n", "Schema saved for gas_balance_monthly__bcfd_386\n", "Schema saved for gas_balance_monthly__bcfd_40\n", "Schema saved for gas_balance_monthly__bcm_386\n", "Schema saved for gas_balance_monthly__bcm_40\n", "Schema saved for gas_balance_monthly__mcf_386\n", "Schema saved for gas_balance_monthly__mcf_40\n", "Schema saved for gas_balance_monthly__mcm_386\n", "Schema saved for gas_balance_monthly__mcm_40\n", "Schema saved for gas_balance_monthly__mmcf_386\n", "Schema saved for gas_balance_monthly__mmcf_40\n", "Schema saved for gas_balance_monthly__mmcfd_386\n", "Schema saved for gas_balance_monthly__mmcfd_40\n", "Schema saved for gas_balance_monthly__tcf_386\n", "Schema saved for gas_balance_monthly__tcf_40\n", "Schema saved for gas_balance_monthly__tj\n", "Schema saved for gas_balance_quarterly__bcf_386\n", "Schema saved for gas_balance_quarterly__bcf_40\n", "Schema saved for gas_balance_quarterly__bcfd_386\n", "Schema saved for gas_balance_quarterly__bcfd_40\n", "Schema saved for gas_balance_quarterly__bcm_386\n", "Schema saved for gas_balance_quarterly__bcm_40\n", "Schema saved for gas_balance_quarterly__mcf_386\n", "Schema saved for gas_balance_quarterly__mcf_40\n", "Schema saved for gas_balance_quarterly__mcm_386\n", "Schema saved for gas_balance_quarterly__mcm_40\n", "Schema saved for gas_balance_quarterly__mmcf_386\n", "Schema saved for gas_balance_quarterly__mmcf_40\n", "Schema saved for gas_balance_quarterly__mmcfd_386\n", "Schema saved for gas_balance_quarterly__mmcfd_40\n", "Schema saved for gas_balance_quarterly__tcf_386\n", "Schema saved for gas_balance_quarterly__tcf_40\n", "Schema saved for gas_balance_quarterly__tj\n", "Schema saved for gas_demand_annual__bcf_386\n", "Schema saved for gas_demand_annual__bcf_40\n", "Schema saved for gas_demand_annual__bcfd_386\n", "Schema saved for gas_demand_annual__bcfd_40\n", "Schema saved for gas_demand_annual__bcm_386\n", "Schema saved for gas_demand_annual__bcm_40\n", "Schema saved for gas_demand_annual__mcf_386\n", "Schema saved for gas_demand_annual__mcf_40\n", "Schema saved for gas_demand_annual__mcm_386\n", "Schema saved for gas_demand_annual__mcm_40\n", "Schema saved for gas_demand_annual__mmcf_386\n", "Schema saved for gas_demand_annual__mmcf_40\n", "Schema saved for gas_demand_annual__mmcfd_386\n", "Schema saved for gas_demand_annual__mmcfd_40\n", "Schema saved for gas_demand_annual__tcf_386\n", "Schema saved for gas_demand_annual__tcf_40\n", "Schema saved for gas_demand_annual__tj\n", "Schema saved for gas_demand_monthly__bcf_386\n", "Schema saved for gas_demand_monthly__bcf_40\n", "Schema saved for gas_demand_monthly__bcfd_386\n", "Schema saved for gas_demand_monthly__bcfd_40\n", "Schema saved for gas_demand_monthly__bcm_386\n", "Schema saved for gas_demand_monthly__bcm_40\n", "Schema saved for gas_demand_monthly__mcf_386\n", "Schema saved for gas_demand_monthly__mcf_40\n", "Schema saved for gas_demand_monthly__mcm_386\n", "Schema saved for gas_demand_monthly__mcm_40\n", "Schema saved for gas_demand_monthly__mmcf_386\n", "Schema saved for gas_demand_monthly__mmcf_40\n", "Schema saved for gas_demand_monthly__mmcfd_386\n", "Schema saved for gas_demand_monthly__mmcfd_40\n", "Schema saved for gas_demand_monthly__tcf_386\n", "Schema saved for gas_demand_monthly__tcf_40\n", "Schema saved for gas_demand_monthly__tj\n", "Schema saved for gas_demand_quarterly__bcf_386\n"]}], "source": ["import os\n", "import json\n", "from datetime import datetime\n", "\n", "os.makedirs('schemas/markets', exist_ok=True)\n", "\n", "def save_table_schema(database_name, table_name):\n", "    try:\n", "        response = glue_client.get_table(\n", "            DatabaseName=database_name,\n", "            Name=table_name\n", "        )\n", "        \n", "        table_details = response['Table']\n", "        \n", "        storage_location = table_details.get('StorageDescriptor', {}).get('Location', '')\n", "        \n", "        schema_info = {\n", "            'table_name': table_name,\n", "            'database_name': database_name,\n", "            'columns': [\n", "                {\n", "                    'name': col['Name'],\n", "                    'type': col['Type'],\n", "                    'comment': col.get('Comment', '')\n", "                }\n", "                for col in table_details.get('StorageDescriptor', {}).get('Columns', [])\n", "            ],\n", "            'storage_location': storage_location,\n", "            'last_updated': datetime.now().isoformat()\n", "        }\n", "        \n", "        filename = f\"schemas/markets/schema_{table_name}.json\"\n", "        with open(filename, 'w') as f:\n", "            json.dump(schema_info, f, indent=2)\n", "            \n", "        print(f\"Schema saved for {table_name}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {table_name}: {str(e)}\")\n", "\n", "for table_name in tables:\n", "    save_table_schema(markets_table, table_name)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Saved metadata for coal_demand_annual__mt\n", "Saved metadata for coal_supply_annual__mt\n", "Saved metadata for cumulative_charging_ports\n", "Saved metadata for coal_price_annual__usd_t\n", "Saved metadata for coal_imports_exports_annual__mt\n", "Saved metadata for copper_global_market_balance\n", "Saved metadata for copper_combined_tcrc_monthly\n", "Saved metadata for metals_global_production_forecast_annual__copper_eq_kt\n", "Saved metadata for metals_global_market_balance_lme_price_annual\n", "Saved metadata for lithium_refinery_supply_annual__kt_lce\n", "Saved metadata for lithium_mine_supply_annual__kt_lce\n", "Saved metadata for lithium_global_chem_market_balance\n", "Saved metadata for lithium_global_spod_market_balance\n", "Saved metadata for metals_global_production_forecast_annual__kt\n", "Saved metadata for metals_lme_shfe_stocks\n", "Saved metadata for metals_industry_value_annual__usd\n", "Saved metadata for metals_production_primary_demand_annual__kt\n", "Saved metadata for metals_implied_country_balance_annual__kt\n", "Saved metadata for metals_mine_production_forecast_annual__copper_eq_kt\n", "Saved metadata for metals_price_annual\n", "Saved metadata for metals_refinery_production_capability_annual__kt\n", "Saved metadata for metals_stock_days_lme_price_annual\n", "Saved metadata for metals_smelter_production_capability_annual__kt\n", "Saved metadata for metals_stock_days_lme_price_quarterly\n", "Saved metadata for metals_refinery_production_forecast_annual__copper_eq_kt\n", "Saved metadata for metals_supply_all_data_annual__kt\n", "Saved metadata for metals_smelter_production_forecast_annual__copper_eq_kt\n", "Saved metadata for nickel_sulphate_price_annual\n", "Saved metadata for gas_balance_annual__mcm_40\n", "Saved metadata for testing_metals_flows_annual__kt\n", "Saved metadata for vehicle_sales_annual__units\n", "Saved metadata for gas_balance_quarterly__mcm_40\n", "Saved metadata for gas_balance_monthly__mcm_40\n", "Saved metadata for metals_mine_production_capability_annual__kt\n", "Saved metadata for gas_supply_annual__mcm_40\n", "Saved metadata for gas_supply_quarterly__mcm_40\n", "Saved metadata for gas_demand_annual__mcm_40\n", "Saved metadata for gas_demand_quarterly__mcm_40\n", "Saved metadata for gas_supply_monthly__mcm_40\n", "Saved metadata for gas_demand_monthly__mcm_40\n", "Saved metadata for gas_imports_exports_annual__mcm_40\n", "Saved metadata for lithium_global_bg_market_balance\n", "Saved metadata for gas_imports_exports_quarterly__mcm_40\n", "Saved metadata for gas_storage_annual__mcm_40\n", "Saved metadata for gas_imports_exports_monthly__mcm_40\n", "Saved metadata for gas_storage_quarterly__mcm_40\n", "Saved metadata for gas_storage_monthly__mcm_40\n", "Saved metadata for gas_transfers_annual__mcm_40\n", "Saved metadata for gas_transfers_quarterly__mcm_40\n", "Saved metadata for markets_releases\n", "Saved metadata for copper_china_market_balance\n", "Saved metadata for gas_transfers_monthly__mcm_40\n", "Saved metadata for gas_supply_annual__mcm_386\n", "Saved metadata for gas_balance_annual__mcm_386\n", "Saved metadata for gas_supply_quarterly__mcm_386\n", "Saved metadata for gas_balance_monthly__mcm_386\n", "Saved metadata for gas_balance_quarterly__mcm_386\n", "Saved metadata for gas_supply_monthly__mcm_386\n", "Saved metadata for gas_demand_annual__mcm_386\n", "Saved metadata for gas_demand_quarterly__mcm_386\n", "Saved metadata for gas_demand_monthly__mcm_386\n", "Saved metadata for gas_imports_exports_annual__mcm_386\n", "Saved metadata for gas_storage_annual__mcm_386\n", "Saved metadata for gas_storage_quarterly__mcm_386\n", "Saved metadata for gas_storage_monthly__mcm_386\n", "Saved metadata for gas_imports_exports_quarterly__mcm_386\n", "Saved metadata for gas_transfers_annual__mcm_386\n", "Saved metadata for gas_transfers_quarterly__mcm_386\n", "Saved metadata for gas_imports_exports_monthly__mcm_386\n", "Saved metadata for gas_transfers_monthly__mcm_386\n", "Saved metadata for gas_balance_annual__bcm_40\n", "Saved metadata for gas_balance_quarterly__bcm_40\n", "Saved metadata for gas_demand_annual__bcm_40\n", "Saved metadata for gas_supply_annual__bcm_40\n", "Saved metadata for gas_demand_quarterly__bcm_40\n", "Saved metadata for gas_supply_monthly__bcm_40\n", "Saved metadata for gas_supply_quarterly__bcm_40\n", "Saved metadata for gas_balance_monthly__bcm_40\n", "Saved metadata for gas_demand_monthly__bcm_40\n", "Saved metadata for gas_imports_exports_annual__bcm_40\n", "Saved metadata for gas_imports_exports_quarterly__bcm_40\n", "Saved metadata for gas_imports_exports_monthly__bcm_40\n", "Saved metadata for gas_storage_annual__bcm_40\n", "Saved metadata for gas_storage_quarterly__bcm_40\n", "Saved metadata for gas_storage_monthly__bcm_40\n", "Saved metadata for gas_transfers_annual__bcm_40\n", "Saved metadata for gas_transfers_monthly__bcm_40\n", "Saved metadata for gas_transfers_quarterly__bcm_40\n", "Saved metadata for gas_balance_annual__bcm_386\n", "Saved metadata for gas_balance_quarterly__bcm_386\n", "Saved metadata for gas_balance_monthly__bcm_386\n", "Saved metadata for gas_supply_annual__bcm_386\n", "Saved metadata for gas_supply_quarterly__bcm_386\n", "Saved metadata for gas_supply_monthly__bcm_386\n", "Saved metadata for gas_demand_annual__bcm_386\n", "Saved metadata for gas_demand_monthly__bcm_386\n", "Saved metadata for gas_imports_exports_annual__bcm_386\n", "Saved metadata for gas_demand_quarterly__bcm_386\n", "Saved metadata for gas_imports_exports_quarterly__bcm_386\n", "Saved metadata for gas_storage_annual__bcm_386\n", "Saved metadata for gas_imports_exports_monthly__bcm_386\n", "Saved metadata for gas_storage_quarterly__bcm_386\n", "Saved metadata for gas_storage_monthly__bcm_386\n", "Saved metadata for gas_transfers_annual__bcm_386\n", "Saved metadata for gas_supply_quarterly__mmcfd_40\n", "Saved metadata for gas_transfers_quarterly__bcm_386\n", "Saved metadata for gas_transfers_monthly__bcm_386\n", "Saved metadata for gas_balance_quarterly__mmcfd_40\n", "Saved metadata for gas_supply_annual__mmcfd_40\n", "Saved metadata for gas_supply_monthly__mmcfd_40\n", "Saved metadata for gas_balance_monthly__mmcfd_40\n", "Saved metadata for gas_balance_annual__mmcfd_40\n", "Saved metadata for gas_demand_annual__mmcfd_40\n", "Saved metadata for gas_demand_quarterly__mmcfd_40\n", "Saved metadata for gas_imports_exports_quarterly__mmcfd_40\n", "Saved metadata for gas_demand_monthly__mmcfd_40\n", "Saved metadata for gas_imports_exports_annual__mmcfd_40\n", "Saved metadata for gas_storage_annual__mmcfd_40\n", "Saved metadata for gas_storage_monthly__mmcfd_40\n", "Saved metadata for gas_imports_exports_monthly__mmcfd_40\n", "Saved metadata for gas_storage_quarterly__mmcfd_40\n", "Saved metadata for gas_transfers_quarterly__mmcfd_40\n", "Saved metadata for gas_transfers_annual__mmcfd_40\n", "Saved metadata for gas_transfers_monthly__mmcfd_40\n", "Saved metadata for gas_balance_annual__mmcfd_386\n", "Saved metadata for gas_supply_annual__mmcfd_386\n", "Saved metadata for gas_balance_quarterly__mmcfd_386\n", "Saved metadata for gas_balance_monthly__mmcfd_386\n", "Saved metadata for gas_supply_quarterly__mmcfd_386\n", "Saved metadata for gas_demand_annual__mmcfd_386\n", "Saved metadata for gas_supply_monthly__mmcfd_386\n", "Saved metadata for gas_demand_quarterly__mmcfd_386\n", "Saved metadata for gas_demand_monthly__mmcfd_386\n", "Saved metadata for gas_imports_exports_annual__mmcfd_386\n", "Saved metadata for gas_imports_exports_monthly__mmcfd_386\n", "Saved metadata for gas_storage_annual__mmcfd_386\n", "Saved metadata for gas_storage_quarterly__mmcfd_386\n", "Saved metadata for gas_imports_exports_quarterly__mmcfd_386\n", "Saved metadata for gas_storage_monthly__mmcfd_386\n", "Saved metadata for gas_transfers_annual__mmcfd_386\n", "Saved metadata for gas_transfers_monthly__mmcfd_386\n", "Saved metadata for gas_transfers_quarterly__mmcfd_386\n", "Saved metadata for gas_balance_annual__bcfd_40\n", "Saved metadata for gas_balance_quarterly__bcfd_40\n", "Saved metadata for gas_supply_annual__bcfd_40\n", "Saved metadata for gas_demand_quarterly__bcfd_40\n", "Saved metadata for gas_supply_monthly__bcfd_40\n", "Saved metadata for gas_demand_monthly__bcfd_40\n", "Saved metadata for gas_demand_annual__bcfd_40\n", "Saved metadata for gas_supply_quarterly__bcfd_40\n", "Saved metadata for gas_imports_exports_annual__bcfd_40\n", "Saved metadata for gas_imports_exports_quarterly__bcfd_40\n", "Saved metadata for gas_balance_monthly__bcfd_40\n", "Saved metadata for gas_storage_annual__bcfd_40\n", "Saved metadata for gas_storage_quarterly__bcfd_40\n", "Saved metadata for gas_storage_monthly__bcfd_40\n", "Saved metadata for gas_imports_exports_monthly__bcfd_40\n", "Saved metadata for gas_transfers_annual__bcfd_40\n", "Saved metadata for gas_transfers_quarterly__bcfd_40\n", "Saved metadata for gas_transfers_monthly__bcfd_40\n", "Saved metadata for gas_balance_annual__bcfd_386\n", "Saved metadata for gas_supply_annual__bcfd_386\n", "Saved metadata for gas_balance_monthly__bcfd_386\n", "Saved metadata for gas_balance_quarterly__bcfd_386\n", "Saved metadata for gas_demand_annual__bcfd_386\n", "Saved metadata for gas_supply_quarterly__bcfd_386\n", "Saved metadata for gas_demand_quarterly__bcfd_386\n", "Saved metadata for gas_supply_monthly__bcfd_386\n", "Saved metadata for gas_demand_monthly__bcfd_386\n", "Saved metadata for gas_imports_exports_annual__bcfd_386\n", "Saved metadata for gas_imports_exports_quarterly__bcfd_386\n", "Saved metadata for gas_storage_quarterly__bcfd_386\n", "Saved metadata for gas_storage_annual__bcfd_386\n", "Saved metadata for gas_imports_exports_monthly__bcfd_386\n", "Saved metadata for gas_storage_monthly__bcfd_386\n", "Saved metadata for gas_transfers_quarterly__bcfd_386\n", "Saved metadata for gas_balance_annual__mmcf_40\n", "Saved metadata for gas_transfers_monthly__bcfd_386\n", "Saved metadata for gas_transfers_annual__bcfd_386\n", "Saved metadata for gas_supply_annual__mmcf_40\n", "Saved metadata for gas_balance_quarterly__mmcf_40\n", "Saved metadata for gas_supply_quarterly__mmcf_40\n", "Saved metadata for gas_balance_monthly__mmcf_40\n", "Saved metadata for gas_supply_monthly__mmcf_40\n", "Saved metadata for gas_demand_annual__mmcf_40\n", "Saved metadata for gas_demand_quarterly__mmcf_40\n", "Saved metadata for gas_demand_monthly__mmcf_40\n", "Saved metadata for gas_imports_exports_quarterly__mmcf_40\n", "Saved metadata for gas_imports_exports_annual__mmcf_40\n", "Saved metadata for gas_storage_quarterly__mmcf_40\n", "Saved metadata for gas_storage_annual__mmcf_40\n", "Saved metadata for gas_storage_monthly__mmcf_40\n", "Saved metadata for gas_imports_exports_monthly__mmcf_40\n", "Saved metadata for gas_transfers_annual__mmcf_40\n", "Saved metadata for gas_transfers_quarterly__mmcf_40\n", "Saved metadata for gas_transfers_monthly__mmcf_40\n", "Saved metadata for gas_balance_annual__mmcf_386\n", "Saved metadata for gas_balance_quarterly__mmcf_386\n", "Saved metadata for gas_supply_annual__mmcf_386\n", "Saved metadata for gas_supply_quarterly__mmcf_386\n", "Saved metadata for gas_balance_monthly__mmcf_386\n", "Saved metadata for gas_demand_quarterly__mmcf_386\n", "Saved metadata for gas_supply_monthly__mmcf_386\n", "Saved metadata for gas_demand_annual__mmcf_386\n", "Saved metadata for gas_demand_monthly__mmcf_386\n", "Saved metadata for gas_imports_exports_quarterly__mmcf_386\n", "Saved metadata for gas_imports_exports_annual__mmcf_386\n", "Saved metadata for gas_storage_quarterly__mmcf_386\n", "Saved metadata for gas_storage_annual__mmcf_386\n", "Saved metadata for gas_imports_exports_monthly__mmcf_386\n", "Saved metadata for gas_transfers_quarterly__mmcf_386\n", "Saved metadata for gas_transfers_annual__mmcf_386\n", "Saved metadata for gas_storage_monthly__mmcf_386\n", "Saved metadata for gas_transfers_monthly__mmcf_386\n", "Saved metadata for gas_balance_annual__mcf_40\n", "Saved metadata for gas_balance_quarterly__mcf_40\n", "Saved metadata for gas_supply_annual__mcf_40\n", "Saved metadata for gas_balance_monthly__mcf_40\n", "Saved metadata for gas_demand_annual__mcf_40\n", "Saved metadata for gas_supply_quarterly__mcf_40\n", "Saved metadata for gas_demand_quarterly__mcf_40\n", "Saved metadata for gas_demand_monthly__mcf_40\n", "Saved metadata for gas_imports_exports_annual__mcf_40\n", "Saved metadata for gas_supply_monthly__mcf_40\n", "Saved metadata for gas_imports_exports_monthly__mcf_40\n", "Saved metadata for gas_storage_annual__mcf_40\n", "Saved metadata for gas_imports_exports_quarterly__mcf_40\n", "Saved metadata for gas_storage_quarterly__mcf_40\n", "Saved metadata for gas_storage_monthly__mcf_40\n", "Saved metadata for gas_transfers_annual__mcf_40\n", "Saved metadata for gas_transfers_quarterly__mcf_40\n", "Saved metadata for gas_transfers_monthly__mcf_40\n", "Saved metadata for gas_balance_quarterly__mcf_386\n", "Saved metadata for gas_balance_annual__mcf_386\n", "Saved metadata for gas_supply_annual__mcf_386\n", "Saved metadata for gas_supply_quarterly__mcf_386\n", "Saved metadata for gas_balance_monthly__mcf_386\n", "Saved metadata for gas_demand_annual__mcf_386\n", "Saved metadata for gas_supply_monthly__mcf_386\n", "Saved metadata for gas_demand_quarterly__mcf_386\n", "Saved metadata for gas_demand_monthly__mcf_386\n", "Saved metadata for gas_imports_exports_annual__mcf_386\n", "Saved metadata for gas_imports_exports_monthly__mcf_386\n", "Saved metadata for gas_imports_exports_quarterly__mcf_386\n", "Saved metadata for gas_storage_annual__mcf_386\n", "Saved metadata for gas_storage_quarterly__mcf_386\n", "Saved metadata for gas_storage_monthly__mcf_386\n", "Saved metadata for gas_transfers_annual__mcf_386\n", "Saved metadata for gas_transfers_quarterly__mcf_386\n", "Saved metadata for gas_balance_annual__bcf_40\n", "Saved metadata for gas_transfers_monthly__mcf_386\n", "Saved metadata for gas_balance_quarterly__bcf_40\n", "Saved metadata for gas_supply_annual__bcf_40\n", "Saved metadata for gas_balance_monthly__bcf_40\n", "Saved metadata for gas_demand_annual__bcf_40\n", "Saved metadata for gas_supply_quarterly__bcf_40\n", "Saved metadata for gas_supply_monthly__bcf_40\n", "Saved metadata for gas_demand_quarterly__bcf_40\n", "Saved metadata for gas_demand_monthly__bcf_40\n", "Saved metadata for gas_imports_exports_monthly__bcf_40\n", "Saved metadata for gas_storage_annual__bcf_40\n", "Saved metadata for gas_imports_exports_annual__bcf_40\n", "Saved metadata for gas_storage_quarterly__bcf_40\n", "Saved metadata for gas_storage_monthly__bcf_40\n", "Saved metadata for gas_transfers_annual__bcf_40\n", "Saved metadata for gas_transfers_monthly__bcf_40\n", "Saved metadata for gas_transfers_quarterly__bcf_40\n", "Saved metadata for gas_balance_annual__bcf_386\n", "Saved metadata for gas_balance_quarterly__bcf_386\n", "Saved metadata for gas_supply_annual__bcf_386\n", "Saved metadata for gas_balance_monthly__bcf_386\n", "Saved metadata for gas_supply_quarterly__bcf_386\n", "Saved metadata for gas_supply_monthly__bcf_386\n", "Saved metadata for gas_demand_annual__bcf_386\n", "Saved metadata for gas_demand_quarterly__bcf_386\n", "Saved metadata for gas_demand_monthly__bcf_386\n", "Saved metadata for gas_imports_exports_quarterly__bcf_386\n", "Saved metadata for gas_imports_exports_annual__bcf_386\n", "Saved metadata for gas_storage_quarterly__bcf_386\n", "Saved metadata for gas_storage_annual__bcf_386\n", "Saved metadata for gas_storage_monthly__bcf_386\n", "Saved metadata for gas_imports_exports_monthly__bcf_386\n", "Saved metadata for gas_transfers_annual__bcf_386\n", "Saved metadata for gas_transfers_monthly__bcf_386\n", "Saved metadata for gas_balance_annual__tcf_40\n", "Saved metadata for gas_balance_quarterly__tcf_40\n", "Saved metadata for gas_transfers_quarterly__bcf_386\n", "Saved metadata for gas_balance_monthly__tcf_40\n", "Saved metadata for gas_supply_monthly__tcf_40\n", "Saved metadata for gas_supply_annual__tcf_40\n", "Saved metadata for gas_supply_quarterly__tcf_40\n", "Saved metadata for gas_demand_annual__tcf_40\n", "Saved metadata for gas_demand_monthly__tcf_40\n", "Saved metadata for gas_imports_exports_annual__tcf_40\n", "Saved metadata for gas_imports_exports_monthly__tcf_40\n", "Saved metadata for gas_storage_quarterly__tcf_40\n", "Saved metadata for gas_demand_quarterly__tcf_40\n", "Saved metadata for gas_imports_exports_quarterly__tcf_40\n", "Saved metadata for gas_storage_annual__tcf_40\n", "Saved metadata for gas_storage_monthly__tcf_40\n", "Saved metadata for gas_transfers_annual__tcf_40\n", "Saved metadata for gas_transfers_quarterly__tcf_40\n", "Saved metadata for gas_transfers_monthly__tcf_40\n", "Saved metadata for gas_supply_annual__tcf_386\n", "Saved metadata for gas_supply_monthly__tcf_386\n", "Saved metadata for gas_balance_quarterly__tcf_386\n", "Saved metadata for gas_demand_annual__tcf_386\n", "Saved metadata for gas_supply_quarterly__tcf_386\n", "Saved metadata for gas_balance_annual__tcf_386\n", "Saved metadata for gas_balance_monthly__tcf_386\n", "Saved metadata for gas_demand_quarterly__tcf_386\n", "Saved metadata for gas_demand_monthly__tcf_386\n", "Saved metadata for gas_imports_exports_annual__tcf_386\n", "Saved metadata for gas_storage_annual__tcf_386\n", "Saved metadata for gas_storage_monthly__tcf_386\n", "Saved metadata for gas_storage_quarterly__tcf_386\n", "Saved metadata for gas_transfers_quarterly__tcf_386\n", "Saved metadata for gas_imports_exports_quarterly__tcf_386\n", "Saved metadata for gas_imports_exports_monthly__tcf_386\n", "Saved metadata for gas_balance_annual__tj\n", "Saved metadata for gas_transfers_annual__tcf_386\n", "Saved metadata for gas_transfers_monthly__tcf_386\n", "Saved metadata for gas_demand_annual__tj\n", "Saved metadata for gas_balance_monthly__tj\n", "Saved metadata for gas_supply_annual__tj\n", "Saved metadata for gas_balance_quarterly__tj\n", "Saved metadata for gas_supply_monthly__tj\n", "Saved metadata for gas_supply_quarterly__tj\n", "Saved metadata for gas_demand_quarterly__tj\n", "Saved metadata for gas_demand_monthly__tj\n", "Saved metadata for gas_imports_exports_annual__tj\n", "Saved metadata for gas_imports_exports_quarterly__tj\n", "Saved metadata for gas_storage_monthly__tj\n", "Saved metadata for gas_storage_quarterly__tj\n", "Saved metadata for gas_storage_annual__tj\n", "Saved metadata for gas_imports_exports_monthly__tj\n", "Saved metadata for gas_transfers_annual__tj\n", "Saved metadata for gas_transfers_quarterly__tj\n", "Saved metadata for gas_transfers_monthly__tj\n", "Saved metadata for gas_imports_exports_annual__mmtpa\n", "Saved metadata for gas_imports_exports_quarterly__mmtpa\n", "Saved metadata for gas_imports_exports_monthly__mmtpa\n", "Saved metadata for gas_transfers_quarterly__mmtpa\n", "Saved metadata for gas_price_real_annual__usd_mmbtu\n", "Saved metadata for gas_transfers_monthly__mmtpa\n", "Saved metadata for gas_transfers_annual__mmtpa\n", "Saved metadata for gas_price_real_monthly__usd_mmbtu\n", "Saved metadata for gas_price_real_quarterly__usd_mmbtu\n", "Saved metadata for gas_price_nominal_annual__usd_mmbtu\n", "Saved metadata for gas_price_nominal_monthly__usd_mmbtu\n", "Saved metadata for gas_price_nominal_quarterly__usd_mmbtu\n"]}], "source": ["import os\n", "import concurrent.futures\n", "\n", "os.makedirs('metadata/markets', exist_ok=True)\n", "\n", "def process_table(table_name):\n", "    try:\n", "        url = f'{ld_url}/registry/markets/all/odata/$registry/{table_name}'\n", "        response = requests.get(url, cookies=cookies)\n", "        \n", "        filename = f'metadata/markets/{table_name}.json'\n", "        with open(filename, 'w') as f:\n", "            json.dump(response.json(), f, indent=2)\n", "            \n", "        print(f\"Saved metadata for {table_name}\")\n", "        \n", "    except Exception as e:\n", "        print(f\"Error processing {table_name}: {str(e)}\")\n", "\n", "with concurrent.futures.ThreadPoolExecutor(max_workers=10) as executor:\n", "    executor.map(process_table, markets_sub_tables)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 2}
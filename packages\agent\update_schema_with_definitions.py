#!/usr/bin/env python3
"""
<PERSON><PERSON>t to update the OpenAPI schema YAML file with definitions from CSV.
This script reads the data_definitions.csv file and updates the query_dataset description in the wmlgv-assistant-agent-schema.yaml file.
"""

import csv
import os
from typing import Dict


def load_definitions_from_csv(csv_path: str) -> Dict[str, str]:
    """Load metric definitions from CSV file"""
    definitions = {}
    try:
        with open(csv_path, "r", encoding="utf-8-sig") as file:
            reader = csv.DictReader(file)
            for row in reader:
                metric_key = row.get("Metric", "")
                definition_key = row.get("Definition") or row.get("Description", "")
                if metric_key and definition_key:
                    definitions[metric_key.strip()] = definition_key.strip()
    except Exception as e:
        print(f"Error loading definitions from CSV: {e}")
    return definitions


def format_definitions_for_yaml(definitions: Dict[str, str]) -> str:
    """Format definitions for inclusion in YAML description"""
    if not definitions:
        return ""

    formatted_defs = []
    for metric, definition in sorted(definitions.items()):
        formatted_defs.append(f"                - **{metric}**: {definition}")

    definitions_section = """
                ### Key Metric Definitions
""" + "\n".join(formatted_defs)

    return definitions_section


def update_yaml_with_definitions(yaml_path: str, definitions: Dict[str, str]):
    """Update the YAML file with definitions from CSV"""
    try:
        with open(yaml_path, "r", encoding="utf-8") as file:
            content = file.read()

        # Find the query_dataset description section
        start_marker = "description: |"
        end_marker = "operationId: 'queryDataset'"

        start_index = content.find(start_marker)
        if start_index == -1:
            print("Error: Could not find description section in YAML file")
            return

        end_index = content.find(end_marker, start_index)
        if end_index == -1:
            print("Error: Could not find operationId section in YAML file")
            return

        # Extract the current description
        current_description = content[start_index:end_index]

        # Remove existing definitions if they exist
        lines = current_description.split("\n")
        filtered_lines = []
        in_definitions_section = False

        for line in lines:
            if "### Key Metric Definitions" in line:
                in_definitions_section = True
                continue
            elif in_definitions_section and line.strip().startswith("- **"):
                continue
            elif (
                in_definitions_section
                and not line.strip().startswith("- **")
                and line.strip() != ""
            ):
                in_definitions_section = False
                filtered_lines.append(line)
            elif not in_definitions_section:
                filtered_lines.append(line)

        # Find the end of the Data Sources section to insert definitions
        new_lines = []
        definitions_inserted = False

        for i, line in enumerate(filtered_lines):
            new_lines.append(line)

            # Look for the end of the Data Sources section (after the company-specific tables description)
            if (
                "- **Company-specific asset data (timeseries)** is stored in `company_asset_timeseries_table`"
                in line
                and not definitions_inserted
            ):
                # Add the definitions section after the Data Sources section
                definitions_section = format_definitions_for_yaml(definitions)
                if definitions_section:
                    new_lines.append(definitions_section)
                    definitions_inserted = True

        # If definitions weren't inserted (fallback), add them at the end
        if not definitions_inserted:
            definitions_section = format_definitions_for_yaml(definitions)
            if definitions_section:
                new_lines.append(definitions_section)

        # Reconstruct the description
        new_description = "\n".join(new_lines)

        # Replace the content
        new_content = content[:start_index] + new_description + content[end_index:]

        # Write the updated content back
        with open(yaml_path, "w", encoding="utf-8") as file:
            file.write(new_content)

        print(f"Successfully updated {yaml_path} with {len(definitions)} definitions")

    except Exception as e:
        print(f"Error updating YAML file: {e}")


def main():
    """Main function to update the YAML file with CSV definitions"""
    script_dir = os.path.dirname(os.path.abspath(__file__))
    csv_path = os.path.join(script_dir, "tools", "data_definitions.csv")
    yaml_path = os.path.join(script_dir, "wmlgv-assistant-agent-schema.yaml")

    if not os.path.exists(csv_path):
        print(f"Error: CSV file not found at {csv_path}")
        return

    if not os.path.exists(yaml_path):
        print(f"Error: YAML file not found at {yaml_path}")
        return

    definitions = load_definitions_from_csv(csv_path)
    if not definitions:
        print("No definitions found in CSV file")
        return

    update_yaml_with_definitions(yaml_path, definitions)


if __name__ == "__main__":
    main()

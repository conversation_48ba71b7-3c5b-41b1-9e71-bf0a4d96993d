import React from "react";
import { render } from "@testing-library/react";
import App from "../App";

jest.mock("../Layout", () => ({
  Layout: ({ children }: { children: React.ReactNode }) => <div data-testid="layout">{children}</div>,
}));

jest.mock("../ChatPane", () => ({
  ChatPane: () => <div data-testid="chat-pane">ChatPane</div>,
}));

jest.mock("../../security", () => ({
  oktaClient: {},
  Secured: ({ children }: { children: React.ReactNode }) => <div data-testid="secured">{children}</div>,
}));

jest.mock("@okta/okta-react", () => ({
  Security: ({ children }: { children: React.ReactNode }) => <div data-testid="security">{children}</div>,
  useOktaAuth: () => ({
    authState: {
      isAuthenticated: true,
      idToken: {
        claims: {
          sub: "user123",
          firstName: "<PERSON>",
          lastName: "Doe",
          email: "<EMAIL>",
          primaryemail: "<EMAIL>",
        },
      },
    },
  }),
}));

jest.mock("launchdarkly-react-client-sdk", () => ({
  LDProvider: ({ children }: { children: React.ReactNode }) => <div data-testid="ld-provider">{children}</div>,
}));

jest.mock("../../config", () => ({
  launchDarkly: {
    clientId: "test-client-id",
  },
}));

describe("App", () => {
  it("renders the complete app structure", () => {
    const { getByTestId } = render(<App />);

    expect(getByTestId("security")).toBeInTheDocument();
    expect(getByTestId("layout")).toBeInTheDocument();
    expect(getByTestId("secured")).toBeInTheDocument();
    expect(getByTestId("ld-provider")).toBeInTheDocument();
    expect(getByTestId("chat-pane")).toBeInTheDocument();
  });

  it("uses FluentProvider with webLightTheme", () => {
    const { container } = render(<App />);

    const fluentProvider = container.querySelector('[class*="fui-FluentProvider"]');
    expect(fluentProvider).toBeInTheDocument();
  });
});

import React from "react";
import { render, screen } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { ChatPane } from "../ChatPane";

jest.mock("../valuations", () => ({
  ValuationsAgent: () => <div data-testid="valuations-agent">Valuations Agent</div>,
}));

jest.mock("../Footer", () => ({
  Footer: () => <div data-testid="footer">Footer</div>,
}));

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("ChatPane", () => {
  it("renders ValuationsAgent directly", () => {
    renderWithProvider(<ChatPane />);

    expect(screen.getByTestId("valuations-agent")).toBeInTheDocument();
    expect(screen.getByText("Valuations Agent")).toBeInTheDocument();
  });

  it("renders the Footer component", () => {
    renderWithProvider(<ChatPane />);

    expect(screen.getByTestId("footer")).toBeInTheDocument();
  });
});

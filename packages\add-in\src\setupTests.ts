import "@testing-library/jest-dom";

// Mock scrollIntoView for jsdom environment
Element.prototype.scrollIntoView = jest.fn();

global.Office = {
  context: {
    document: {},
    workbook: {},
  },
  onReady: jest.fn(),
} as unknown as typeof Office;

global.Excel = {
  run: jest.fn(),
  createSession: jest.fn(),
} as unknown as typeof Excel;

global.AppConfig = {
  apiClient: {
    baseUrl: "https://api2.dev.woodmac.com/valuations-assistant/",
  },
  okta: {
    issuer: "https://woodmackenzie.okta.com/oauth2/default",
    clientId: "test-client-id",
  },
  launchDarkly: {
    clientId: "test-launchdarkly-client-id",
  },
};

Object.defineProperty(window, "location", {
  value: {
    href: "http://localhost:3000",
    origin: "http://localhost:3000",
    protocol: "http:",
    host: "localhost:3000",
    hostname: "localhost",
    port: "3000",
    pathname: "/",
    search: "",
    hash: "",
  },
  writable: true,
});

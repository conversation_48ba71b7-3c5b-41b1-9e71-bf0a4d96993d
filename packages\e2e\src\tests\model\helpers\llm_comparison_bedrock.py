import boto3
import json

bedrock_runtime = boto3.client(service_name="bedrock-runtime")


def invoke_bedrock_claude_model(
    system_prompt, prompt, model_id="anthropic.claude-3-haiku-20240307-v1:0"
):
    # Example cost: 60/1000*0.0008+2/1000*0.004 = 0.000056
    request_body = {
        "anthropic_version": "bedrock-2023-05-31",
        "max_tokens": 500,
        "temperature": 0.7,
        "system": system_prompt,
        "messages": [
            {"role": "user", "content": prompt},
        ],
    }

    response = bedrock_runtime.invoke_model(
        modelId=model_id, body=json.dumps(request_body)
    )

    response_body = json.loads(response["body"].read().decode("utf-8"))
    return response_body["content"][0]["text"]


def invoke_bedrock_nova_model(system_prompt, prompt, model_id="amazon.nova-micro-v1:0"):
    # Example cost: 60/1000*0.000035+2/1000*0.00014 = 0.00000238
    request_body = {
        "schemaVersion": "messages-v1",
        "messages": [{"role": "user", "content": [{"text": prompt}]}],
        "system": [{"text": system_prompt}],
        "inferenceConfig": {
            "maxTokens": 100,
            "topP": 0.9,
            "temperature": 0,
        },
    }

    response = bedrock_runtime.invoke_model(
        modelId=model_id, body=json.dumps(request_body)
    )

    response_body = json.loads(response["body"].read().decode("utf-8"))
    return response_body["output"]["message"]["content"][0]["text"]


def invoke_bedrock_model(system_prompt: str, prompt: str, model_name: str):
    if "anthropic.claude" in model_name:
        return invoke_bedrock_claude_model(system_prompt, prompt, model_name)
    elif "amazon.nova" in model_name:
        return invoke_bedrock_nova_model(system_prompt, prompt, model_name)
    else:
        raise ValueError(f"Model {model_name} not supported")


def find_llm_similarity(
    model_output: str, expected_output: str, model_name: str = "amazon.nova-micro-v1:0"
):
    similarity_score = invoke_bedrock_model(
        system_prompt="You will be comparing 2 sentences. Provide the similarity score between the 2 sentences focusing on fundamental meaning. Provide just a number. Do not provide any more details.",
        prompt=f"a){model_output}\nb){expected_output}",
        model_name=model_name,
    )
    return float(similarity_score)

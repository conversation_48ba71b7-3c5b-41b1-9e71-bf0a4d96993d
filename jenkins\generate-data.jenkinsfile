#!/usr/bin/env groovy
@Library('utils') _

def sys = "NA"
AWS_REGION = 'us-east-1'
BUILD_ENVIRONMENT = woodmac.getJenkinsEnvironment()
jenkinsAgent = "harbor.prod.woodmac.com/liveservices/jenkins-node-jnlp-cloudformation:3283.v92c105e0f819-1-alpine-jdk21-aws2"

String getTaskRole(String environment) {
    return woodmac.getAgentRole(
        region: AWS_REGION,
        environment: environment,
        parameterName: "/${environment}/wmlgv-assistant-agent/deployment-agent-role-name"
    );
}

def syncData(String envPrefix) {
    dir('jenkins') {
        script {
            def sourcePath = "s3://wmlgv-aidev-assistant-valuations-data/parquet/"
            def destinationPath = "s3://wmlgv-${envPrefix}-assistant-valuations-data/parquet/"
            sh "aws s3 sync ${sourcePath} ${destinationPath} --metadata-directive REPLACE --delete"
        }
    }
}

properties([
    parameters([
        choice(
            name: 'DATASET',
            description: '''Select the dataset to generate:
            - all: Generate both datasets
            - all_upstream_assets: Generate data for all upstream assets
            - all_upstream_companies: Generate data for all upstream companies''',
            choices: ['all', 'all_upstream_assets', 'all_upstream_companies'],
        ),
        string(
            name: 'VERSION',
            description: 'Dataset version to use (use "latest" for most recent upstream version, or specify specific version)',
            defaultValue: 'latest',
            trim: true
        ),
        string(
            name: 'ASSETS_ZIP',
            description: 'Optional: Assets tar filename in S3 raw_zips bucket to use for combination (skip generation)',
            defaultValue: '',
            trim: true
        ),
        string(
            name: 'COMPANIES_ZIP',
            description: 'Optional: Companies tar filename in S3 raw_zips bucket to use for combination (skip generation)',
            defaultValue: '',
            trim: true
        )
    ])
])

def assetsZipFilename = params.ASSETS_ZIP
def companiesZipFilename = params.COMPANIES_ZIP
def skipGeneration = assetsZipFilename != '' || companiesZipFilename != ''

pipeline {
    agent none
    options {
        disableConcurrentBuilds()
        ansiColor('xterm')
        timeout(time: 2, unit: 'HOURS')
    }
    stages {
        stage('Generate data') {
            when {
                beforeAgent true
                expression {
                    return BUILD_ENVIRONMENT == 'dev' && !skipGeneration
                }
            }
            agent {
                ecs {
                    inheritFrom "dynamicFargate-us-east-1-aidev"
                    taskrole getTaskRole('aidev')
                    image "${jenkinsAgent}"
                    memory "6144"
                    memoryReservation "6144"
                    cpu "2048"
                }
            }
            environment {
                IDENTITY_URL = "https://identity-dev.woodmac.com"
                UVS_URL = "https://api2.dev.woodmac.com/upstream-valuation/upstream-valuation-service/develop-main"
                REPORT_PROVIDER_URL = "https://wmlgv-report-provider.dev.woodmac.com"
                DATA_REPOSITORY_URL = "https://wmlgv-data-repository.dev.woodmac.com"
                PRICING_URL = "https://api2.dev.woodmac.com/pricing"
                LENS_DIRECT_URL = "https://data.dev.woodmac.com"
                LENS_DIRECT_API_KEY = "682afb60-4f87-11ed-ae7d-ad7b1677c753"
            }
            stages {
                stage('prepare') {
                    steps {
                        sh '''
                        python3 -m venv venv
                        source venv/bin/activate
                        pip install -r packages/data-ingestion-utils/requirements.txt
                        '''
                    }
                }
                stage('Upstream asset scenarios') {
                    when {
                        expression {
                            return params.DATASET == 'all_upstream_assets' || params.DATASET == 'all'
                        }
                    }
                    environment {
                        LENS = credentials('valuations-lens-user')
                    }
                    steps {
                        dir('packages/data-ingestion-utils') {
                            sh """
                                source ../../venv/bin/activate
                                python generator.py --scenario all_upstream_assets --version ${params.VERSION}
                            """
                            script {
                                def timestamp = sh(script: "date +%s", returnStdout: true).trim()
                                assetsZipFilename = "assets_${timestamp}.tar.gz"
                                sh """
                                    cd output && find . -name "*.parquet" | tar -czf "../${assetsZipFilename}" -T -
                                    aws s3 cp "../${assetsZipFilename}" s3://wmlgv-aidev-assistant-valuations-data/raw_zips/
                                    cd ..
                                """
                            }
                            sh "rm -rf output"
                        }
                    }
                }
                stage('All companies with upstream assets') {
                    when {
                        expression {
                            return params.DATASET == 'all_upstream_companies' || params.DATASET == 'all'
                        }
                    }
                    environment {
                        LENS = credentials('valuations-lens-user')
                    }
                    steps {
                        dir('packages/data-ingestion-utils') {
                            sh """
                                source ../../venv/bin/activate
                                python generator.py --scenario all_upstream_companies --version ${params.VERSION}
                            """
                            script {
                                def timestamp = sh(script: "date +%s", returnStdout: true).trim()
                                companiesZipFilename = "companies_${timestamp}.tar.gz"
                                sh """
                                    cd output && find . -name "*.parquet" | tar -czf "../${companiesZipFilename}" -T -
                                    aws s3 cp "../${companiesZipFilename}" s3://wmlgv-aidev-assistant-valuations-data/raw_zips/
                                    cd ..
                                """
                            }
                            sh "rm -rf output"
                        }
                    }
                }
            }
        }
        
        stage('Combine data') {
            when {
                beforeAgent true
                expression {
                    return BUILD_ENVIRONMENT == 'dev' && (assetsZipFilename != "" || companiesZipFilename != "")
                }
            }
            agent {
                ecs {
                    inheritFrom "dynamicFargate-us-east-1-aidev"
                    taskrole getTaskRole('aidev')
                    image "${jenkinsAgent}"
                    memory "10240"
                    memoryReservation "10240"
                    cpu "2048"
                }
            }
            environment {
                IDENTITY_URL = "https://identity-dev.woodmac.com"
                UVS_URL = "https://api2.dev.woodmac.com/upstream-valuation/upstream-valuation-service/develop-main"
                REPORT_PROVIDER_URL = "https://wmlgv-report-provider.dev.woodmac.com"
                DATA_REPOSITORY_URL = "https://wmlgv-data-repository.dev.woodmac.com"
                PRICING_URL = "https://api2.dev.woodmac.com/pricing"
                LENS_DIRECT_URL = "https://data.dev.woodmac.com"
                LENS_DIRECT_API_KEY = "682afb60-4f87-11ed-ae7d-ad7b1677c753"
            }
            stages {
                stage('prepare') {
                    steps {
                        sh '''
                        python3 -m venv venv
                        source venv/bin/activate
                        pip install -r packages/data-ingestion-utils/requirements.txt
                        '''
                    }
                }
                stage('Combine assets data') {
                    when {
                        expression {
                            return assetsZipFilename != ""
                        }
                    }
                    environment {
                        LENS = credentials('valuations-lens-user')
                    }
                    steps {
                        dir('packages/data-ingestion-utils') {
                            sh """
                                mkdir -p output
                                aws s3 cp s3://wmlgv-aidev-assistant-valuations-data/raw_zips/${assetsZipFilename} ./
                                tar -xzf ${assetsZipFilename} -C output
                                source ../../venv/bin/activate
                                python generator.py --scenario all_upstream_assets --combine --reports-dir output
                                aws s3 cp output/all_upstream_assets_discrete_combined.parquet s3://wmlgv-aidev-assistant-valuations-data/parquet/all_upstream_assets_discrete_combined.parquet
                                aws s3 cp output/all_upstream_assets_timeseries_combined.parquet s3://wmlgv-aidev-assistant-valuations-data/parquet/all_upstream_assets_timeseries_combined.parquet
                            """
                            archiveArtifacts artifacts: 'output/all_upstream_assets_*.parquet'
                            sh "rm -rf output ${assetsZipFilename}"
                        }
                    }
                }
                stage('Combine companies data') {
                    when {
                        expression {
                            return companiesZipFilename != ""
                        }
                    }
                    environment {
                        LENS = credentials('valuations-lens-user')
                    }
                    steps {
                        dir('packages/data-ingestion-utils') {
                            sh """
                                mkdir -p output
                                aws s3 cp s3://wmlgv-aidev-assistant-valuations-data/raw_zips/${companiesZipFilename} ./
                                tar -xzf ${companiesZipFilename} -C output
                                source ../../venv/bin/activate
                                python generator.py --scenario all_upstream_companies --combine --reports-dir output
                                aws s3 cp output/all_upstream_companies_discrete_combined.parquet s3://wmlgv-aidev-assistant-valuations-data/parquet/all_upstream_companies_discrete_combined.parquet
                                aws s3 cp output/all_upstream_companies_timeseries_combined.parquet s3://wmlgv-aidev-assistant-valuations-data/parquet/all_upstream_companies_timeseries_combined.parquet
                            """
                            archiveArtifacts artifacts: 'output/all_upstream_companies_*.parquet'
                            sh "rm -rf output ${companiesZipFilename}"
                        }
                    }
                }
            }
        }
        
        stage('Sync data buckets') {
            when {
                beforeAgent true
                expression {
                    return BUILD_ENVIRONMENT == 'prod'
                }
            }
            stages {
                stage('Sync INT') {
                    agent {
                        ecs {
                            inheritFrom "dynamic-us-east-1-aiint"
                            taskrole getTaskRole('aiint')
                            image "${jenkinsAgent}"
                        }
                    }
                    steps {
                        syncData('aiint')
                    }
                }
                stage('Sync UAT') {
                    agent {
                        ecs {
                            inheritFrom "dynamic-us-east-1-aiuat"
                            taskrole getTaskRole('aiuat')
                            image "${jenkinsAgent}"
                        }
                    }
                    steps {
                        syncData('aiuat')
                    }
                }
                stage('Sync PROD') {
                    agent {
                        ecs {
                            inheritFrom "dynamic-us-east-1-aiprod"
                            taskrole getTaskRole('aiprod')
                            image "${jenkinsAgent}"
                        }
                    }
                    steps {
                        syncData('aiprod')
                    }
                }
            }
        }
    }
}

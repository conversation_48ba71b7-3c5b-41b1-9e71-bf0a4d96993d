from pathlib import Path
import sys
import time
import concurrent.futures
import requests
import os
import argparse
from valuation_client import ValuationClient
from pricing_client import PricingClient
from asset_json_flattener import combine_parquet_files
from lens_direct_utils import get_all_company_filter_batches
from dotenv import load_dotenv
from lens_direct_metadata_provider import LensDirectMetadataProvider

load_dotenv()

# batches with all regions/all assets
upstream_asset_batches = [
    {
        "superRegion": {
            "queryType": "terms",
            "values": [
                "Africa",
                "Antarctica",
                "Asia",
            ],
            "includeEmpty": False,
            "invertFilter": False,
        },
    },
    {
        "superRegion": {
            "queryType": "terms",
            "values": [
                "Europe",
                "International Waters",
                "Latin America and the Caribbean",
            ],
            "includeEmpty": False,
            "invertFilter": False,
        },
    },
    {
        "superRegion": {
            "queryType": "terms",
            "values": [
                "Middle East",
                "North America",
                "Oceania",
                "Russia and the Caspian",
            ],
            "includeEmpty": False,
            "invertFilter": False,
        },
    },
]

SCENARIOS = {
    "all_upstream_assets": upstream_asset_batches,
    "all_upstream_companies": get_all_company_filter_batches,
}


def get_auth_token() -> str:
    if os.environ.get("IDENTITY_TOKEN"):
        return os.environ.get("IDENTITY_TOKEN")
    username = os.environ.get("LENS_USR")
    password = os.environ.get("LENS_PSW")
    if not username or not password:
        raise ValueError(
            "LENS_USR and LENS_PSW must be provided as environment variables"
        )
    url = f"{os.environ.get('IDENTITY_URL')}/openam/identity/authenticate?username={username}&password={password}"

    response = requests.get(url)
    response.raise_for_status()

    token_text = response.text
    if token_text.startswith("token.id="):
        token = token_text.split("=", 1)[1]
        if not token:
            raise ValueError("Invalid response format - token not found")
        return token
    else:
        raise ValueError("Invalid response format - token not found")


def get_valuation_ids_from_output(reports_dir: str) -> list:
    output_dir = Path(reports_dir)
    valuation_ids = []

    if not output_dir.exists():
        return valuation_ids

    for item in output_dir.iterdir():
        if item.is_dir():
            parquet_files = list(item.glob("*.parquet"))
            if parquet_files:
                valuation_ids.append(item.name)

    return valuation_ids


def combine_parquet_output(scenario: str, valuation_ids: list = None):
    if not valuation_ids:
        return

    start_time = time.time()
    combine_parquet_files(
        [f"output/{v}/flattened_discrete.parquet" for v in valuation_ids],
        f"{scenario}_discrete",
        include_timestamp=False,
    )
    print(
        "Combined discrete parquet files in %.2f seconds"
        % (time.time() - start_time),
        flush=True,
    )

    start_time = time.time()
    combine_parquet_files(
        [f"output/{v}/flattened_timeseries.parquet" for v in valuation_ids],
        f"{scenario}_timeseries",
        include_timestamp=False,
    )
    print(
        "Combined timeseries parquet files in %.2f seconds"
        % (time.time() - start_time),
        flush=True,
    )


def generate_data(scenario: str, version: str = "latest", max_retries: int = 0, include_lens_metadata: bool = True):
    load_dotenv()
    batches = SCENARIOS[scenario]
    if not batches:
        raise ValueError(f"Invalid scenario: {scenario}")
    if callable(batches):
        batches = batches()

    uvs_url = os.environ.get("UVS_URL")
    report_provider_url = os.environ.get("REPORT_PROVIDER_URL")
    data_repository_url = os.environ.get("DATA_REPOSITORY_URL")
    pricing_url = os.environ.get("PRICING_URL")
    if (
        not uvs_url
        or not report_provider_url
        or not data_repository_url
        or not pricing_url
    ):
        raise ValueError(
            "UVS_URL, REPORT_PROVIDER_URL, DATA_REPOSITORY_URL and PRICING_URL must be provided as environment variables"
        )
    token = get_auth_token()

    lens_direct_metadata_provider = LensDirectMetadataProvider(scenario=scenario, max_retries=max_retries) if include_lens_metadata else None
    
    valuations_client = ValuationClient(
        uvs_url=uvs_url,
        report_provider_url=report_provider_url,
        data_repository_url=data_repository_url,
        token=token,
        lens_direct_metadata_provider=lens_direct_metadata_provider
    )
    price_decks = PricingClient(
        pricing_url=pricing_url,
        token=token,
    ).get_wm_price_decks()
    print(
        f"Going to run {len(batches)} batches (with {len(price_decks)} pricing scenarios)...",
        flush=True,
    )
    valuations = []
    failed_valuations = []
    for batch in batches:
        filters = batch
        type = "company" if "companyName" in filters else "asset"
        print(f"Running batch number {batches.index(batch) + 1}", flush=True)

        def process_price_deck(price_deck, retry_count=0):
            try:
                post_body = {
                    "type": type,
                    "filters": filters,
                    "priceDeckId": price_decks[price_deck],
                    "version": version,
                }
                valuation_id = valuations_client.run_valuation(post_body)
                valuations_client.save_valuation_report(
                    valuation_id,
                    price_deck,
                    f"all-{type}-{price_deck}-{version}",
                    version,
                )
                return valuation_id
            except Exception as e:
                if retry_count < max_retries:
                    print(
                        f"Retrying for price deck {price_deck} (attempt {retry_count + 1}/{max_retries})",
                        flush=True,
                    )
                    return process_price_deck(price_deck, retry_count + 1)
                raise e

        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = {
                executor.submit(process_price_deck, price_deck): price_deck
                for price_deck in price_decks
            }
            for future in concurrent.futures.as_completed(futures):
                price_deck = futures[future]
                try:
                    valuation_id = future.result()
                    valuations.append(valuation_id)
                except Exception as e:
                    print(f"Error processing price deck {price_deck}: {e}", flush=True)
                    failed_valuations.append(
                        {"filters": filters, "price_deck": price_deck}
                    )

    if lens_direct_metadata_provider:
        lens_direct_metadata_provider.print_count_assets_not_found()
    
    if len(failed_valuations) > 0:
        print(
            f"Failed to process the following requests: {failed_valuations}",
            flush=True,
        )
        sys.exit(1)

    return valuations

if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Generate data for different scenarios"
    )
    parser.add_argument(
        "--scenario",
        choices=list(SCENARIOS.keys()),
        help="Scenario to generate data for (choices: %(choices)s)",
    )
    parser.add_argument(
        "--version", default="latest", help="Version to use (default: %(default)s)"
    )
    parser.add_argument(
        "--max-retries",
        type=int,
        default=5,
        help="Maximum number of retry attempts for each valuation request (default: %(default)s)",
    )
    parser.add_argument(
        "--combine",
        action="store_true",
        help="Combine output files after generation",
    )
    parser.add_argument(
        "--no-include-lens-metadata",
        dest="include_lens_metadata",
        action="store_false",
        default=True,
        help="Do not include lens metadata (default: include lens metadata)",
    )
    output_group = parser.add_mutually_exclusive_group()
    output_group.add_argument(
        "--valuation-ids",
        nargs="+",
        help="List of valuation IDs to combine (if provided, skips generation)",
    )
    output_group.add_argument(
        "--reports-dir",
        help="Directory containing valuation reports to combine (mutually exclusive with --valuation-ids)",
    )

    args = parser.parse_args()

    start_time = time.time()

    valuation_ids = None
    if args.valuation_ids:
        valuation_ids = args.valuation_ids
    elif args.reports_dir:
        valuation_ids = get_valuation_ids_from_output(args.reports_dir)
        print(
            "Valuation IDs found in reports directory:", len(valuation_ids), flush=True
        )
        if not valuation_ids:
            print(
                f"No valuation IDs found in directory: {args.reports_dir}", flush=True
            )
            sys.exit(1)
    if valuation_ids:
        combine_parquet_output(args.scenario, valuation_ids)
    else:
        valuation_ids = generate_data(args.scenario, args.version, args.max_retries, args.include_lens_metadata)

        if args.combine:
            combine_parquet_output(args.scenario, valuation_ids)

    elapsed = time.time() - start_time
    print(f"Total time spent: {elapsed:.2f} seconds", flush=True)
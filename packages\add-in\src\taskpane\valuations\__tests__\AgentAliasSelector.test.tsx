import React from "react";
import { render, screen, waitFor } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { AgentAliasSelector } from "../AgentAliasSelector";


describe("AgentAliasSelector", () => {
    const mockOnSelectedAlias = jest.fn();
    const availableAliases = [
        { agentAliasId: "1", agentAliasName: "Alias 1" },
        { agentAliasId: "2", agentAliasName: "Alias 2" },
    ];
    const selectedAlias = availableAliases[0];

    it("renders available aliases", async () => {
        render(
            <AgentAliasSelector
                availableAliases={availableAliases}
                onSetSelectedAlias={mockOnSelectedAlias}
            />
        );

        const comboBox = screen.getByRole("combobox");
        expect(comboBox).toBeInTheDocument();
        userEvent.click(comboBox);
        
        const option1 = await screen.findByRole("option", { name: "<PERSON>as 1" });
        expect(option1).toBeInTheDocument();
        expect(screen.getByRole("option", { name: "<PERSON>as 2" })).toBeInTheDocument();
    });

    it("renders the selected alias", async () => {
        render(
            <AgentAliasSelector
                availableAliases={availableAliases}
                selectedAlias={selectedAlias}
                onSetSelectedAlias={mockOnSelectedAlias}
            />
        );

        expect(screen.getByText(selectedAlias.agentAliasName)).toBeInTheDocument();
    });

    it("calls onSetSelectedAlias when an alias is selected", async () => {
        render(
            <AgentAliasSelector
                availableAliases={availableAliases}
                selectedAlias={selectedAlias}
                onSetSelectedAlias={mockOnSelectedAlias}
            />
        );

        const comboBox = screen.getByRole("combobox");
        userEvent.click(comboBox);

        const option = await screen.findByRole("option", { name: "Alias 2" });
        userEvent.click(option);
        await waitFor(() => expect(mockOnSelectedAlias).toHaveBeenCalledWith("2"));
    });
});
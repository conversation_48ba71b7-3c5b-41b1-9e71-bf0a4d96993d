import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { ValuationsAgent } from "../ValuationsAgent";

const mockAddTableToExcel = jest.fn();
const mockHandleChatMessageEntered = jest.fn();
const mockOnSetSelectedAlias = jest.fn();

const availableAliases = [
  { agentAliasId: "v1", agentAliasName: "Valuations Agent v1" },
  { agentAliasId: "v2", agentAliasName: "Valuations Agent v2" },
];

jest.mock("../use-valuations-agent", () => ({
  useValuationsAgent: () => ({
    chatHistory: [
      { role: "user", message: "Show me some data" },
      {
        role: "assistant",
        message: "Here is your valuation data",
        additionalData: [
          ["Company", "Value"],
          ["ABC Corp", "1000"],
          ["XYZ Inc", "2000"],
        ],
      },
    ],
    loading: false,
    addTableToExcel: mockAddTableToExcel,
    handleChatMessageEntered: mockHandleChatMessageEntered,
    showSuggestedQuestions: true,
    suggestedQuestions: ["Show me recent valuations", "Compare companies"],
    onSetSelectedAlias: mockOnSetSelectedAlias,
    availableAliases,
    selectedAlias: availableAliases[0],
  }),
}));

jest.mock("../../Agent", () => ({
  Agent: ({
    howCanIHelpMessage,
    chatHistory,
    loading,
    handleChatMessageEntered,
    showSuggestedQuestions,
    suggestedQuestions,
    botMessageFooterComponentsCreator,
    header,
  }: any) => {
    const tableData = chatHistory.find((msg: any) => msg.additionalData)?.additionalData;

    return (
      <div data-testid="agent">
        <div data-testid="header">{header}</div>
        <div data-testid="help-message">{howCanIHelpMessage}</div>
        <div data-testid="loading-state">{loading ? "Loading" : "Not Loading"}</div>
        <div data-testid="suggested-questions-enabled">{showSuggestedQuestions ? "Yes" : "No"}</div>
        <div data-testid="suggested-questions-count">{suggestedQuestions?.length || 0}</div>
        <button data-testid="handle-message" onClick={() => handleChatMessageEntered("Test message")}>
          Send Message
        </button>
        {tableData && (
          <div data-testid="footer-components">
            {botMessageFooterComponentsCreator(tableData).map((component: any, index: number) => (
              <div key={index} data-testid={`footer-component-${index}`}>
                {component}
              </div>
            ))}
          </div>
        )}
      </div>
    );
  },
}));

jest.mock("../AgentAliasSelector", () => ({
  AgentAliasSelector: ({
    availableAliases, selectedAlias, onSetSelectedAlias
  }: any) => (    
    <>
      <div data-testid="agent-alias-selector">
        <div data-testid="alias-select">{availableAliases.length}</div>
        <div data-testid="selected-alias">{selectedAlias?.agentAliasName}</div> 
        <button
          data-testid="set-alias-button"
          onClick={() => onSetSelectedAlias(availableAliases[1])}
        >
          Set Alias
        </button> 
      </div>
    </>
    ),
}));


jest.mock("../../use-styles", () => ({
  useStyles: () => ({
    buttonIcon: "mock-button-icon-class",
  }),
}));

jest.mock("@fluentui/react-components", () => ({
  Button: ({ children, onClick }: any) => (
    <button onClick={onClick} data-testid="fluent-button">
      {children}
    </button>
  ),
}));

jest.mock("@fluentui/react-icons", () => ({
  AddRegular: ({ className }: any) => (
    <span data-testid="add-icon" className={className}>
      +
    </span>
  ),
}));

describe("ValuationsAgent", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders with correct help message", () => {
    render(<ValuationsAgent />);

    expect(screen.getByTestId("help-message")).toHaveTextContent("How can I help you with Valuations?");
  });

  it("passes chat history to Agent component", () => {
    render(<ValuationsAgent />);

    expect(screen.getByTestId("agent")).toBeInTheDocument();
  });

  it("passes loading state to Agent component", () => {
    render(<ValuationsAgent />);

    expect(screen.getByTestId("loading-state")).toHaveTextContent("Not Loading");
  });

  it("enables suggested questions", () => {
    render(<ValuationsAgent />);

    expect(screen.getByTestId("suggested-questions-enabled")).toHaveTextContent("Yes");
    expect(screen.getByTestId("suggested-questions-count")).toHaveTextContent("2");
  });

  it("handles chat message entry", async () => {
    const user = userEvent.setup();
    render(<ValuationsAgent />);

    const sendButton = screen.getByTestId("handle-message");
    await user.click(sendButton);

    expect(mockHandleChatMessageEntered).toHaveBeenCalledWith("Test message");
  });

  it("creates footer components for table data", () => {
    render(<ValuationsAgent />);

    const footerComponents = screen.getByTestId("footer-components");
    expect(footerComponents).toBeInTheDocument();

    const component0 = screen.getByTestId("footer-component-0");
    const component1 = screen.getByTestId("footer-component-1");

    expect(component0).toBeInTheDocument();
    expect(component1).toBeInTheDocument();
  });

  it("renders add to sheet buttons with correct icons", () => {
    render(<ValuationsAgent />);

    const buttons = screen.getAllByTestId("fluent-button");
    expect(buttons).toHaveLength(2);

    const icons = screen.getAllByTestId("add-icon");
    expect(icons).toHaveLength(2);

    icons.forEach((icon) => {
      expect(icon).toHaveClass("mock-button-icon-class");
      expect(icon).toHaveTextContent("+");
    });
  });

  it("calls addTableToExcel without chart when first button is clicked", async () => {
    const user = userEvent.setup();
    render(<ValuationsAgent />);

    const buttons = screen.getAllByTestId("fluent-button");
    await user.click(buttons[0]);

    expect(mockAddTableToExcel).toHaveBeenCalledWith([
      ["Company", "Value"],
      ["ABC Corp", "1000"],
      ["XYZ Inc", "2000"],
    ]);
  });

  it("calls addTableToExcel with chart when second button is clicked", async () => {
    const user = userEvent.setup();
    render(<ValuationsAgent />);

    const buttons = screen.getAllByTestId("fluent-button");
    await user.click(buttons[1]);

    expect(mockAddTableToExcel).toHaveBeenCalledWith(
      [
        ["Company", "Value"],
        ["ABC Corp", "1000"],
        ["XYZ Inc", "2000"],
      ],
      true
    );
  });

  it("footer components creator filters falsy values correctly", () => {
    const footerComponentsCreator = (table: any) =>
      [table && [<div>Button 1</div>, <div>Button 2</div>]].flat().filter(Boolean);

    const result = footerComponentsCreator(null);
    expect(result).toEqual([]);

    const resultUndefined = footerComponentsCreator(undefined);
    expect(resultUndefined).toEqual([]);
  });

  it("integrates all hook values correctly", () => {
    render(<ValuationsAgent />);

    expect(screen.getByTestId("agent")).toBeInTheDocument();
    expect(screen.getByTestId("help-message")).toHaveTextContent("How can I help you with Valuations?");
    expect(screen.getByTestId("loading-state")).toHaveTextContent("Not Loading");
    expect(screen.getByTestId("suggested-questions-enabled")).toHaveTextContent("Yes");
    expect(screen.getByTestId("suggested-questions-count")).toHaveTextContent("2");
  });

  it("renders alias select with available aliases", () => {
    render(<ValuationsAgent />);

    const aliasSelect = screen.getByTestId("alias-select");
    expect(aliasSelect).toBeInTheDocument();
    expect(aliasSelect).toHaveTextContent("2");
  });

  it("calls onSetSelectedAlias when alias is changed", async () => {
    const user = userEvent.setup();
    render(<ValuationsAgent />);

    const aliasSelect = screen.getByTestId("set-alias-button");
    await user.click(aliasSelect);
    expect(mockOnSetSelectedAlias).toHaveBeenCalledWith(availableAliases[1]);
  });
});

describe("ValuationsAgent footerComponentsCreator", () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("returns empty array when table is falsy", () => {
    const footerComponentsCreator = (table: any) =>
      [table && [<div>Button 1</div>, <div>Button 2</div>]].flat().filter(Boolean);

    expect(footerComponentsCreator(null)).toEqual([]);
    expect(footerComponentsCreator(undefined)).toEqual([]);
    expect(footerComponentsCreator(false)).toEqual([]);
  });

  it("creates correct buttons for valid table data", () => {
    render(<ValuationsAgent />);

    const footerComponents = screen.getByTestId("footer-components");
    expect(footerComponents).toBeInTheDocument();

    const buttons = screen.getAllByTestId("fluent-button");
    expect(buttons).toHaveLength(2);

    expect(buttons[0]).toHaveTextContent("Add to a new sheet");
    expect(buttons[1]).toHaveTextContent("Add chart to a new sheet");
  });
});

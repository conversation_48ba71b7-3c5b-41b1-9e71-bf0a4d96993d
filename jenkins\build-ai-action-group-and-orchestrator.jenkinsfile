#!/usr/bin/env groovy
@Library('utils') _

AWS_REGION = 'us-east-1'
BUILD_ENVIRONMENT = woodmac.getJenkinsEnvironment()

String getEnvironment() {
    return 'dev'
}

def getAvailableEnvironments() {
    return 'dev'
}

def buildVersion = null

def getVersion() {
    def commitHash = sh(script: 'git rev-parse --short HEAD', returnStdout: true).trim()
    def timestamp = new Date().format('yyyyMMdd-HHmmss')
    return "${timestamp}-${commitHash}"
}

properties([
    parameters([
        choice(name: 'TARGET_ENVIRONMENT', description: 'Environment to deploy', choices: getAvailableEnvironments()),
    ])
])

environmentName = getEnvironment()
AGENT_IAM_ROLE_PARAMETER_NAME = "/${environmentName}/wmim/agent-role-name"
jenkinsAgentForPythonWIthRust = "harbor.prod.woodmac.com/wm-lens-markets/lens-markets-jenkins-node-rust:latest"
jenkinsAgentForDocker = "harbor.prod.woodmac.com/liveservices/jenkins-node-jnlp-docker:3256.v88a_f6e922152-4-alpine-jdk21"

def nexusCredentials;

pipeline {
    agent none
    options {
        disableConcurrentBuilds()
        ansiColor('xterm')
    }
    stages {
        stage('action group build agent') {
            agent  {
                ecs {
                    inheritFrom "dynamic-us-east-1-${environmentName}"
                    taskrole woodmac.getAgentRole(
                        region: 'us-east-1',
                        environment: "${environmentName}",
                        parameterName: "${AGENT_IAM_ROLE_PARAMETER_NAME}",
                    )
                    image "${jenkinsAgentForPythonWIthRust}"
                }
            }
            stages {
                stage('set version') {
                    steps {
                        script {
                            // Set only the buildVersion
                            buildVersion = getVersion()
                            echo "Set version to ${buildVersion}"
                        }
                    }
                }
                stage('build action group') {
                    steps {
                        dir('packages/agent') {
                            sh(
                                label: "Starting action group build...",
                                script: """
                                    mkdir -p package

                                    cp -r tools package/
                                    cp *.py package/
                                    cp requirements.txt package/
                                    cp wmlgv-assistant-agent-schema.yaml package/
                                    pip install --target ./package -r ./package/requirements.txt
                                """
                            )   

                            zip zipFile: "wmlgv-assistant-agent-${buildVersion}.zip", dir: 'package'
                        }
                    }
                }
                stage('publish action group to nexus') {
                    when {
                        branch 'main'
                    }
                    steps {
                        script {
                            nexusCredentials = nexus.getCredentials('us-east-1', 'prod')
                        }
                        dir('packages/agent') {
                            script {
                                echo "Pushing lambda zip to Nexus"

                                nexus.publish(
                                    type: 'raw',
                                    env: 'prod',
                                    credentials: nexusCredentials,
                                    repository: 'lens-internal-modelling',
                                    source_file: "wmlgv-assistant-agent-${buildVersion}.zip",
                                    folder: "wmlgv/assistant-agent-action-group",
                                    verbose: true)
                                
                                echo "Pushed lambda zip to Nexus"
                            }
                        }
                    }
                }
            }
        }
        stage('orchestrator build agent') {
            agent  {
                ecs {
                    inheritFrom "dynamic-us-east-1-${environmentName}"
                    taskrole woodmac.getAgentRole(
                        region: 'us-east-1',
                        environment: "${environmentName}",
                        parameterName: "${AGENT_IAM_ROLE_PARAMETER_NAME}",
                    )
                    image "${jenkinsAgentForDocker}"
                }
            }
            environment {
                NODE_OPTIONS="--max_old_space_size=512"
            }
            stages {
                stage('static checks') {
                    steps {
                        dir('packages/orchestrator') {
                            script {
                                sh """
                                    npm ci
                                    npm run lint
                                    npm run test:coverage
                                """
                            }
                        }
                    }
                }
                stage('build orchestrator') {
                    steps {
                        dir('packages/orchestrator') {
                            script {
                                echo "Starting orchestrator build version: ${buildVersion}"
                                sh """
                                    docker build -t wmlgv-assistant-orchestrator:${buildVersion} .
                                    docker tag wmlgv-assistant-orchestrator:${buildVersion} wmlgv-assistant-orchestrator:latest
                                """
                            }
                        }
                    }
                }
                stage('push orchestrator to nexus') {
                    when {
                        branch 'main'
                    }
                    steps {
                        script {
                            nexusCredentials = nexus.getCredentials('us-east-1', 'prod')
                        }
                        dir('packages/orchestrator') {
                            echo "Pushing orchestrator image version to Nexus: ${buildVersion}"

                            script {
                                nexus.publish(
                                    type: 'docker',
                                    env: 'prod',
                                    credentials: nexusCredentials,
                                    dockerImage: "wmlgv-assistant-orchestrator:${buildVersion}",
                                    projectPrefix: "woodmac2.0/wmlgv",
                                    tag: "${buildVersion}",
                                )
                            }
                        }
                    }
                }
            }
        }
        stage('trigger deploy') {
            when {
                branch 'main'
            }
            steps {
                script {       
                    build job: "Valuations/Valuations AI/AI Account Pipelines/AI Deploy/main",
                    parameters: [
                        string(name: 'LAMBDA_VERSION', value: "${buildVersion}")
                    ],
                    wait: false,
                    propagate: false
                }
                script {       
                    build job: "Valuations/Valuations AI/AI Account Pipelines/AI ECS Deploy/main",
                    parameters: [
                        string(name: 'DOCKER_VERSION', value: "${buildVersion}"),
                        string(name: 'TARGET_ENVIRONMENT', value: 'aidev')
                    ],
                    wait: false,
                    propagate: false
                }
            }
        }
    }
}

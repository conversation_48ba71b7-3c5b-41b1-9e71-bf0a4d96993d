import os
import requests
import csv
import time
from typing import List


class LensDirectQueryBuilder:
    def __init__(self, base_url, api_key):
        self.base_url = base_url
        self.api_key = api_key
        self.select = []
        self.filter = ""
        self.orderby = ""
        self.top = None
        self.apply = ""

    def with_select(self, fields):
        self.select = fields
        return self

    def with_filter(self, filter_expression: str):
        self.filter = filter_expression.replace("&", "%26").replace("`", "'")
        return self

    def with_order_by(self, order_expression):
        self.orderby = order_expression
        return self

    def with_top(self, limit):
        self.top = limit
        return self

    def with_aggregation(self, group_by_fields, aggregations):
        group_by_part = f"groupby(({','.join(group_by_fields)}),"

        aggregate_parts = [
            f"{agg['field']} with {agg['operation']} as {agg['alias']}"
            for agg in aggregations
        ]

        self.apply = f"{group_by_part}aggregate({','.join(aggregate_parts)}))"
        return self

    def build_url(self):
        params = []

        if self.select:
            params.append(f"$select={','.join(self.select)}")

        if self.filter:
            params.append(f"$filter={self.filter}")

        if self.apply:
            params.append(f"$apply={self.apply}")

        if self.orderby:
            params.append(f"$orderby={self.orderby}")

        if self.top is not None:
            params.append(f"$top={self.top}")

        return f"{self.base_url}{'?' if params else ''}{'&'.join(params)}"

    def _request(self, url, max_retries=1):
        """
        Internal method to make HTTP requests with retry logic for applicable HTTP status codes.
        
        Args:
            url (str): The URL to request
            max_retries (int): Maximum number of retries (default: 1)
            
        Returns:
            dict: JSON response data
            
        Raises:
            requests.exceptions.HTTPError: If request fails after all retries
        """
        headers = {"apiKey": self.api_key}
        
        retryable_status_codes = {429, 500, 502, 503, 504}
        
        for attempt in range(max_retries + 1):
            try:
                response = requests.get(url, headers=headers)
                
                if response.status_code == 200:
                    return response.json()
                
                if response.status_code in retryable_status_codes and attempt < max_retries:
                    MAX_WAIT_TIME = 30  # Maximum wait time in seconds
                    wait_time = min(2 ** attempt, MAX_WAIT_TIME)  # Exponential backoff with a maximum wait time (avoiding too long waits after multiple retries)
                    print(f"Request failed with status {response.status_code}. Retrying in {wait_time}s... (attempt {attempt + 1}/{max_retries + 1})", flush=True)
                    time.sleep(wait_time)
                    continue
                
                response.raise_for_status()
                
            except requests.exceptions.RequestException as e:
                if attempt < max_retries:
                    wait_time = 2 ** attempt
                    print(f"Network error: {e}. Retrying in {wait_time}s... (attempt {attempt + 1}/{max_retries + 1})", flush=True)
                    time.sleep(wait_time)
                    continue
                raise e

    def _execute_single_page(self, max_retries=1):
        url = self.build_url()
        print(f"Executing query: {url}", flush=True)
        return self._request(url, max_retries)
    
    def execute(self, max_retries=1):
        values = []
        data = self._execute_single_page(max_retries)
        values.extend(data["value"])
        if data.get("@odata.nextLink"):
            next_url = data["@odata.nextLink"]
            count_pages = 0
            while next_url:
                count_pages += 1
                data = self._request(next_url, max_retries)
                values.extend(data["value"])           
                next_url = data.get("@odata.nextLink")

        return values

def get_lens_direct_url_and_token():
    lens_direct_url = os.getenv("LENS_DIRECT_URL")
    api_key = os.getenv("LENS_DIRECT_API_KEY")

    if not lens_direct_url:
        raise ValueError("LENS_DIRECT_URL environment variable is not set.")
    if not api_key:
        raise ValueError("LENS_DIRECT_API_KEY environment variable is not set.")
    
    return lens_direct_url, api_key

def fetch_valuable_companies(max_retries=1):
    company_ids = {}

    lens_direct_url, api_key = get_lens_direct_url_and_token()

    url_builder = LensDirectQueryBuilder(
        f"{lens_direct_url}/qe/v1/odata/upstream_weekly/field_company_history",
        api_key,
    )
    url_builder.with_select(["id_company", "company_name", "asset_count"])
    filter_expr = (
        "company_is_top_level eq 'Y' and "
        "field_interest_is_latest eq 'Y' and "
        "field_interest ge 0 and "
        "field_gem_filename ne '' and "
        # excluding these companies because they have data issues...
        "company_name ne 'Private Investors' and "
        "company_name ne 'SNH' and "
        "company_name ne 'Government of Gabon'"
    )
    url_builder.with_filter(filter_expr)
    url_builder.with_aggregation(
        ["company_name", "id_company"],
        [
            {
                "field": "field_name",
                "operation": "countdistinct",
                "alias": "asset_count",
            }
        ],
    )
    url_builder.with_order_by("asset_count desc")
    
    def update_company_ids(values):
        for item in values:
            company_ids[item["company_name"]] = {
                "id": item["id_company"],
                "asset_count": item["asset_count"],
            }
    
    data = url_builder.execute(max_retries)
    update_company_ids(data)

    return company_ids


def batch_companies_by_asset_count(company_id_map, max_assets=3000, max_companies=300):
    batches = []
    current_batch = []
    current_asset_count = 0

    for company_tuple in company_id_map.items():
        _, info = company_tuple

        if (
            current_asset_count + info["asset_count"] > max_assets
            and current_batch
            or len(current_batch) >= max_companies
        ):
            batches.append(current_batch)
            current_batch = []
            current_asset_count = 0

        current_batch.append(company_tuple)
        current_asset_count += info["asset_count"]

    if current_batch:
        batches.append(current_batch)

    return batches


def convert_batches_to_filters(batches):
    filter_batches = []

    for batch in batches:
        filter_batches.append(
            {
                "companyName": {
                    "name": "companyName",
                    "queryType": "terms",
                    "values": [str(company_info[1]["id"]) for company_info in batch],
                    "invertFilter": False,
                    "includeEmpty": False,
                }
            }
        )

    return filter_batches


def read_companies_from_csv(file_path):
    company_ids = {}

    with open(file_path, mode="r") as csv_file:
        csv_reader = csv.DictReader(csv_file)
        for row in csv_reader:
            company_ids[row["company_name"]] = {
                "id": row["id_company"],
                "asset_count": int(row["asset_count"]),
            }

    return company_ids

def get_static_list_upstream_companies():
    """
    Reads a static list of upstream companies from a CSV file.
    The CSV file should contain columns: company_name, id_company, asset_count.
    """
    return read_companies_from_csv('static/upstream_companies.csv')


def get_all_company_filter_batches():
    # Uncomment when Lens Direct API is fixed
    # company_id_map = fetch_valuable_companies()
    company_id_map = get_static_list_upstream_companies()
    batches = batch_companies_by_asset_count(company_id_map)
    filter_batches = convert_batches_to_filters(batches)

    return filter_batches


def fetch_field_table(columns: List[str] = None, filter: str = None, max_retries=1):
    lens_direct_url, api_key = get_lens_direct_url_and_token()
    url_builder = LensDirectQueryBuilder(
        f"{lens_direct_url}/query/upstream_weekly/imp-met/odata/field",
        api_key,
    )
    if columns:
        url_builder.with_select(columns + ["id_valuation", "id_field_gem"])
    if filter:
        url_builder.with_filter(filter)

    rows = url_builder.execute(max_retries)
    print(f"Fetched {len(rows)} rows from the Lens Direct field table.", flush=True)
    return rows

def fetch_lng_plant_table(columns: List[str] = None, filter: str = None, max_retries=1):
    lens_direct_url, api_key = get_lens_direct_url_and_token()
    url_builder = LensDirectQueryBuilder(
        f"{lens_direct_url}/query/upstream_weekly/imp-met/odata/lng_plant",
        api_key,
    )
    if columns:
        url_builder.with_select(columns + ["id_valuation"])
    if filter:
        url_builder.with_filter(filter)

    rows = url_builder.execute(max_retries)
    print(f"Fetched {len(rows)} rows from the Lens Direct lng_plant table.", flush=True)
    return rows

def fetch_pipeline_table(columns: List[str] = None, filter: str = None, max_retries=1):
    lens_direct_url, api_key = get_lens_direct_url_and_token()
    url_builder = LensDirectQueryBuilder(
        f"{lens_direct_url}/query/upstream_weekly/imp-met/odata/pipeline",
        api_key,
    )
    if columns:
        url_builder.with_select(columns + ["id_valuation"])
    if filter:
        url_builder.with_filter(filter)

    rows = url_builder.execute(max_retries)
    print(f"Fetched {len(rows)} rows from the Lens Direct pipeline table.", flush=True)
    return rows

def fetch_field_company_table(columns: List[str] = None, filter: str = None, max_retries=1):
    lens_direct_url, api_key = get_lens_direct_url_and_token()
    url_builder = LensDirectQueryBuilder(
        f"{lens_direct_url}/query/upstream_weekly/imp-met/odata/field_company",
        api_key,
    )
    if columns:
        url_builder.with_select(columns + ["id_valuation", "company_name", "id_field_gem"])
    if filter:
        url_builder.with_filter(filter)
    rows = url_builder.execute(max_retries)
    print(f"Fetched {len(rows)} rows from the Lens Direct field_company table.", flush=True)
    return rows

def fetch_plant_company_table(columns: List[str] = None, filter: str = None, max_retries=1):
    lens_direct_url, api_key = get_lens_direct_url_and_token()
    url_builder = LensDirectQueryBuilder(
        f"{lens_direct_url}/query/upstream_weekly/imp-met/odata/lng_plant_company",
        api_key,
    )
    if columns:
        url_builder.with_select(columns + ["id_plant", "company_name"])
    if filter:
        url_builder.with_filter(filter)
    rows = url_builder.execute(max_retries)
    print(f"Fetched {len(rows)} rows from the Lens Direct lng_plant_company table.", flush=True)
    return rows

def fetch_pipeline_company_table(columns: List[str] = None, filter: str = None, max_retries=1):
    lens_direct_url, api_key = get_lens_direct_url_and_token()
    url_builder = LensDirectQueryBuilder(
        f"{lens_direct_url}/query/upstream_weekly/imp-met/odata/pipeline_company",
        api_key,
    )
    if columns:
        url_builder.with_select(columns + ["id_valuation", "company_name"])
    if filter:
        url_builder.with_filter(filter)
    rows = url_builder.execute(max_retries)
    print(f"Fetched {len(rows)} rows from the Lens Direct pipeline_company table.", flush=True)
    return rows

def fetch_field_company_history_table(columns: List[str] = None, filter: str = None, max_retries=1):
    lens_direct_url, api_key = get_lens_direct_url_and_token()

    url_builder = LensDirectQueryBuilder(
        f"{lens_direct_url}/query/upstream_weekly/imp-met/odata/field_company_history",
        api_key,
    )
    if columns:
        url_builder.with_select(columns + ["id_valuation", "company_name", "id_field_gem"])
    if filter:
        url_builder.with_filter(filter)
    rows = url_builder.execute(max_retries)
    print(f"Fetched {len(rows)} rows from the Lens Direct field_company_history table.", flush=True)
    return rows

def fetch_pipeline_company_history_table(columns: List[str] = None, filter: str = None, max_retries=1):
    lens_direct_url, api_key = get_lens_direct_url_and_token()
    url_builder = LensDirectQueryBuilder(
        f"{lens_direct_url}/query/upstream_weekly/imp-met/odata/pipeline_company_history",
        api_key,
    )
    if columns:
        url_builder.with_select(columns + ["id_valuation", "company_name"])
    if filter:
        url_builder.with_filter(filter)
    rows = url_builder.execute(max_retries)
    print(f"Fetched {len(rows)} rows from the Lens Direct pipeline_company_history table.", flush=True)
    return rows

def fetch_plant_company_history_table(columns: List[str] = None, filter: str = None, max_retries=1):
    lens_direct_url, api_key = get_lens_direct_url_and_token()
    url_builder = LensDirectQueryBuilder(
        f"{lens_direct_url}/query/upstream_weekly/imp-met/odata/lng_plant_company_history",
        api_key,
    )
    if columns:
        url_builder.with_select(columns + ["id_valuation", "company_name"])
    if filter:
        url_builder.with_filter(filter)
    rows = url_builder.execute(max_retries)
    print(f"Fetched {len(rows)} rows from the Lens Direct lng_plant_company_history table.", flush=True)
    return rows
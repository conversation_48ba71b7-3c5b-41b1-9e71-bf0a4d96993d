import React from "react";
import { Text, Table, TableHeader, TableRow, TableBody, TableCell } from "@fluentui/react-components";
import { useTableStyles } from "./use-styles";
import { TableData } from "./chat-types";

const maxColumnsInTable = 4;
const maxRowsInTable = 10;

const PreviewRow = ({ row, cellStyle, emptyCellStyle }: { row: Array<string | number>; cellStyle?: string, emptyCellStyle: string; }) => (
    <TableRow>
        {row.map((cell, colIndex) => (
            colIndex < maxColumnsInTable
                ? <TableCell className={cellStyle} key={colIndex}>{cell}</TableCell>
                : colIndex === maxColumnsInTable
                    ? <TableCell className={emptyCellStyle} key={colIndex}>...</TableCell>
                    : null

        ))}
    </TableRow>
);

const PreviewTable = ({ tableData }: { tableData: TableData }) => {
    const { tableContainer, table, headerCell, emptyColumn, emptyHeaderColumn, tableNotes } = useTableStyles();
    const [headerRow, ...otherRows] = tableData;

    return (
        <div className={tableContainer}>
            <Table size="extra-small" className={table}>
                <TableHeader>
                    <PreviewRow row={headerRow} cellStyle={headerCell} emptyCellStyle={emptyHeaderColumn} />
                </TableHeader>
                <TableBody>       
                    {otherRows.filter((_, index) => index < maxRowsInTable).map((row, rowIndex) => <PreviewRow key={rowIndex} row={row} emptyCellStyle={emptyColumn} />)}
                </TableBody>
            </Table>
            <div className={tableNotes}>
                {headerRow.length > maxColumnsInTable && <Text size={100}>Note: Showing only first {maxColumnsInTable} columns</Text>}
                {tableData.length > maxRowsInTable + 1 && <Text size={100}>Note: Showing only first {maxRowsInTable} rows</Text>}
            </div>
        </div>
    );
};

export { PreviewTable };

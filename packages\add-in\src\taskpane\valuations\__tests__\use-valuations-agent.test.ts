import { renderHook, act, waitFor } from "@testing-library/react";
import { useValuationsAgent } from "../use-valuations-agent";
import { askBot } from "../requests";
import { useFlags } from "launchdarkly-react-client-sdk";

jest.mock("../requests");
jest.mock("launchdarkly-react-client-sdk");
jest.mock("uuid", () => ({
  v4: jest.fn(() => "test-session-id"),
}));

const mockAskBot = askBot as jest.MockedFunction<typeof askBot>;
const mockUseFlags = useFlags as jest.MockedFunction<typeof useFlags>;

global.Excel = {
  run: jest.fn(),
  ChartType: {
    columnClustered: "columnClustered",
  },
  ChartSeriesBy: {
    auto: "auto",
  },
} as any;

const mockContext = {
  workbook: {
    worksheets: {
      add: jest.fn().mockReturnValue({
        activate: jest.fn(),
        getRange: jest.fn().mockReturnValue({
          getAbsoluteResizedRange: jest.fn().mockReturnValue({
            values: undefined,
          }),
          getRowsBelow: jest.fn().mockReturnValue({
            getAbsoluteResizedRange: jest.fn().mockReturnValue({
              getOffsetRange: jest.fn().mockReturnValue({}),
            }),
          }),
        }),
        getUsedRange: jest.fn().mockReturnValue({
          format: {
            autofitColumns: jest.fn(),
          },
        }),
        tables: {
          add: jest.fn().mockReturnValue({
            getRange: jest.fn().mockReturnValue({
              getRowsBelow: jest.fn().mockReturnValue({
                getAbsoluteResizedRange: jest.fn().mockReturnValue({
                  getOffsetRange: jest.fn().mockReturnValue({}),
                }),
              }),
            }),
          }),
        },
        charts: {
          add: jest.fn().mockReturnValue({
            setPosition: jest.fn(),
          }),
        },
      }),
    },
  },
  sync: jest.fn(),
};

describe("useValuationsAgent", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseFlags.mockReturnValue({ suggestQuestions: false });
    (global.Excel.run as jest.Mock).mockImplementation((callback) => callback(mockContext));
  });

  it("initializes with correct default state", () => {
    const { result } = renderHook(() => useValuationsAgent());

    expect(result.current.chatHistory).toEqual([]);
    expect(result.current.loading).toBe(false);
    expect(result.current.suggestedQuestions).toEqual([
      "What is the value of the Thunder Horse field?",
      "Benchmark the top five assets in North America by IRR, identifying those with the lowest capex/boe.",
      "Which field in Norway has the highest Remaining NPV?",
      "Compare BP and Shell under Base and Low pricing",
      "Which countries have the highest average post-tax IRR for their assets?",
    ]);
    expect(result.current.showSuggestedQuestions).toBe(false);
    expect(typeof result.current.handleChatMessageEntered).toBe("function");
    expect(typeof result.current.addTableToExcel).toBe("function");
  });

  it("shows suggested questions when flag is enabled and chat history is empty", () => {
    mockUseFlags.mockReturnValue({ suggestQuestions: true });

    const { result } = renderHook(() => useValuationsAgent());

    expect(result.current.showSuggestedQuestions).toBe(true);
  });

  it("hides suggested questions when flag is disabled", () => {
    mockUseFlags.mockReturnValue({ suggestQuestions: false });

    const { result } = renderHook(() => useValuationsAgent());

    expect(result.current.showSuggestedQuestions).toBe(false);
  });

  it("hides suggested questions when chat history is not empty", () => {
    mockUseFlags.mockReturnValue({ suggestQuestions: true });

    const { result } = renderHook(() => useValuationsAgent());

    act(() => {
      result.current.handleChatMessageEntered("test message");
    });

    expect(result.current.showSuggestedQuestions).toBe(false);
  });

  it("handles chat message with answer response", async () => {
    const mockResponse = {
      answer: "Test answer",
      table: null,
    };
    mockAskBot.mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useValuationsAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.chatHistory[0]).toEqual({
      role: "user",
      message: "test question",
    });
    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "Test answer",
    });
    expect(mockAskBot).toHaveBeenCalledWith("test question", "test-session-id", undefined);
  });

  it("handles chat message with table response", async () => {
    const mockResponse = {
      answer: null,
      table: "| Name | Value |\n|------|-------|\n| Test | 123 |",
    };
    mockAskBot.mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useValuationsAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "I have found a table of data for you",
      additionalData: [
        [" Name ", " Value "],
        [" Test ", " 123 "],
      ],
    });
  });

  it("handles chat message with both answer and table response", async () => {
    const mockResponse = {
      answer: "Here is your data",
      table: "| Name | Value |\n|------|-------|\n| Test | 123 |",
    };
    mockAskBot.mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useValuationsAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "Here is your data",
      additionalData: [
        [" Name ", " Value "],
        [" Test ", " 123 "],
      ],
    });
  });

  it("handles empty response gracefully", async () => {
    const mockResponse = {
      answer: null,
      table: null,
    };
    mockAskBot.mockResolvedValue(mockResponse);

    const { result } = renderHook(() => useValuationsAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "Sorry, I don't have an answer for you",
    });
  });

  it("handles API errors gracefully", async () => {
    mockAskBot.mockRejectedValue(new Error("API Error"));

    const { result } = renderHook(() => useValuationsAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("test question");
    });

    await waitFor(() => {
      expect(result.current.chatHistory).toHaveLength(2);
    });

    expect(result.current.chatHistory[1]).toEqual({
      role: "assistant",
      message: "Sorry, something has gone wrong. Please ask me something else",
    });
  });

  it("ignores empty or whitespace-only messages", async () => {
    const { result } = renderHook(() => useValuationsAgent());

    await act(async () => {
      result.current.handleChatMessageEntered("   ");
    });

    expect(result.current.chatHistory).toHaveLength(0);
    expect(mockAskBot).not.toHaveBeenCalled();
  });

  it("sets loading state during message processing", async () => {
    let resolvePromise: (value: any) => void;
    const promise = new Promise((resolve) => {
      resolvePromise = resolve;
    });

    mockAskBot.mockReturnValue(promise as any);

    const { result } = renderHook(() => useValuationsAgent());

    act(() => {
      result.current.handleChatMessageEntered("test message");
    });

    expect(result.current.loading).toBe(true);

    await act(async () => {
      resolvePromise({ answer: "Test", table: null });
      await promise;
    });

    expect(result.current.loading).toBe(false);
  });

  describe("addTableToExcel", () => {
    it("creates Excel table without chart", async () => {
      const testData = [
        ["Name", "Value"],
        ["Test", "123"],
      ];

      const { result } = renderHook(() => useValuationsAgent());

      await act(async () => {
        await result.current.addTableToExcel(testData);
      });

      expect(global.Excel.run).toHaveBeenCalled();
      expect(mockContext.workbook.worksheets.add).toHaveBeenCalled();
      expect(mockContext.workbook.worksheets.add().activate).toHaveBeenCalled();
      expect(mockContext.workbook.worksheets.add().tables.add).toHaveBeenCalled();
    });

    it("creates Excel table with chart when requested", async () => {
      const testData = [
        ["Name", "Value"],
        ["Test", "123"],
      ];

      const { result } = renderHook(() => useValuationsAgent());

      await act(async () => {
        await result.current.addTableToExcel(testData, true);
      });

      expect(global.Excel.run).toHaveBeenCalled();
      expect(mockContext.workbook.worksheets.add().charts.add).toHaveBeenCalled();
    });
  });
});

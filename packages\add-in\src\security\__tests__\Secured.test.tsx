import React from "react";
import { render, screen } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { Secured } from "../Secured";

jest.mock("../okta-client", () => ({
  oktaClient: {
    authStateManager: {
      getAuthState: jest.fn(),
      subscribe: jest.fn(),
      unsubscribe: jest.fn(),
    },
  },
}));

jest.mock("../../taskpane/login-dialog", () => ({
  displayLoginDialog: jest.fn(),
}));

const { oktaClient } = require("../okta-client");

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("Secured", () => {
  const mockChildren = <div>Protected Content</div>;

  beforeEach(() => {
    oktaClient.authStateManager.getAuthState.mockClear();
    oktaClient.authStateManager.subscribe.mockClear();
    oktaClient.authStateManager.unsubscribe.mockClear();
  });

  it("renders children when user is authenticated", () => {
    oktaClient.authStateManager.getAuthState.mockReturnValue({
      isAuthenticated: true,
    });

    renderWithProvider(<Secured>{mockChildren}</Secured>);

    expect(screen.getByText("Protected Content")).toBeInTheDocument();
    expect(screen.queryByRole("button", { name: "Login" })).not.toBeInTheDocument();
  });

  it("renders Login component when user is not authenticated", () => {
    oktaClient.authStateManager.getAuthState.mockReturnValue({
      isAuthenticated: false,
    });

    renderWithProvider(<Secured>{mockChildren}</Secured>);

    expect(screen.queryByText("Protected Content")).not.toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Login" })).toBeInTheDocument();
  });

  it("renders Login component when authState is null", () => {
    oktaClient.authStateManager.getAuthState.mockReturnValue(null);

    renderWithProvider(<Secured>{mockChildren}</Secured>);

    expect(screen.queryByText("Protected Content")).not.toBeInTheDocument();
    expect(screen.getByRole("button", { name: "Login" })).toBeInTheDocument();
  });

  it("subscribes to auth state changes on mount", () => {
    oktaClient.authStateManager.getAuthState.mockReturnValue({
      isAuthenticated: false,
    });

    renderWithProvider(<Secured>{mockChildren}</Secured>);

    expect(oktaClient.authStateManager.subscribe).toHaveBeenCalledTimes(1);
  });

  it("unsubscribes from auth state changes on unmount", () => {
    oktaClient.authStateManager.getAuthState.mockReturnValue({
      isAuthenticated: false,
    });

    const { unmount } = renderWithProvider(<Secured>{mockChildren}</Secured>);
    unmount();

    expect(oktaClient.authStateManager.unsubscribe).toHaveBeenCalledTimes(1);
  });
});

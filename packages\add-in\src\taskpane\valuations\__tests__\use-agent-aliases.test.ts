import { renderHook, act, waitFor } from "@testing-library/react";
import { useAvailableAliases  } from "../use-agent-aliases";
import { getAvailableAliases } from "../requests";
import { useFlags } from "launchdarkly-react-client-sdk";

jest.mock("../requests");
jest.mock("launchdarkly-react-client-sdk");

const mockAvailableAliases = [
  { agentAliasId: "alias1", agentAliasName: "Alias 1" },
  { agentAliasId: "alias2", agentAliasName: "Alias 2" },
];

const mockOnAliasChange = jest.fn();
const mockGetAvailableAliases = getAvailableAliases as jest.MockedFunction<typeof getAvailableAliases>;
const mockUseFlags = useFlags as jest.MockedFunction<typeof useFlags>;

describe("useAvailableAliases", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockUseFlags.mockReturnValue({ selectAgentAlias: false });
    mockGetAvailableAliases.mockResolvedValue(mockAvailableAliases);
  });

  it("loads available aliases when flag is enabled", async () => {
    mockUseFlags.mockReturnValue({  selectAgentAlias: true });

    const { result } = renderHook(() => useAvailableAliases(mockOnAliasChange));

    expect(mockGetAvailableAliases).toHaveBeenCalledTimes(1);
    await waitFor(() => expect(result.current.availableAliases).toEqual(mockAvailableAliases));
    expect(result.current.selectedAlias).toBeUndefined();
  });

  it("does not load available aliases when flag is disabled", () => {
    mockUseFlags.mockReturnValue({ selectAgentAlias: false });
    const { result } = renderHook(() => useAvailableAliases(mockOnAliasChange));
    expect(result.current.selectedAlias).toBeUndefined();
    expect(result.current.availableAliases).toEqual([])
  });

  it("sets selected alias when onSetSelectedAlias is called", async () => {
    mockUseFlags.mockReturnValue({ selectAgentAlias: true });
    const { result } = renderHook(() => useAvailableAliases(mockOnAliasChange));

    await waitFor(() => result.current.availableAliases.length > 0);

    act(() => {
      result.current.onSetSelectedAlias("alias1");
    });

    await waitFor(() => expect(result.current.selectedAlias).toEqual(mockAvailableAliases[0]));
    expect(mockOnAliasChange).toHaveBeenCalled();
  });
});

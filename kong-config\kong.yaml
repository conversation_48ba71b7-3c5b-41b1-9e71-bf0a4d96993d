_format_version: '3.0'
_info:
    defaults: {}
    select_tags:
        - woodmac
        - wmlgv-assistant
        - ${{ env "DECK_ENVIRONMENT" }}
_konnect:
    control_plane_name: ${{ env "DECK_KONNECT_CONTROL_PLANE_NAME" }}
services:
    - connect_timeout: 10000
      host: ${{ env "DECK_PRIVATE_LINK" }}
      name: wmlgv-assistant-orchestrator-${{ env "DECK_ENVIRONMENT" }}
      path: /
      plugins:
          - config:
                per_consumer: true
                status_code_metrics: true
                latency_metrics: true
            enabled: true
            name: prometheus
      port: 10443
      protocol: https
      read_timeout: 120000
      write_timeout: 120000
      retries: 3
      routes:
          - hosts:
                - ${{ env "DECK_KONG_DATA_PLANE_HOST" }}
            https_redirect_status_code: 426
            methods:
                - GET
                - POST
                - OPTIONS
            name: wmlgv-assistant-${{ env "DECK_ENVIRONMENT" }}
            path_handling: v0
            paths:
                - ~/valuations-assistant
            preserve_host: false
            protocols:
                - https
            regex_priority: 0
            strip_path: true

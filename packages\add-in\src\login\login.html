<!-- Copyright (c) Microsoft Corporation. All rights reserved. Licensed under the MIT license. -->
<!-- See LICEN<PERSON> in the project root for license information -->

<!doctype html>
<html lang="en" data-framework="typescript">

<head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=Edge" />
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Woodmac Assistant Add-in</title>

    <script type="text/javascript">
        // Office js deletes window.history.pushState and window.history.replaceState. Cache them
        window._historyCache = {
            replaceState: window.history.replaceState,
            pushState: window.history.pushState
        };
    </script>

    <script type="text/javascript" src="https://appsforoffice.microsoft.com/lib/1.1/hosted/office.js"></script>

    <script type="text/javascript">
        // And restore them
        window.history.replaceState = window._historyCache.replaceState;
        window.history.pushState = window._historyCache.pushState;
    </script>
    <script type="text/javascript" src="./config.js"></script>
</head>

<body style="width: 100%; height: 100%; margin: 0; padding: 0;">
    <div id="container"></div>
</body>

</html>

/* eslint-disable @typescript-eslint/no-require-imports */
import OktaJwtVerifier from '@okta/jwt-verifier';
import { NextFunction } from 'express';
import request from 'supertest';

jest.mock('@okta/jwt-verifier');
jest.mock('request-context');
jest.mock('./auth/entitlements-service', () => ({
    entitlementsService: {
        checkUserEntitlement: jest.fn().mockResolvedValue(true),
        clearCaches: jest.fn(),
    },
}));
jest.mock('./auth/token-generator', () => ({
    getToken: jest.fn().mockResolvedValue('mock-token'),
}));
jest.mock('@aws-sdk/client-bedrock-agent-runtime', () => ({
    BedrockAgentRuntimeClient: jest.fn().mockImplementation(() => ({
        send: jest.fn().mockResolvedValue({
            completion: {
                async *[Symbol.asyncIterator]() {
                    yield {
                        chunk: {
                            bytes: new TextEncoder().encode('Test response'),
                        },
                    };
                },
            },
        }),
    })),
    InvokeAgentCommand: jest.fn(),
}));
jest.mock('axios', () => ({
    __esModule: true,
    default: {
        get: jest.fn().mockResolvedValue({ data: { questions: [] } }),
        post: jest.fn().mockResolvedValue({ data: { result: 'success' } }),
    },
    get: jest.fn().mockResolvedValue({ data: {} }),
    post: jest.fn().mockResolvedValue({ data: {} }),
}));
jest.mock('./logger', () => ({
    __esModule: true,
    default: {
        info: jest.fn(),
        warn: jest.fn(),
        error: jest.fn(),
        debug: jest.fn(),
    },
    requestLogger: (_req: unknown, _res: unknown, next: NextFunction) => next(),
}));

const protectedRoutes = [
    ['/ask-chatbot', 'get'],
    ['/ask-lens-director', 'post'],
    ['/lens-direct-data', 'post'],
    ['/ask-digital-content', 'post'],
] as const;

const expectedIssuer = 'https://test-issuer.com/path';
const expectedAudience = 'api://test-api';

const mockBedrockAgentRuntimeSend = jest.fn();
const mockInvokeAgentCommand = jest.fn();

jest.mock('@aws-sdk/client-bedrock-agent-runtime', () => {
    return {
        BedrockAgentRuntimeClient: jest.fn().mockImplementation(() => ({
            send: mockBedrockAgentRuntimeSend,
        })),
        InvokeAgentCommand: mockInvokeAgentCommand,
    };
});

const mockGetAvailableAliases = jest.fn();

jest.mock('./bedrock/agent-client', () => ({
    getAgentAliases: mockGetAvailableAliases,
}));

const mockValidateAlias = jest.fn();
jest.mock('./validation/alias-validator', () => ({
    validateAlias: mockValidateAlias,
}));

describe('when the server app is running', () => {
    const oldEnv = process.env;
    const agentId = 'test-agent-id';
    const defaultAgentAlias = 'default-alias';

    beforeEach(() => {
        jest.clearAllMocks();
        mockBedrockAgentRuntimeSend.mockImplementation(() => ({
            completion: (async function* () {
                yield { chunk: { bytes: Buffer.from('|test|table|=====this is the description') } };
            })()
        }));
        
        mockGetAvailableAliases.mockResolvedValue([
                { agentAliasName: 'version1', agentAliasId: 'alias-1' },
                { agentAliasName: 'orchestrator', agentAliasId: 'alias-orch' }
        ]);
            
        mockValidateAlias.mockResolvedValue(true);
        process.env = {
            AGENT_ALIAS: defaultAgentAlias,
            TOKEN_ISSUER: expectedIssuer,
            TOKEN_AUDIENCE: expectedAudience,
            AGENT_ID: 'test-agent-id',
            ENVIRONMENT: 'test',
            LENS_DIGITAL_CONTENT_CHAT_BOT_URL: 'https://test-chatbot.com',
            USE_ENTITLEMENTS: 'true',
        };
    });

    afterAll(() => {
        process.env = oldEnv;
    });

    describe('Route Protection', () => {
        it.each(protectedRoutes)(
            'should protect %s route and return 401 when unauthorized',
            async (url, method) => {
                const { app } = require('./app');

                await request(app)
                    [method](url)
                    .set('Accept', 'application/json')
                    .expect('Content-Type', /json/)
                    .expect(401);
            },
        );
    });

    describe('Health Check', () => {
        it('should allow health check without authentication', async () => {
            const { app } = require('./app');

            await request(app)
                .get('/health')
                .set('Accept', 'application/json')
                .expect('Content-Type', /json/)
                .expect(200)
                .expect({ status: 'ok' });
        });

        it('should allow health check even with invalid auth header', async () => {
            const { app } = require('./app');

            await request(app)
                .get('/health')
                .set('Accept', 'application/json')
                .set('authorization', 'Invalid auth header')
                .expect('Content-Type', /json/)
                .expect(200)
                .expect({ status: 'ok' });
        });
    });

    describe('Auth Middleware Integration', () => {
        it('should successfully authenticate valid requests', async () => {
            const mockVerifyAccessToken = jest.fn().mockResolvedValue({
                claims: {
                    sub: '<EMAIL>',
                    uid: 'user123',
                    cid: 'company123',
                },
            });

            (OktaJwtVerifier as jest.Mock).mockImplementation(() => ({
                verifyAccessToken: mockVerifyAccessToken,
            }));

            const { app } = require('./app');

            await request(app)
                .get('/ask-chatbot?prompt=test&sessionId=test')
                .set('Accept', 'application/json')
                .set('authorization', 'Bearer valid-token');

            expect(mockVerifyAccessToken).toHaveBeenCalledWith(
                'valid-token',
                expectedAudience,
            );
        });

        it('should set user context when user is authenticated', async () => {
            const mockVerifyAccessToken = jest.fn().mockResolvedValue({
                claims: {
                    sub: '<EMAIL>',
                    uid: 'user123',
                    cid: 'company123',
                },
            });

            (OktaJwtVerifier as jest.Mock).mockImplementation(() => ({
                verifyAccessToken: mockVerifyAccessToken,
            }));

            const requestContextMock = require('request-context');

            const { app } = require('./app');

            await request(app)
                .get('/ask-chatbot?prompt=test&sessionId=test')
                .set('Accept', 'application/json')
                .set('authorization', 'Bearer valid-token');

            expect(requestContextMock.set).toHaveBeenCalledWith(
                'woodmac:user',
                {
                    email: '<EMAIL>',
                    id: 'user123',
                    accountId: 'company123',
                    internalUserEmail: '<EMAIL>',
                    bearerToken: 'valid-token',
                },
            );
        });
    });

    describe('GET /ask-chatbot', () => {

        const sut = async (agentAliasIdParam?: string) => {
            const { app } = require('./app');
            return request(app)
                .get('/ask-chatbot')
                .query({ prompt: 'test', sessionId: 'abc', versionAlias: agentAliasIdParam })
                .set('authorization', 'Bearer token');
        };

        it('should pass the correct data to the bedrock agent runtime=', async () => {
            const expectedPrompt = 'test';
            const expectedSessionId = 'abc';

            await sut();

            expect(mockInvokeAgentCommand).toHaveBeenCalledWith({
                agentId,
                agentAliasId: defaultAgentAlias,
                sessionId: expectedSessionId,
                enableTrace: true,
                inputText: expectedPrompt,
            });
        });

        it('should handle a table and answer separated by ======', async () => {
            mockBedrockAgentRuntimeSend.mockImplementation(() => ({
                completion: (async function* () {
                    yield { chunk: { bytes: Buffer.from('|test|table|=====this is the description') } };
                })()
            }));

            const { status, body } = await sut();

            expect(status).toBe(200);
            expect(body.table).toBe('|test|table|');
            expect(body.answer).toBe('{{TABLE}}this is the description');
        });

        it('should handle a table and answer not separated by ======', async () => {
             mockBedrockAgentRuntimeSend.mockImplementation(() => ({
                completion: (async function* () {
                    yield { chunk: { bytes: Buffer.from('|test|table| this is the description') } };
                })()
            }));

            const { status, body } = await sut();

            expect(status).toBe(200);
            expect(body.table).toBe('|test|table|');
            expect(body.answer).toBe('{{TABLE}}this is the description');
        });

        it('should handle a table with no answer', async () => {
             mockBedrockAgentRuntimeSend.mockImplementation(() => ({
                completion: (async function* () {
                    yield { chunk: { bytes: Buffer.from('|test|table|') } };
                })()
            }));

            const { status, body } = await sut();

            expect(status).toBe(200);
            expect(body.table).toBe('|test|table|');
            expect(body.answer).toBe('{{TABLE}}');
        });

        it('should handle an answer with no table', async () => {
             mockBedrockAgentRuntimeSend.mockImplementation(() => ({
                completion: (async function* () {
                    yield { chunk: { bytes: Buffer.from('this is the description') } };
                })()
            }));

            const { status, body } = await sut();

            expect(status).toBe(200);
            expect(body.table).toBeUndefined();
            expect(body.answer).toBe('this is the description');
        });

        it('should use the alias if passed in', async () => {
            const expectedPrompt = 'test';
            const expectedSessionId = 'abc';
            const expectedVersionAlias = 'orchestrator';

            await sut(expectedVersionAlias);

            expect(mockInvokeAgentCommand).toHaveBeenCalledWith({
                agentId,
                agentAliasId: expectedVersionAlias,
                sessionId: expectedSessionId,
                enableTrace: true,
                inputText: expectedPrompt,
            });
        });

        it('should handle agent errors', async () => {
            mockBedrockAgentRuntimeSend.mockRejectedValue(new Error('Agent error'));

            const { status, body } = await sut();

            expect(status).toBe(500);
            expect(body.error).toBe('Something has broken!');
        });
    });

    describe('GET /valuations-agent/aliases', () => {
        const sut = async () => {
            const { app } = require('./app');
            return request(app)
                .get('/valuations-agent/aliases')
                .set('authorization', 'Bearer token');
        }

        it('should return agent aliases', async () => {
            const { status, body } = await sut();

            expect(status).toBe(200);
            expect(body.versionAliases?.length).toBe(2);
            expect(body.versionAliases).toEqual([
                { agentAliasName: 'version1', agentAliasId: 'alias-1' },
                { agentAliasName: 'orchestrator', agentAliasId: 'alias-orch' }
            ]);
        });

        it('should handle agent errors', async () => {
            mockGetAvailableAliases.mockRejectedValue(new Error('Agent Aliases error'));

            const { status, body } = await sut();

            expect(status).toBe(500);
            expect(body.error).toBe('Something has broken!');
        });
    }); 

    describe('Entitlements Middleware Integration', () => {
        beforeEach(() => {
            const entitlementsServiceMock = require('./auth/entitlements-service');
            entitlementsServiceMock.entitlementsService.checkUserEntitlement.mockClear();
        });

        it('should allow access when user has entitlements', async () => {
            const mockVerifyAccessToken = jest.fn().mockResolvedValue({
                claims: {
                    sub: '<EMAIL>',
                    uid: 'user123',
                    cid: 'company123',
                },
            });

            (OktaJwtVerifier as jest.Mock).mockImplementation(() => ({
                verifyAccessToken: mockVerifyAccessToken,
            }));

            const entitlementsServiceMock = require('./auth/entitlements-service');
            entitlementsServiceMock.entitlementsService.checkUserEntitlement.mockResolvedValue(
                true,
            );

            const { app } = require('./app');

            await request(app)
                .get('/ask-chatbot?prompt=test&sessionId=test')
                .set('Accept', 'application/json')
                .set('authorization', 'Bearer valid-token')
                .expect(200);

            expect(
                entitlementsServiceMock.entitlementsService
                    .checkUserEntitlement,
            ).toHaveBeenCalledWith(
                'user123',
                'vals-chat-assistant',
                'VALSCHATASSIST',
            );
        });

        it('should deny access when user lacks entitlements', async () => {
            const mockVerifyAccessToken = jest.fn().mockResolvedValue({
                claims: {
                    sub: '<EMAIL>',
                    uid: 'user123',
                    cid: 'company123',
                },
            });

            (OktaJwtVerifier as jest.Mock).mockImplementation(() => ({
                verifyAccessToken: mockVerifyAccessToken,
            }));

            const entitlementsServiceMock = require('./auth/entitlements-service');
            entitlementsServiceMock.entitlementsService.checkUserEntitlement.mockResolvedValue(
                false,
            );

            const { app } = require('./app');

            await request(app)
                .get('/ask-chatbot?prompt=test&sessionId=test')
                .set('Accept', 'application/json')
                .set('authorization', 'Bearer valid-token')
                .expect(403)
                .expect({
                    message: 'Access denied',
                    error: 'Forbidden',
                });

            expect(
                entitlementsServiceMock.entitlementsService
                    .checkUserEntitlement,
            ).toHaveBeenCalledWith(
                'user123',
                'vals-chat-assistant',
                'VALSCHATASSIST',
            );
        });

        it('should deny access when entitlements service throws an error', async () => {
            const mockVerifyAccessToken = jest.fn().mockResolvedValue({
                claims: {
                    sub: '<EMAIL>',
                    uid: 'user123',
                    cid: 'company123',
                },
            });

            (OktaJwtVerifier as jest.Mock).mockImplementation(() => ({
                verifyAccessToken: mockVerifyAccessToken,
            }));

            const entitlementsServiceMock = require('./auth/entitlements-service');
            entitlementsServiceMock.entitlementsService.checkUserEntitlement.mockRejectedValue(
                new Error('Service unavailable'),
            );

            const { app } = require('./app');

            await request(app)
                .get('/ask-chatbot?prompt=test&sessionId=test')
                .set('Accept', 'application/json')
                .set('authorization', 'Bearer valid-token')
                .expect(403)
                .expect({
                    message: 'Access denied',
                    error: 'Forbidden',
                });
        });

        it('should apply entitlements middleware to all protected routes', async () => {
            const mockVerifyAccessToken = jest.fn().mockResolvedValue({
                claims: {
                    sub: '<EMAIL>',
                    uid: 'user123',
                    cid: 'company123',
                },
            });

            (OktaJwtVerifier as jest.Mock).mockImplementation(() => ({
                verifyAccessToken: mockVerifyAccessToken,
            }));

            const entitlementsServiceMock = require('./auth/entitlements-service');
            entitlementsServiceMock.entitlementsService.checkUserEntitlement.mockResolvedValue(
                false,
            );

            const { app } = require('./app');

            await request(app)
                .post('/ask-lens-director')
                .set('Accept', 'application/json')
                .set('authorization', 'Bearer valid-token')
                .send({
                    prompt: 'test',
                    oDataUrl: 'https://data.woodmac.com/odata/test',
                })
                .expect(403);

            expect(
                entitlementsServiceMock.entitlementsService
                    .checkUserEntitlement,
            ).toHaveBeenCalledWith(
                'user123',
                'vals-chat-assistant',
                'VALSCHATASSIST',
            );
        });

        it('should not apply entitlements middleware to health check endpoint', async () => {
            const entitlementsServiceMock = require('./auth/entitlements-service');
            entitlementsServiceMock.entitlementsService.checkUserEntitlement.mockClear();

            const { app } = require('./app');

            await request(app)
                .get('/health')
                .set('Accept', 'application/json')
                .expect(200)
                .expect({ status: 'ok' });

            expect(
                entitlementsServiceMock.entitlementsService
                    .checkUserEntitlement,
            ).not.toHaveBeenCalled();
        });
    });
});



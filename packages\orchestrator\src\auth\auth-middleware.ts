import OktaJwtVerifier from '@okta/jwt-verifier';
import { NextFunction, Request, Response } from 'express';
import logger from '../logger';

export interface AuthenticatedRequest extends Request {
    user?: {
        email?: string;
        id?: string;
        accountId?: string;
        internalUserEmail?: string;
        bearerToken?: string;
    };
}

const oktaJwtVerifier = (issuer: string): OktaJwtVerifier =>
    new OktaJwtVerifier({
        issuer,
    });

const createTokenVerifier =
    (issuer: string, audience: string) => async (accessToken: string) =>
        oktaJwtVerifier(issuer).verifyAccessToken(accessToken, audience);

const sendUnAuthorizedResponse = (res: Response) =>
    res.status(401).json({ message: 'Unauthorized' });
const authorizationHeaderPresent = (authorization?: string): boolean =>
    !!authorization && authorization.startsWith('Bearer ');

const createAuthMiddleware =
    (issuer?: string, audience?: string) =>
    async (req: AuthenticatedRequest, res: Response, next: NextFunction) => {
        if (!issuer || !audience) {
            logger.error(
                { issuer, audience },
                'Missing issuer or audience configuration',
            );
            sendUnAuthorizedResponse(res);
            return;
        }

        const { authorization } = req.headers;

        if (!authorizationHeaderPresent(authorization)) {
            logger.warn(
                { path: req.path, ip: req.ip },
                'Missing authorization header',
            );
            sendUnAuthorizedResponse(res);
            return;
        }

        const [, bearerToken] = (authorization as string).split(' ');

        const verifyBearerToken = createTokenVerifier(issuer, audience);

        try {
            const { claims } = await verifyBearerToken(bearerToken);
            const email = claims.sub;
            const isInternalUser =
                email?.endsWith('@woodmac.com') ||
                email?.endsWith('@woodmacad.com');
            const user = {
                email,
                id: claims.uid,
                accountId: claims.cid,
                bearerToken,
                // to make it easier to find the internal user in the logs
                ...(isInternalUser && { internalUserEmail: email }),
            };
            Object.assign(req, { user });
            next();
        } catch (error) {
            logger.warn(
                { path: req.path, ip: req.ip, error: error as Error },
                'Token verification failed',
            );
            sendUnAuthorizedResponse(res);
        }
    };

export { createAuthMiddleware };

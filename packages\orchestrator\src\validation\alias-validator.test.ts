import { validate<PERSON>lia<PERSON> } from './alias-validator';
import { getAgentAliases } from '../bedrock/agent-client';

jest.mock('../bedrock/agent-client');

const mockGetAgentAliases = getAgentAliases as jest.MockedFunction<typeof getAgentAliases>;

describe('validateAlias', () => {
    beforeEach(() => {
        jest.clearAllMocks();
    });

    it('should return true when aliasId is undefined', async () => {
        const result = await validateAlias(undefined);
        expect(result).toBe(true);
        expect(mockGetAgentAliases).not.toHaveBeenCalled();
    });

    it('should return true when aliasId exists in agent aliases', async () => {
        mockGetAgentAliases.mockResolvedValue([
            { agentAliasId: 'alias-1', agentAliasName: 'version1' },
            { agentAliasId: 'alias-2', agentAliasName: 'orchestrator' }
        ]);

        const result = await validate<PERSON>lia<PERSON>('alias-1');
        expect(result).toBe(true);
        expect(mockGetAgentAliases).toHaveBeenCalledTimes(1);
    });

    it('should return false when aliasId does not exist in agent aliases', async () => {
        mockGetAgentAliases.mockResolvedValue([
            { agentAliasId: 'alias-1', agentAliasName: 'version1' },
            { agentAliasId: 'alias-2', agentAliasName: 'orchestrator' }
        ]);

        const result = await validateAlias('alias-3');
        expect(result).toBe(false);
        expect(mockGetAgentAliases).toHaveBeenCalledTimes(1);
    });

    it('should return false when getAgentAliases returns empty array', async () => {
        mockGetAgentAliases.mockResolvedValue([]);

        const result = await validateAlias('alias-1');
        expect(result).toBe(false);
        expect(mockGetAgentAliases).toHaveBeenCalledTimes(1);
    });

    it('should handle errors from getAgentAliases', async () => {
        mockGetAgentAliases.mockRejectedValue(new Error('AWS error'));

        await expect(validateAlias('alias-1')).rejects.toThrow('AWS error');
        expect(mockGetAgentAliases).toHaveBeenCalledTimes(1);
    });
});
const mockError = jest.fn();
jest.mock('../logger', () => ({
    debug: jest.fn(),
    info: jest.fn(),
    error: mockError,
}));

const mockBedrockClient = jest.fn();
const mockSend = jest.fn();
const mockCommand = jest.fn();

describe('getAgentAliases', () => {
    const OLD_ENV = process.env;
    beforeEach(() => {
        jest.resetAllMocks();
        jest.resetModules();
        process.env = { ...OLD_ENV, AGENT_ID: 'test-agent-id' };
    });
    
    afterAll(() => {
        process.env = OLD_ENV;
    });

    const setup = async () => {
        jest.doMock('@aws-sdk/client-bedrock-agent', () => ({
            BedrockAgentClient: mockBedrockClient,
            ListAgentAliasesCommand: mockCommand,
        }));
        mockBedrockClient.mockImplementation(() => ({
            send: mockSend,
        }));
        const { getAgentAliases } = await import('./agent-client');
        return getAgentAliases;
    };

    it('fetches and filters agent aliases, caches them, and returns filtered aliases', async () => {
        const agentAliasSummaries = [
            { agentAliasName: 'version1', agentAliasId: 'id1' },
            { agentAliasName: 'orchestrator', agentAliasId: 'id2' },
            { agentAliasName: 'other', agentAliasId: 'id3' },
        ];
        
        mockBedrockClient.mockImplementation(() => ({
            send: mockSend,
        }));

        mockSend.mockResolvedValueOnce({ agentAliasSummaries });
        const getAgentAliases = await setup();

        const result = await getAgentAliases();
        expect(mockSend).toHaveBeenCalledTimes(1);
        expect(mockCommand).toHaveBeenCalledTimes(1);
        expect(result).toEqual([
            { agentAliasName: 'version1', agentAliasId: 'id1' },
            { agentAliasName: 'orchestrator', agentAliasId: 'id2' },
        ]);
    });

    it('returns cached aliases if not expired', async () => {       
        mockBedrockClient.mockImplementation(() => ({
            send: mockSend,
        }));
        
        const agentAliasSummaries = [
            { agentAliasName: 'version2', agentAliasId: 'id4' },
        ];

        mockSend.mockResolvedValueOnce({ agentAliasSummaries });
        
        const getAgentAliases = await setup();

        const firstResult = await getAgentAliases();
        expect(firstResult).toEqual([
            { agentAliasName: 'version2', agentAliasId: 'id4' },
        ]);

        mockSend.mockClear();
        const secondResult = await getAgentAliases();
        expect(secondResult).toEqual(firstResult);
        expect(mockSend).not.toHaveBeenCalled();
    });

    it('throws and logs error if fetching fails', async () => {
        mockBedrockClient.mockImplementation(() => ({
            send: mockSend,
        }));
        
        const error = new Error('Failed to fetch');
        mockSend.mockRejectedValueOnce(error);
        
        const getAgentAliases = await setup();
        await expect(getAgentAliases()).rejects.toThrow('Failed to fetch');
        expect(mockError).toHaveBeenCalledWith(error, 'Error fetching valuations agent aliases');
    });

    it('filters only versionN and orchestrator aliases', async () => {
        const agentAliasSummaries = [
            { agentAliasName: 'version123', agentAliasId: 'id1' },
            { agentAliasName: 'orchestrator', agentAliasId: 'id2' },
            { agentAliasName: 'foo', agentAliasId: 'id3' },
            { agentAliasName: 'version', agentAliasId: 'id4' },
            { agentAliasName: 'versionA', agentAliasId: 'id5' },
            { agentAliasName: undefined, agentAliasId: 'id6' },
        ];
        mockSend.mockResolvedValueOnce({ agentAliasSummaries });
        const getAgentAliases = await setup();

        const result = await getAgentAliases();

        expect(result).toEqual([
            { agentAliasName: 'version123', agentAliasId: 'id1' },
            { agentAliasName: 'orchestrator', agentAliasId: 'id2' },
        ]);
    });

    it('returns empty array if no matching aliases', async () => {
        const agentAliasSummaries = [
            { agentAliasName: 'foo', agentAliasId: 'id1' },
            { agentAliasName: 'bar', agentAliasId: 'id2' },
        ];
        mockSend.mockResolvedValueOnce({ agentAliasSummaries });
        const getAgentAliases = await setup();

        const result = await getAgentAliases();

        expect(result).toEqual([]);
    });
});
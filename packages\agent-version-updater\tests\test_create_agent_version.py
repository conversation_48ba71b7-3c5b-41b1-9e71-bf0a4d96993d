from unittest.mock import Mock, patch
import sys
import os
from datetime import datetime

sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from create_agent_version import list_agent_versions, create_alias_from_draft, create_new_alias_if_draft_updated


def test_list_agent_versions_single_page():
    mock_client = Mock()
    mock_paginator = Mock()
    mock_client.get_paginator.return_value = mock_paginator
    mock_paginator.paginate.return_value = [
        {"agentVersionSummaries": [{"agentVersion": "1", "agentStatus": "PREPARED"}]}
    ]

    result = list_agent_versions(mock_client, "test-agent-id")

    assert len(result) == 1
    assert result[0]["agentVersion"] == "1"
    mock_client.get_paginator.assert_called_once_with("list_agent_versions")


def test_list_agent_versions_multiple_pages():
    mock_client = <PERSON><PERSON>()
    mock_paginator = Mock()
    mock_client.get_paginator.return_value = mock_paginator
    mock_paginator.paginate.return_value = [
        {"agentVersionSummaries": [{"agentVersion": "1", "agentStatus": "PREPARED"}]},
        {"agentVersionSummaries": [{"agentVersion": "2", "agentStatus": "PREPARED"}]},
        {"agentVersionSummaries": []}
    ]

    result = list_agent_versions(mock_client, "test-agent-id")

    assert len(result) == 2
    assert result[0]["agentVersion"] == "1"
    assert result[1]["agentVersion"] == "2"


def test_list_agent_versions_empty_response():
    mock_client = Mock()
    mock_paginator = Mock()
    mock_client.get_paginator.return_value = mock_paginator
    mock_paginator.paginate.return_value = [{"agentVersionSummaries": []}]

    result = list_agent_versions(mock_client, "test-agent-id")

    assert len(result) == 0


@patch("builtins.print")
def test_create_alias_from_draft(mock_print):
    mock_client = Mock()

    create_alias_from_draft(mock_client, "test-agent-id", 2)

    mock_client.create_agent_alias.assert_called_once_with(
        agentId="test-agent-id",
        agentAliasName="version2",
        description="Alias for version 2",
    )
    assert mock_print.call_count == 2
    mock_print.assert_any_call("Creating alias 'version2' for agent test-agent-id...")
    mock_print.assert_any_call("Created alias 'version2' pointing to DRAFT.")


@patch("builtins.print")
@patch("boto3.client")
def test_create_new_alias_if_draft_updated_draft_newer(mock_boto_client, mock_print):
    mock_client = Mock()
    mock_boto_client.return_value = mock_client
    
    # Mock versions with draft newer than latest numeric
    versions = [
        {"agentVersion": "DRAFT", "agentStatus": "NOT_PREPARED", "updatedAt": datetime(2024, 1, 2)},
        {"agentVersion": "1", "agentStatus": "PREPARED", "updatedAt": datetime(2024, 1, 1)},
    ]
    
    with patch("create_agent_version.list_agent_versions", return_value=versions):
        with patch("create_agent_version.create_alias_from_draft") as mock_create_alias:
            create_new_alias_if_draft_updated("test-agent-id", "us-east-1")
            
            mock_create_alias.assert_called_once_with(mock_client, "test-agent-id", 2)
            mock_print.assert_any_call("Draft is newer than the latest published version.")


@patch("builtins.print")
@patch("boto3.client")
def test_create_new_alias_if_draft_updated_draft_older(mock_boto_client, mock_print):
    mock_client = Mock()
    mock_boto_client.return_value = mock_client
    
    # Mock versions with draft older than latest numeric
    versions = [
        {"agentVersion": "DRAFT", "agentStatus": "NOT_PREPARED", "updatedAt": datetime(2024, 1, 1)},
        {"agentVersion": "1", "agentStatus": "PREPARED", "updatedAt": datetime(2024, 1, 2)},
    ]
    
    with patch("create_agent_version.list_agent_versions", return_value=versions):
        with patch("create_agent_version.create_alias_from_draft") as mock_create_alias:
            create_new_alias_if_draft_updated("test-agent-id", "us-east-1")
            
            mock_create_alias.assert_not_called()
            mock_print.assert_any_call("Draft is not newer than the latest published version. No alias update performed.")


@patch("builtins.print")
@patch("boto3.client")
def test_create_new_alias_if_draft_updated_draft_only(mock_boto_client, mock_print):
    mock_client = Mock()
    mock_boto_client.return_value = mock_client
    
    # Mock versions with only draft, no numeric versions
    versions = [
        {"agentVersion": "DRAFT", "agentStatus": "NOT_PREPARED", "updatedAt": datetime(2024, 1, 1)},
    ]
    
    with patch("create_agent_version.list_agent_versions", return_value=versions):
        with patch("create_agent_version.create_alias_from_draft") as mock_create_alias:
            create_new_alias_if_draft_updated("test-agent-id", "us-east-1")
            
            mock_create_alias.assert_called_once_with(mock_client, "test-agent-id", 1)
            mock_print.assert_any_call("Draft exists and no numeric published versions found.")


@patch("builtins.print")
@patch("boto3.client")
def test_create_new_alias_if_draft_updated_no_draft(mock_boto_client, mock_print):
    mock_client = Mock()
    mock_boto_client.return_value = mock_client
    
    # Mock versions with no draft
    versions = [
        {"agentVersion": "1", "agentStatus": "PREPARED", "updatedAt": datetime(2024, 1, 1)},
        {"agentVersion": "2", "agentStatus": "PREPARED", "updatedAt": datetime(2024, 1, 2)},
    ]
    
    with patch("create_agent_version.list_agent_versions", return_value=versions):
        with patch("create_agent_version.create_alias_from_draft") as mock_create_alias:
            create_new_alias_if_draft_updated("test-agent-id", "us-east-1")
            
            mock_create_alias.assert_not_called()
            mock_print.assert_any_call("No draft version found.")


@patch("builtins.print")
@patch("boto3.client")
def test_create_new_alias_if_draft_updated_multiple_numeric_versions(mock_boto_client, mock_print):
    mock_client = Mock()
    mock_boto_client.return_value = mock_client
    
    # Mock versions with multiple numeric versions - should pick highest
    versions = [
        {"agentVersion": "DRAFT", "agentStatus": "NOT_PREPARED", "updatedAt": datetime(2024, 1, 5)},
        {"agentVersion": "1", "agentStatus": "PREPARED", "updatedAt": datetime(2024, 1, 1)},
        {"agentVersion": "3", "agentStatus": "PREPARED", "updatedAt": datetime(2024, 1, 3)},
        {"agentVersion": "2", "agentStatus": "PREPARED", "updatedAt": datetime(2024, 1, 2)},
    ]
    
    with patch("create_agent_version.list_agent_versions", return_value=versions):
        with patch("create_agent_version.create_alias_from_draft") as mock_create_alias:
            create_new_alias_if_draft_updated("test-agent-id", "us-east-1")
            
            # Should create version 4 (3 + 1)
            mock_create_alias.assert_called_once_with(mock_client, "test-agent-id", 4)


@patch("builtins.print")
@patch("boto3.client")
def test_create_new_alias_if_draft_updated_custom_region(mock_boto_client, mock_print):
    mock_client = Mock()
    mock_boto_client.return_value = mock_client
    
    versions = [
        {"agentVersion": "DRAFT", "agentStatus": "NOT_PREPARED", "updatedAt": datetime(2024, 1, 2)},
        {"agentVersion": "1", "agentStatus": "PREPARED", "updatedAt": datetime(2024, 1, 1)},
    ]
    
    with patch("create_agent_version.list_agent_versions", return_value=versions):
        create_new_alias_if_draft_updated("test-agent-id", "eu-west-1")
        
        mock_boto_client.assert_called_once_with("bedrock-agent", region_name="eu-west-1")

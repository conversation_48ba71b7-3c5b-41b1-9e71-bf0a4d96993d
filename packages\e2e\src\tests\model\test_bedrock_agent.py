import boto3
import pytest
import uuid
import pandas as pd
import io
from functools import lru_cache
import asyncio
from .helpers.llm_comparison_bedrock import find_llm_similarity

AGENT_ID = "ITOR9EQ6UY"
ALIAS_ID = "TSTALIASID"

s3_client = boto3.client(
    "s3"
    # ,verify=False,  # This disables SSL certificate verification comment out when not local
)


@lru_cache(maxsize=2)
def get_asset_data():
    try:
        response = s3_client.get_object(
            Bucket="wmlgv-aidev-assistant-valuations-data",
            Key="parquet/all_upstream_assets_combined.parquet",
        )

        return pd.read_parquet(io.BytesIO(response["Body"].read()))
    except Exception as e:
        pytest.fail(f"Failed to retrieve asset data from S3: {str(e)}")


@lru_cache(maxsize=2)
def get_company_data():
    try:
        response = s3_client.get_object(
            Bucket="wmlgv-aidev-assistant-valuations-data",
            Key="parquet/top_50_companies_combined.parquet",
        )

        return pd.read_parquet(io.BytesIO(response["Body"].read()))
    except Exception as e:
        pytest.fail(f"Failed to retrieve company data from S3: {str(e)}")


async def invoke_bedrock_agent(prompt):
    bedrock_agent_runtime = boto3.client("bedrock-agent-runtime")

    session_id = str(uuid.uuid4())

    request_params = {
        "agentId": AGENT_ID,
        "agentAliasId": ALIAS_ID,
        "sessionId": session_id,
        "inputText": prompt,
        "enableTrace": False,
    }

    response = bedrock_agent_runtime.invoke_agent(**request_params)

    response_text = ""
    for event in response["completion"]:
        if "chunk" in event:
            chunk_bytes = event["chunk"].get("bytes", b"")
            decoded_response = chunk_bytes.decode("utf-8")
            response_text += decoded_response

    return response_text


def assess_similarity(agent_response, expected_output):
    llm_similarity_score = find_llm_similarity(
        model_output=agent_response,
        expected_output=expected_output,
        model_name="amazon.nova-micro-v1:0",
    )

    # Print for debugging
    print(f"Agent response: {agent_response}")
    print(f"Expected output: {expected_output}")
    print(f"LLM similarity score: {llm_similarity_score:.2f}")

    assert llm_similarity_score >= 0.8, (
        f"LLM similarity too low: {llm_similarity_score:.2f}"
    )


@pytest.mark.asyncio
async def test_value_blacktip_asset():
    prompt = "Value the asset Blacktip"

    async def get_expected_output():
        assets_df = get_asset_data()
        blacktip = assets_df[
            (assets_df["asset_name"] == "Blacktip")
            & (assets_df["price_scenario"] == "BASE")
        ].iloc[0]

        return f"""| Asset Name | Remaining PV Post-tax ($ million) | Remaining PV Pre-tax ($ million) | 
        |------------|-----------------------------------|----------------------------------| 
        | Blacktip | {blacktip["remaining_pv_post_tax"]:.2f} | {blacktip["remaining_pv_pre_tax"]:.2f} | 
        The value of the Blacktip asset based on the BASE price scenario is: 
        - Remaining PV Post-tax: ${blacktip["remaining_pv_post_tax"]:.2f} million 
        - Remaining PV Pre-tax: ${blacktip["remaining_pv_pre_tax"]:.2f} million 
        These values represent the future cash flows of the Blacktip asset discounted to present value, both after and before taxes."""

    [expected_output, agent_response] = await asyncio.gather(
        get_expected_output(), invoke_bedrock_agent(prompt)
    )
    assess_similarity(agent_response, expected_output)


@pytest.mark.asyncio
async def test_benchmark_north_america_assets():
    prompt = "Benchmark the top five upstream assets in North America by capex/boe, identifying those with the lowest capex/boe."

    async def get_expected_output():
        assets_df = get_asset_data()
        na_assets = assets_df[
            (assets_df["country"].isin(["United States", "Canada", "Mexico"]))
            & (assets_df["price_scenario"] == "BASE")
        ]
        na_assets_sorted = na_assets.sort_values("capex_boe")
        top_5_assets = na_assets_sorted.head(5)

        expected_output = """| Asset Name | Country | Capex/boe | Remaining PV Post-tax (millions USD) | Remaining PV Pre-tax (millions USD) | 
        |------------|---------|-----------|--------------------------------------|-------------------------------------| """

        for _, asset in top_5_assets.iterrows():
            expected_output += f"\n    | {asset['asset_name']} | {asset['country']} | {asset['capex_boe']:.4f} | {asset['remaining_pv_post_tax']:.2f} | {asset['remaining_pv_pre_tax']:.2f} | "

        expected_output += """
        ===== These are the top five upstream assets in North America with the lowest capex/boe. 
        The capex/boe metric represents the capital expenditure per barrel of oil equivalent, which is a measure of the efficiency of capital investment in oil and gas production. 
        Interestingly, the top two assets show negative capex/boe values, which could indicate either data anomalies or special circumstances such as asset sales or accounting adjustments. 
        The remaining three assets show zero capex/boe, which might suggest fully developed assets with no further capital expenditure required. 
        The "Remaining PV Post-tax" and "Remaining PV Pre-tax" columns represent the present value of future cash flows from these assets, 
        both after and before taxes, respectively. These values are in millions of USD. It's worth noting that four out of the five assets are located in Canada, with only one in the United States. 
        This could suggest that Canadian upstream assets in the dataset generally have lower capex/boe compared to those in the US and Mexico."""
        return expected_output

    [expected_output, agent_response] = await asyncio.gather(
        get_expected_output(), invoke_bedrock_agent(prompt)
    )
    assess_similarity(agent_response, expected_output)


@pytest.mark.asyncio
async def test_highest_npv_field_norway():
    prompt = "Which field in Norway has the highest Remaining NPV"

    async def get_expected_output():
        assets_df = get_asset_data()
        norway_assets = assets_df[
            (assets_df["country"] == "Norway") & (assets_df["price_scenario"] == "BASE")
        ]
        top_norway_asset = norway_assets.sort_values(
            "remaining_pv_post_tax", ascending=False
        ).iloc[0]

        expected_output = f"""| Asset Name | Remaining PV Post-tax (millions USD) | Remaining PV Pre-tax (millions USD) | 
        |------------|---------------------------------------|-------------------------------------| 
        | {top_norway_asset["asset_name"]} | {top_norway_asset["remaining_pv_post_tax"]:.2f} | {top_norway_asset["remaining_pv_pre_tax"]:.2f} | ===== 
        The field in Norway with the highest Remaining Net Present Value (NPV) is {top_norway_asset["asset_name"]}. 
        The Remaining PV Post-tax, which represents the after-tax net present value of future cash flows, is approximately {top_norway_asset["remaining_pv_post_tax"] / 1000:.2f} billion USD. 
        This indicates that {top_norway_asset["asset_name"]} is a highly valuable asset in Norway's oil and gas portfolio. 
        The Remaining PV Pre-tax, showing the value before taxes, is significantly higher at about {top_norway_asset["remaining_pv_pre_tax"] / 1000:.2f} billion USD. 
        The large difference between pre-tax and post-tax values reflects the substantial tax burden on oil and gas operations in Norway. 
        {top_norway_asset["asset_name"]} is indeed a major field in the Norwegian Continental Shelf, known for its large gas and oil reserves. Its high NPV suggests it continues to be a crucial asset for Norway's petroleum industry."""
        return expected_output

    [expected_output, agent_response] = await asyncio.gather(
        get_expected_output(), invoke_bedrock_agent(prompt)
    )
    assess_similarity(agent_response, expected_output)


@pytest.mark.asyncio
async def test_value_zohr_field_egypt():
    prompt = "What is the value of the Zohr field in Eygpt?"

    async def get_expected_output():
        assets_df = get_asset_data()
        zohr = assets_df[
            (assets_df["asset_name"] == "Zohr")
            & (assets_df["country"] == "Egypt")
            & (assets_df["price_scenario"] == "BASE")
        ].iloc[0]

        expected_output = f"""| Asset Name | Country | Remaining PV Post-tax (millions USD) | Remaining PV Pre-tax (millions USD) | 
        |------------|---------|---------------------------------------|-------------------------------------| 
        | Zohr | Egypt | {zohr["remaining_pv_post_tax"]:.2f} | {zohr["remaining_pv_pre_tax"]:.2f} | ===== 
        The value of the Zohr field in Egypt can be expressed in two ways: 
        1. Remaining PV Post-tax: Approximately {zohr["remaining_pv_post_tax"] / 1000:.2f} billion USD 
        2. Remaining PV Pre-tax: Approximately {zohr["remaining_pv_pre_tax"] / 1000:.2f} billion USD 
        The Remaining PV Post-tax represents the net present value of future cash flows after taxes, 
        which is often considered the more relevant figure for assessing the actual value to the operating company. 
        The Remaining PV Pre-tax shows the value before taxes are applied. 
        The significant difference between the pre-tax and post-tax values indicates a substantial tax burden on the operations of this field in Egypt. 
        Zohr is indeed a major gas field in the Mediterranean Sea off the coast of Egypt, discovered in 2015. 
        Its high valuation reflects its importance as one of the largest gas fields in the region."""
        return expected_output

    [expected_output, agent_response] = await asyncio.gather(
        get_expected_output(), invoke_bedrock_agent(prompt)
    )
    assess_similarity(agent_response, expected_output)


@pytest.mark.asyncio
async def test_compare_fuzzy_assets_tengiz_vs_kashagan():
    prompt = "compare tengiz and kashagan"

    async def get_expected_output():
        assets_table = get_asset_data()
        filtered_df = assets_table[
            (assets_table["asset_name"].str.lower().str.contains("tengiz"))
            | (assets_table["asset_name"].str.lower().str.contains("kashagan"))
        ]
        filtered_df = filtered_df[filtered_df["price_scenario"] == "BASE"]
        selected_columns = [
            "asset_name",
            "country",
            "remaining_pv_post_tax",
            "remaining_pv_pre_tax",
            "total_remaining_reserves_wi_mmboe",
            "remaining_liquid_reserves_wi_mmbbls",
            "remaining_gas_reserves_wi_bcf",
            "capex_boe",
            "opex_boe",
            "reserve_life_at_current_production_years",
            "post_tax_irr",
            "pre_tax_irr",
        ]

        result = filtered_df[selected_columns].sort_values(by="asset_name").head(3)

        expected_output = f"""
            # Comparison of Kazakhstan Assets
            {result.to_markdown()}
            =====
            ## Analysis: Tengiz vs Kashagan

            ### 1. Location
            Both assets are located in Kazakhstan.

            ### 2. Valuation
            - **Kashagan Contract Area** has the highest remaining post-tax value at ${result["remaining_pv_post_tax"].to_list()[0] / 1000} billion, followed by **Tengizchevroil Area** at ${result["remaining_pv_post_tax"].to_list()[2] / 1000} billion.
            - **Tengizchevroil Area** has a higher pre-tax value (${result["remaining_pv_pre_tax"].to_list()[2] / 1000} billion) compared to **Kashagan Contract Area** (${result["remaining_pv_pre_tax"].to_list()[0] / 1000} billion).
            - **Kashagan Phase Two C Sub Commercial** has significantly lower valuations, likely due to its sub-commercial status.

            ### 3. Reserves
            - **Kashagan Contract Area** has the largest total remaining reserves ({result["total_remaining_reserves_wi_mmboe"].to_list()[0]} MMBOE), followed by **Tengizchevroil Area** ({result["total_remaining_reserves_wi_mmboe"].to_list()[2]} MMBOE).
            - **Kashagan** has higher gas reserves, while **Tengiz** has a higher proportion of liquid reserves.

            ### 4. Costs
            - **Tengizchevroil Area** has moderate CAPEX (${result["capex_boe"].to_list()[2]}/BOE) and high OPEX (${result["opex_boe"].to_list()[2]}/BOE).
            - **Kashagan Contract Area** has high CAPEX (${result["capex_boe"].to_list()[0]}/BOE) but lower OPEX (${result["opex_boe"].to_list()[0]}/BOE).
            - **Kashagan Phase Two C Sub Commercial** has very low CAPEX and OPEX, likely due to its development stage.

            ### 5. Reserve Life
            - **Kashagan Contract Area** has a significantly longer reserve life ({result["reserve_life_at_current_production_years"].to_list()[0]} years) compared to **Tengizchevroil Area** ({result["reserve_life_at_current_production_years"].to_list()[2]} years).
            - Reserve life for **Kashagan Phase Two C Sub Commercial** is not available, possibly due to its sub-commercial status.

            ## Summary
            While **Tengiz** (Tengizchevroil Area) is a valuable asset, **Kashagan** (particularly the Contract Area) appears to be larger in terms of reserves and valuation, with a longer reserve life. However, **Tengiz** has lower CAPEX requirements and a higher proportion of liquid reserves, which could be advantageous depending on market conditions and operational strategies.
        """
        return expected_output

    [expected_output, agent_response] = await asyncio.gather(
        get_expected_output(), invoke_bedrock_agent(prompt)
    )
    assess_similarity(agent_response, expected_output)


@pytest.mark.asyncio
async def test_value_harbour_energy():
    prompt = "What is the value of the company Harbour Energy"

    async def get_expected_output():
        companies_df = get_company_data()

        harbour_assets = companies_df[
            (companies_df["company"] == "Harbour Energy")
            & (companies_df["price_scenario"] == "BASE")
        ]

        harbour = {
            "Total Remaining PV Post-tax": harbour_assets[
                "remaining_pv_post_tax"
            ].sum(),
            "Total Remaining PV Pre-tax": harbour_assets["remaining_pv_pre_tax"].sum(),
            "Total Remaining Reserves WI (mmboe)": harbour_assets[
                "total_remaining_reserves_wi_mmboe"
            ].sum()
            if "total_remaining_reserves_wi_mmboe" in harbour_assets.columns
            else 0,
            "Total Remaining Liquid Reserves WI (mmbbls)": harbour_assets[
                "remaining_liquid_reserves_wi_mmbbls"
            ].sum()
            if "remaining_liquid_reserves_wi_mmbbls" in harbour_assets.columns
            else 0,
            "Total Remaining Gas Reserves WI (bcf)": harbour_assets[
                "remaining_gas_reserves_wi_bcf"
            ].sum()
            if "remaining_gas_reserves_wi_bcf" in harbour_assets.columns
            else 0,
        }

        expected_output = f"""| Valuation Metric | Value (millions USD) | |-------------------|----------------------| 
        | Total Remaining PV Post-tax | {harbour["Total Remaining PV Post-tax"]:.2f} | 
        | Total Remaining PV Pre-tax | {harbour["Total Remaining PV Pre-tax"]:.2f} | 
        | Total Remaining Reserves WI (mmboe) | {harbour["Total Remaining Reserves WI (mmboe)"]:.2f} | 
        | Total Remaining Liquid Reserves WI (mmbbls) | {harbour["Total Remaining Liquid Reserves WI (mmbbls)"]:.2f} |
        | Total Remaining Gas Reserves WI (bcf) | {harbour["Total Remaining Gas Reserves WI (bcf)"]:.2f} | ===== 
        The value of Harbour Energy can be expressed in two ways: 
        1. Total Remaining PV Post-tax: Approximately {harbour["Total Remaining PV Post-tax"] / 1000:.2f} billion USD 
        2. Total Remaining PV Pre-tax: Approximately {harbour["Total Remaining PV Pre-tax"] / 1000:.2f} billion USD 
        The Total Remaining PV Post-tax represents the sum of the net present values of all Harbour Energy's assets after taxes. 
        This figure, about {harbour["Total Remaining PV Post-tax"] / 1000:.2f} billion USD, is often considered the more relevant measure for assessing the company's value to shareholders. 
        The Total Remaining PV Pre-tax shows the value before taxes are applied, which is significantly higher at about {harbour["Total Remaining PV Pre-tax"] / 1000:.2f} billion USD. 
        The large difference between the pre-tax and post-tax values indicates a substantial tax burden on Harbour Energy's operations across its various assets. 
        In addition, Harbour Energy has total remaining reserves of {harbour["Total Remaining Reserves WI (mmboe)"]:.2f} million barrels of oil equivalent (mmboe),
        which includes {harbour["Total Remaining Liquid Reserves WI (mmbbls)"]:.2f} million barrels of liquid reserves and 
        {harbour["Total Remaining Gas Reserves WI (bcf)"]:.2f} billion cubic feet of gas reserves.
        These reserves figures provide an indication of the company's resource base for future production.
        It's important to note that these figures represent the sum of the remaining present values of the company's assets and may not directly correspond to the company's market capitalization or enterprise value, which can be influenced by additional factors such as debt, cash on hand, and market sentiment."""
        return expected_output

    [expected_output, agent_response] = await asyncio.gather(
        get_expected_output(), invoke_bedrock_agent(prompt)
    )
    assess_similarity(agent_response, expected_output)


@pytest.mark.asyncio
async def test_compare_bp_shell():
    prompt = "compare BP and Shell"

    async def get_expected_output():
        companies_df = get_company_data()

        bp_assets = companies_df[
            (companies_df["company"] == "BP")
            & (companies_df["price_scenario"] == "BASE")
        ]
        shell_assets = companies_df[
            (companies_df["company"] == "Shell")
            & (companies_df["price_scenario"] == "BASE")
        ]

        bp = {
            "Remaining PV Post-tax": bp_assets["remaining_pv_post_tax"].sum(),
            "Remaining PV Pre-tax": bp_assets["remaining_pv_pre_tax"].sum(),
            "Total Remaining Reserves WI (mmboe)": bp_assets[
                "total_remaining_reserves_wi_mmboe"
            ].sum(),
            "Remaining Liquid Reserves WI (mmbbls)": bp_assets[
                "remaining_liquid_reserves_wi_mmbbls"
            ].sum(),
            "Remaining Gas Reserves WI (bcf)": bp_assets[
                "remaining_gas_reserves_wi_bcf"
            ].sum(),
            "Average_Capex_boe": bp_assets["capex_boe"].mean(),
            "Average_Opex_boe": bp_assets["opex_boe"].mean(),
        }

        shell = {
            "Remaining PV Post-tax": shell_assets["remaining_pv_post_tax"].sum(),
            "Remaining PV Pre-tax": shell_assets["remaining_pv_pre_tax"].sum(),
            "Total Remaining Reserves WI (mmboe)": shell_assets[
                "total_remaining_reserves_wi_mmboe"
            ].sum()
            if "total_remaining_reserves_wi_mmboe" in shell_assets.columns
            else 0,
            "Remaining Liquid Reserves WI (mmbbls)": shell_assets[
                "remaining_liquid_reserves_wi_mmbbls"
            ].sum()
            if "remaining_liquid_reserves_wi_mmbbls" in shell_assets.columns
            else 0,
            "Remaining Gas Reserves WI (bcf)": shell_assets[
                "remaining_gas_reserves_wi_bcf"
            ].sum()
            if "remaining_gas_reserves_wi_bcf" in shell_assets.columns
            else 0,
            "Average_Capex_boe": shell_assets["capex_boe"].mean(),
            "Average_Opex_boe": shell_assets["opex_boe"].mean(),
        }

        expected_output = f"""| Metric | BP | Shell | 
        |--------|----|----| 
        | Total Remaining PV Post-tax (billions USD) | {bp["Remaining PV Post-tax"] / 1000:.2f} | {shell["Remaining PV Post-tax"] / 1000:.2f} | 
        | Total Remaining PV Pre-tax (billions USD) | {bp["Remaining PV Pre-tax"] / 1000:.2f} | {shell["Remaining PV Pre-tax"] / 1000:.2f} | 
        | Total Remaining Reserves WI (mmboe) | {bp["Total Remaining Reserves WI (mmboe)"]:.2f} | {shell["Total Remaining Reserves WI (mmboe)"]:.2f} | 
        | Average Capex/boe (USD) | {bp["Average_Capex_boe"]} | {shell["Average_Capex_boe"]} | 
        | Average Opex/boe (USD) | {bp["Average_Opex_boe"]} | {shell["Average_Opex_boe"]} | 
        | Total Remaining Liquid Reserves WI (mmbbls) | {bp["Remaining Liquid Reserves WI (mmbbls)"]} | {shell["Remaining Liquid Reserves WI (mmbbls)"]} | 
        | Total Remaining Gas Reserves WI (bcf) | {bp["Remaining Gas Reserves WI (bcf)"]} | {shell["Remaining Gas Reserves WI (bcf)"]} | ===== 
        Comparison between BP and Shell: 
        1. Value: - Shell has a higher Total Remaining PV Post-tax (${shell["Remaining PV Post-tax"] / 1000:.2f} billion) compared to BP (${bp["Remaining PV Post-tax"] / 1000:.2f} billion), indicating a higher after-tax value of future cash flows. 
        - Shell also has a higher Total Remaining PV Pre-tax (${shell["Remaining PV Pre-tax"] / 1000:.2f} billion) than BP (${bp["Remaining PV Pre-tax"] / 1000:.2f} billion). 
        - The difference between pre-tax and post-tax values is larger for BP, suggesting potentially higher tax burdens or different tax situations across their asset portfolio. 
        2. Reserves: - BP has slightly higher Total Remaining Reserves ({bp["Total Remaining Reserves WI (mmboe)"]:.2f} mmboe) compared to Shell ({shell["Total Remaining Reserves WI (mmboe)"]:.2f} mmboe). 
        - BP also has higher Total Remaining Liquid Reserves ({bp["Remaining Liquid Reserves WI (mmbbls)"]} mmbbls) than Shell ({shell["Remaining Liquid Reserves WI (mmbbls)"]} mmbbls). 
        - BP's Total Remaining Gas Reserves ({bp["Remaining Gas Reserves WI (bcf)"]} bcf) are also higher than Shell's ({shell["Remaining Gas Reserves WI (bcf)"]} bcf). 
        3. Costs: - Shell has a slightly higher Average Capex/boe ({shell["Average_Capex_boe"]}) compared to BP ({bp["Average_Capex_boe"]}). 
        - However, Shell has a slightly lower Average Opex/boe ({shell["Average_Opex_boe"]}) than BP ({bp["Average_Opex_boe"]}). 
        4. Reserve Mix: - Both companies have a mix of liquid and gas reserves, with BP having a slightly higher proportion of both compared to Shell. 
        In summary, while BP has slightly larger reserves, Shell appears to have higher overall value based on the Remaining PV metrics. 
        This could be due to factors such as the quality of reserves, production efficiency, or the specific locations and types of assets each company holds. 
        Shell's higher valuation despite slightly lower reserves might indicate more profitable or efficiently managed assets. 
        Both companies have significant capex requirements per barrel, with Shell's being slightly higher, but Shell manages to maintain slightly lower operating costs per barrel. 
        The companies appear to be closely matched in many respects, with each having certain advantages in different areas."""
        return expected_output

    [expected_output, agent_response] = await asyncio.gather(
        get_expected_output(), invoke_bedrock_agent(prompt)
    )
    assess_similarity(agent_response, expected_output)

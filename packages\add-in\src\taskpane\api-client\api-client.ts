import axios from 'axios';
import { getAccessToken } from '../../security';
import { apiClient as apiConfig } from '../../config';

const composeAddAuthorizationHeader = (getAccessToken: () => Promise<string>) => async config => {
    const accessToken = await getAccessToken();

    return {
        ...config,
        headers: {
            ...config.headers,
            ...(accessToken && { authorization: `Bearer ${accessToken}` }),
        },
    };
};

const apiClient = axios.create({
    baseURL: apiConfig.baseUrl,
    paramsSerializer: {
        encode: (param: string) => encodeURIComponent(param),
    },
});

const addAuthorizationHeader = composeAddAuthorizationHeader(getAccessToken);
apiClient.interceptors.request.use(addAuthorizationHeader);

export { apiClient };

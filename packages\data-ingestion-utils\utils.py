import re


def to_snake_case(text):
    if not text:
        return ""
        
    text = text.strip()
    # Insert underscore between lowercase/digit and uppercase (camelCase → camel_Case)
    text = re.sub(r'([a-z0-9])([A-Z])', r'\1_\2', text)
    # Insert underscore between uppercase and uppercase followed by lowercase (SQLServer → SQL_Server)
    text = re.sub(r'([A-Z])([A-Z][a-z])', r'\1_\2', text)
    # Replace spaces, hyphens, dots and other separators with underscores
    text = re.sub(r'[\s\-\.\/\(\)]+', '_', text)
    # Remove any non-alphanumeric characters except underscores
    text = re.sub(r'[^\w]', '', text)
    # Replace multiple underscores with a single one
    text = re.sub(r'_+', '_', text)
    # Remove leading/trailing underscores
    text = text.strip('_')
    return text.lower()
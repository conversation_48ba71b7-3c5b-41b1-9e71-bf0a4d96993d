import axios from 'axios';
import { EntitlementsService } from './entitlements-service';
import { GetSecretValueCommand } from '@aws-sdk/client-secrets-manager';

jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

jest.mock('../logger', () => ({
    debug: jest.fn(),
    info: jest.fn(),
    error: jest.fn(),
    warn: jest.fn(),
}));

const mockSMSend = jest.fn();

jest.mock('@aws-sdk/client-secrets-manager', () => {
    const actual = jest.requireActual('@aws-sdk/client-secrets-manager');
    return {
        ...actual,
        SecretsManagerClient: jest.fn().mockImplementation(() => ({
            send: mockSMSend,
        })),
    };
});


describe('EntitlementsService', () => {
    let service: EntitlementsService;
    const clientSecret = 'test-client-secret';
    const mockEnv = {
        ENTITLEMENTS_OAUTH_URL: 'https://oauth.example.com/token',
        ENTITLEMENTS_CLIENT_ID: 'test-client-id',
        ENTITLEMENTS_CLIENT_SECRET_NAME: 'test-client-secret-name',
        ENTITLEMENTS_API_URL: 'https://entitlements.example.com',
    };

    beforeEach(() => {
        jest.clearAllMocks();
        Object.assign(process.env, mockEnv);
        service = new EntitlementsService();
        service.clearCaches();
    });

    afterEach(() => {
        process.env.ENTITLEMENTS_OAUTH_URL = undefined;
        process.env.ENTITLEMENTS_CLIENT_ID = undefined;
        process.env.ENTITLEMENTS_CLIENT_SECRET_NAME = undefined;
        process.env.ENTITLEMENTS_API_URL = undefined;
    });

    describe('checkUserEntitlement', () => {
        it('should retrieve client secret from AWS Secrets Manager', async () => {
            mockSMSend.mockResolvedValueOnce({
                SecretString: clientSecret,
            });

            await service.checkUserEntitlement(
                'user123',
                'vals-chat-assistant',
                'VALSCHATASSIST',
            );

            const callArgs = mockSMSend.mock.calls[0][0];
            expect(callArgs).toBeInstanceOf(GetSecretValueCommand);
            expect(callArgs.input.SecretId).toBe(process.env.ENTITLEMENTS_CLIENT_SECRET_NAME);
        });

        it('should return true when user has entitlements', async () => {
            mockSMSend.mockResolvedValueOnce({
                SecretString: clientSecret,
            });

            mockedAxios.post.mockResolvedValueOnce({
                data: {
                    access_token: 'test-token',
                    expires_in: 3600,
                },
            });

            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    totalCount: 1,
                    count: 1,
                    results:  [{ principalId: 'user123', data: [{ val: 'VALSCHATASSIST', typ: 'feature' }] }],
                },
            });

            const result = await service.checkUserEntitlement(
                'user123',
                'vals-chat-assistant',
                'VALSCHATASSIST',
            );

            expect(result).toBe(true);
            expect(mockedAxios.post).toHaveBeenCalledWith(
                'https://oauth.example.com/token',
                expect.any(URLSearchParams),
                expect.objectContaining({
                    headers: expect.objectContaining({
                        Authorization: expect.stringContaining('Basic'),
                    }),
                }),
            );
            expect(mockedAxios.get).toHaveBeenCalledWith(
                'https://entitlements.example.com/api/v1/entitlements/select',
                expect.objectContaining({
                    headers: expect.objectContaining({
                        Authorization: 'Bearer test-token',
                    }),
                    params: {
                        principalId: "user123",
                        productType: "vals-chat-assistant",
                    },
                }),
            );
        });

        it('should return false when user has no entitlements', async () => {
            mockSMSend.mockResolvedValueOnce({
                SecretString: clientSecret,
            });

            mockedAxios.post.mockResolvedValueOnce({
                data: {
                    access_token: 'test-token',
                    expires_in: 3600,
                },
            });

            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    totalCount: 0,
                    count: 0,
                    results: [],
                },
            });

            const result = await service.checkUserEntitlement(
                'user123',
                'vals-chat-assistant',
                'VALSCHATASSIST',
            );

            expect(result).toBe(false);
        });

        it('should return false when OAuth token request fails', async () => {
            mockSMSend.mockResolvedValueOnce({
                SecretString: clientSecret,
            });

            mockedAxios.post.mockRejectedValueOnce(new Error('OAuth failed'));

            const result = await service.checkUserEntitlement(
                'user123',
                'vals-chat-assistant',
                'VALSCHATASSIST',
            );

            expect(result).toBe(false);
        });

        it('should return false when Secret Manager get secret request fails', async () => {
            mockSMSend.mockRejectedValueOnce(new Error('SM failed'));

            const result = await service.checkUserEntitlement(
                'user123',
                'vals-chat-assistant',
                'VALSCHATASSIST',
            );

            expect(result).toBe(false);
        });

        it('should return false when entitlements API call fails', async () => {
            mockSMSend.mockResolvedValueOnce({
                SecretString: clientSecret,
            });

            mockedAxios.post.mockResolvedValueOnce({
                data: {
                    access_token: 'test-token',
                    expires_in: 3600,
                },
            });

            mockedAxios.get.mockRejectedValueOnce(new Error('API failed'));

            const result = await service.checkUserEntitlement(
                'user123',
                'vals-chat-assistant',
                'VALSCHATASSIST',
            );

            expect(result).toBe(false);
        });

        it('should use custom feature parameter', async () => {
            mockSMSend.mockResolvedValueOnce({
                SecretString: clientSecret,
            });

            mockedAxios.post.mockResolvedValueOnce({
                data: {
                    access_token: 'test-token',
                    expires_in: 3600,
                },
            });

            mockedAxios.get.mockResolvedValueOnce({
                data: {
                    totalCount: 1,
                    count: 1,
                    results:  [{ principalId: 'user123', data: [{ val: 'VALSCHATASSIST', typ: 'feature' }] }],
                },
            });

            await service.checkUserEntitlement('user123',
                'vals-chat-assistant',
                'VALSCHATASSIST',);

            expect(mockedAxios.get).toHaveBeenCalledWith(
                expect.any(String),
                expect.objectContaining({
                    params: {
                        principalId: "user123",
                        productType: "vals-chat-assistant",
                    },
                }),
            );
        });
    });
});

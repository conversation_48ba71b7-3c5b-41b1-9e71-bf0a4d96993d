import requests
import time
from typing import Dict, Any
import json
from pathlib import Path
from datetime import datetime
from asset_json_flattener import flatten_discrete_parquet, flatten_timeseries_parquet
from lens_direct_metadata_provider import LensDirectMetadataProvider

current_year = datetime.now().year

BASE_REQUEST = {
    "cashflowOutput": "nominal",
    "discounting": {"month": 1, "year": current_year, "percentage": 10},
    "economicCutoffApplied": False,
    "entitlementBasis": "method-2",
    "priceDeckId": "dd4170e7-be13-4500-8bcd-b20a580aecb1|00gcd5xza61ty9ZWZ1d7",
    "sensitivities": {
        "startYear": current_year,
        "capexPercentage": 0,
        "gasPricePercentage": 0,
        "gasProductionPercentage": 0,
        "liquidPricePercentage": 0,
        "liquidProductionPercentage": 0,
        "opexPercentage": 0,
        "tariffPaymentPercentage": 0,
        "tariffReceiptPercentage": 0,
    },
    "taxSchedule": "paid",
    "taxSynergies": "standalone",
}


class ValuationClient:
    def __init__(
        self,
        uvs_url: str,
        report_provider_url: str,
        data_repository_url: str,
        token: str,
        lens_direct_metadata_provider: LensDirectMetadataProvider = None,
    ):
        self.uvs_url = uvs_url
        self.report_provider_url = report_provider_url
        self.token = token
        self.data_repository_url = data_repository_url
        self.lens_direct_metadata_provider = lens_direct_metadata_provider        

    def merge_requests(self, post_body: Dict[Any, Any]) -> Dict[Any, Any]:
        final_request = BASE_REQUEST.copy()

        def deep_update(original: Dict, update: Dict) -> Dict:
            for key, value in update.items():
                if isinstance(value, dict) and key in original:
                    original[key] = deep_update(original[key], value)
                else:
                    original[key] = value
            return original

        return deep_update(final_request, post_body)

    def run_valuation(
        self, post_body: Dict[Any, Any] = {}, timeout_minutes: int = 5
    ) -> Dict:
        def make_request_with_retry(method, url, **kwargs):
            try:
                response = method(url, **kwargs)
                response.raise_for_status()
                return response
            except requests.exceptions.HTTPError as e:
                if 500 <= e.response.status_code < 600:
                    # Retry once on 5xx errors
                    time.sleep(1)
                    response = method(url, **kwargs)
                    response.raise_for_status()
                    return response
                raise

        start_time = time.time()

        # Make initial POST request
        req = self.merge_requests(post_body)
        post_response = make_request_with_retry(
            requests.post,
            f"{self.uvs_url}/valuations/v1",
            json=req,
            headers={"Cookie": f"iPlanetDirectoryPro={self.token}"},
        )
        valuation_id = post_response.json()["valuationId"]
        print(
            f"Created a valuation with ID: {valuation_id}. Took {time.time() - start_time:.2f} seconds",
            flush=True,
        )

        # Poll until complete
        start_time = time.time()
        timeout_seconds = timeout_minutes * 60

        while True:
            if time.time() - start_time > timeout_seconds:
                raise Exception(f"Polling timed out after {timeout_minutes} minutes")

            poll_response = make_request_with_retry(
                requests.get,
                f"{self.uvs_url}/valuations/v1/{valuation_id}",
                headers={"Cookie": f"iPlanetDirectoryPro={self.token}"},
            )
            response_data = poll_response.json()

            if response_data["status"] == "completed":
                print(
                    f"Completed valuation with ID: {valuation_id}. (number of assets {response_data['assetsTotal']}). Took {time.time() - start_time:.2f} seconds",
                    flush=True,
                )
                return valuation_id
            elif response_data["status"] == "failed":
                print(f"Failed valuation with ID: {valuation_id}", flush=True)
                raise Exception("Process failed")

            time.sleep(1)

    def save_valuation_report(
        self, valuation_id: str, price_scenario: str, scenario_name: str, version: str
    ) -> None:
        # Create output directory if it doesn't exist
        output_dir = Path(f"output/{valuation_id}")
        output_dir.mkdir(parents=True, exist_ok=True)
        output_file = output_dir / "reports.json"

        # check if file exists before making the request
        if output_file.exists():
            print(f"Report already exists at {output_file}", flush=True)
        else:
            response = requests.post(
                f"{self.report_provider_url}/api/ValuationReport/RetrieveReport",
                headers={"Content-Type": "application/json"},
                json={
                    "header": {
                        "correlationId": valuation_id,
                        "flags": {"enableCustomCalculations": True},
                        "createdBy": "wmlgv-assistant-data-generator",
                    },
                    "body": {},
                },
            )
            response.raise_for_status()
            # Save response to file
            with open(f"{output_dir}/{scenario_name}.txt", "w") as f:
                f.write("\n")
            with open(output_file, "w") as f:
                f.write(response.text)
            print(f"Saved report to {output_file}", flush=True)

        parquet_path = f"{output_file}".replace(
            "reports.json", "flattened_discrete.parquet"
        )
        print(
            f"Creating flattened parquet (discrete metrics): {parquet_path}", flush=True
        )
        start_time = time.time()
        with open(output_file) as f:
            flatten_discrete_parquet(
                json.load(f),
                parquet_path,
                price_scenario,
                asset_info_getter=self.get_asset_info(version),
                lens_direct_metadata_provider=self.lens_direct_metadata_provider,
            )
        print(
            f"Flattened discrete parquet created in {time.time() - start_time:.2f} seconds",
            flush=True,
        )

        parquet_path = f"{output_file}".replace(
            "reports.json", "flattened_timeseries.parquet"
        )
        print(
            f"Creating flattened parquet (timeseries metrics): {parquet_path}",
            flush=True,
        )
        start_time = time.time()
        with open(output_file) as f:
            flatten_timeseries_parquet(
                json.load(f),
                parquet_path,
                price_scenario,
                asset_info_getter=self.get_asset_info(version),
                lens_direct_metadata_provider=self.lens_direct_metadata_provider,
            )
            print(
                f"Flattened timeseries parquet created in {time.time() - start_time:.2f} seconds",
                flush=True,
            )

    def _get_domain_mapping(self, version: str):
        if not hasattr(self, "_domain_mapping_cache"):
            self._domain_mapping_cache = {}
        if version in self._domain_mapping_cache:
            return self._domain_mapping_cache[version]

        url = f"{self.data_repository_url}/api/DataRepositoryManager/GetDomainMapping?version={version}"
        try:
            response = requests.get(url)
            response.raise_for_status()
            data = response.json()

            processed_data = {}
            for asset in data.get("assets", []):
                key = f"{asset.get('countryName', '')}-{asset.get('regimeName', '')}-{asset.get('assetName', '')}"
                processed_data[key] = asset

            self._domain_mapping_cache[version] = processed_data
            print(
                f"Pulled domain mapping for version '{version}' with {len(processed_data)} assets",
                flush=True,
            )

            return processed_data

        except Exception as e:
            print(
                f"Error fetching domain mapping for version {version}: {str(e)}",
                flush=True,
            )
            raise e

    def get_asset_info(
        self,
        version: str,
    ):
        domain_mapping = self._get_domain_mapping(version)

        def asset_info_getter(country_name: str, regime_name: str, asset_name: str):
            key = "-".join([country_name, regime_name, asset_name])
            asset = domain_mapping.get(key, {})
            return asset.get("wmdbAssetType", "unknown"), int(asset.get("wmdbAssetId", None)) if asset.get("wmdbAssetId", None) else None

        return asset_info_getter


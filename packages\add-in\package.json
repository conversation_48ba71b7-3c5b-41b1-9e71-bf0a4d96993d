{"name": "office-addin-taskpane-react", "version": "0.0.1", "repository": {"type": "git", "url": "https://github.com/OfficeDev/Office-Addin-TaskPane-React.git"}, "license": "MIT", "config": {"app_to_debug": "excel", "app_type_to_debug": "desktop", "dev_server_port": 3000}, "engines": {"node": ">=20.0.0"}, "scripts": {"build": "webpack --mode production", "build:dev": "webpack --mode development", "dev-server": "webpack serve --mode development", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "office-addin-lint prettier --files src/**/*.{ts,tsx}", "signin": "office-addin-dev-settings m365-account login", "signout": "office-addin-dev-settings m365-account logout", "start": "office-addin-debugging start manifests/manifest.local.xml", "start:desktop": "office-addin-debugging start manifests/manifest-wm-assistant.local.xml desktop", "start:web": "office-addin-debugging start manifests/manifest-wm-assistant.local.xml web", "stop": "office-addin-debugging stop manifests/manifest-wm-assistant.local.xml", "test": "jest", "test:coverage": "jest --coverage", "test:watch": "jest --watch", "validate": "office-addin-manifest validate manifests/manifest-wm-assistant.local.xml", "watch": "webpack --mode development --watch"}, "dependencies": {"@fluentui/react-components": "^9.46.8", "@fluentui/react-icons": "^2.0.232", "@okta/okta-auth-js": "^7.10.1", "@okta/okta-react": "^6.9.0", "axios": "^1.7.9", "core-js": "^3.36.0", "es6-promise": "^4.2.8", "launchdarkly-react-client-sdk": "^3.6.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-markdown": "^10.1.0", "react-router-dom": "^5.3.0", "regenerator-runtime": "^0.14.1", "uuid": "^11.0.3"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/preset-env": "^7.25.4", "@babel/preset-typescript": "^7.23.3", "@eslint/js": "^9.27.0", "@svgr/webpack": "^8.1.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^29.5.14", "@types/office-js": "^1.0.377", "@types/office-runtime": "^1.0.35", "@types/react": "^18.3.22", "@types/react-dom": "^18.3.7", "@types/webpack": "^5.28.5", "acorn": "^8.11.3", "babel-loader": "^9.1.3", "copy-webpack-plugin": "^12.0.2", "eslint": "^8.57.1", "eslint-plugin-office-addins": "^3.0.2", "eslint-plugin-react": "^7.28.0", "file-loader": "^6.2.0", "html-loader": "^5.0.0", "html-webpack-plugin": "^5.6.0", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "less": "^4.2.0", "less-loader": "^12.2.0", "office-addin-cli": "^1.6.5", "office-addin-debugging": "^5.1.6", "office-addin-dev-certs": "^1.13.5", "office-addin-lint": "^2.3.5", "office-addin-manifest": "^1.13.6", "office-addin-prettier-config": "^1.2.1", "os-browserify": "^0.3.0", "process": "^0.11.10", "source-map-loader": "^5.0.0", "ts-jest": "^29.3.4", "ts-loader": "^9.5.1", "typescript": "^5.8.3", "typescript-eslint": "^8.32.1", "url-loader": "^4.1.1", "webpack": "^5.95.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "5.1.0"}, "prettier": "office-addin-prettier-config", "browserslist": ["last 2 versions", "ie 11"]}
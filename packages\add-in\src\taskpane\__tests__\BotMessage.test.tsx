import React from "react";
import { render, screen } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { BotMessage } from "../BotMessage";

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("BotMessage", () => {
  it("renders message text", () => {
    const testMessage = "This is a bot response";
    renderWithProvider(<BotMessage message={testMessage} />);

    expect(screen.getByText(testMessage)).toBeInTheDocument();
  });

  it("renders table data when provided", () => {
    const tableData = [
      ["Name", "Value"],
      ["Test", "123"],
    ];
    renderWithProvider(<BotMessage tableData={tableData} />);

    expect(screen.getByText("Name")).toBeInTheDocument();
    expect(screen.getByText("Value")).toBeInTheDocument();
    expect(screen.getByText("Test")).toBeInTheDocument();
    expect(screen.getByText("123")).toBeInTheDocument();
  });

  it("renders message with table placeholder", () => {
    const message = "Here is your data: {{TABLE}} Please review it.";
    const tableData = [["Header"], ["Data"]];

    renderWithProvider(<BotMessage message={message} tableData={tableData} />);

    expect(screen.getByText("Here is your data:")).toBeInTheDocument();
    expect(screen.getByText("Please review it.")).toBeInTheDocument();
    expect(screen.getByText("Header")).toBeInTheDocument();
  });

  it("shows footer with AI warning by default", () => {
    const footerComponents = [<div key="test">Footer content</div>];
    renderWithProvider(<BotMessage message="Test" footerComponents={footerComponents} />);

    expect(screen.getByText("Footer content")).toBeInTheDocument();
    expect(screen.getByText("AI-generated content may be incorrect")).toBeInTheDocument();
  });

  it("hides AI warning when hideAiWarning is true", () => {
    const footerComponents = [<div key="test">Footer content</div>];
    renderWithProvider(<BotMessage message="Test" footerComponents={footerComponents} hideAiWarning={true} />);

    expect(screen.getByText("Footer content")).toBeInTheDocument();
    expect(screen.queryByText("AI-generated content may be incorrect")).not.toBeInTheDocument();
  });

  it("shows footer with AI warning by when no other footer components are provided``", () => {
    renderWithProvider(<BotMessage message="Test" />);

    expect(screen.getByText("AI-generated content may be incorrect")).toBeInTheDocument();
  });

  it("shows loading skeleton when loading is true", () => {
    renderWithProvider(<BotMessage loading={true} />);

    expect(document.querySelector(".fui-Skeleton")).toBeInTheDocument();
  });
});

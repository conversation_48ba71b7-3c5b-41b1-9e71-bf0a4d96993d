/* eslint-disable no-undef */

const devCerts = require("office-addin-dev-certs");
const CopyWebpackPlugin = require("copy-webpack-plugin");
const HtmlWebpackPlugin = require("html-webpack-plugin");

async function getHttpsOptions() {
  const httpsOptions = await devCerts.getHttpsServerOptions();
  return { ca: httpsOptions.ca, key: httpsOptions.key, cert: httpsOptions.cert };
}

module.exports = async (env, options) => { 
  const buildenv = 'local';
  const config = {
    devtool: "source-map",
    entry: {
      taskpane: "./src/taskpane/index.tsx",
      login: "./src/login/index.tsx",
    },
    output: {
      clean: true,
      filename: "[name].[contenthash:8].bundle.js",
      chunkFilename: "[name].[contenthash:8].chunk.js",
    },
    resolve: {
      extensions: [".ts", ".tsx", ".js"],
    },
    module: {
      rules: [
        {
          test: /\.ts$/,
          exclude: /node_modules/,
          use: {
            loader: "babel-loader",
          },
        },
        {
          test: /\.tsx?$/,
          exclude: /node_modules/,
          use: ["ts-loader"],
        },
        {
          test: /\.html$/,
          exclude: /node_modules/,
          loader: "html-loader",
          options: {
            sources: {
              urlFilter: (_, value) => !value.includes('/config.js'),
            },
          },
          
        },
        {
          test: /\.(png|jpg|jpeg|ttf|woff|woff2|gif|ico)$/,
          type: "asset/resource",
          generator: {
            filename: "assets/[name][ext][query]",
          },
        },
        {
          test: /\.svg$/,
          oneOf: [
            {
              resourceQuery: /component/, // e.g. import MySvg from './my.svg?component'
              use: ['@svgr/webpack'],
            },
            {
              type: 'asset/resource', // Regular import as an image
            },
          ],
        }
      ],
    },
    plugins: [
      new HtmlWebpackPlugin({
        filename: "taskpane.html",
        template: "./src/taskpane/taskpane.html",
        chunks: ["taskpane"],
      }),
      new HtmlWebpackPlugin({
        filename: "login.html",
        template: "./src/login/login.html",
        chunks: ["login"],
      }),
      new HtmlWebpackPlugin({
        filename: "login/callback",
        template: "./src/login/login.html",
        chunks: ["login"],
      }),
      new CopyWebpackPlugin({
        patterns: [
          {
            from: "assets/*",
            to: "assets/[name][ext][query]",
          },
          {        
            from: `config/config.${buildenv}.js`,
            to: "./config.js",
          },
          {        
            from: `config/config.${buildenv}.js`,
            to: "./login/config.js",
          },
        ],
      }),
    ],
    devServer: {
      hot: true,
      headers: {
        "Access-Control-Allow-Origin": "*",
      },
      server: {
        type: "https",
        options: env.WEBPACK_BUILD || options.https !== undefined ? options.https : await getHttpsOptions(),
      },
      port: process.env.npm_package_config_dev_server_port || 3000,
    },
  };

  return config;
};

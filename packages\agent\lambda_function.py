import time
from logger import (
    get_logger,
    set_global_correlation_id,
    set_bedrock_agent_info,
    set_trace_attributes,
    set_trace_response_attributes,
)
from tools.valuation_data_query_tool import Tools as ValuationDataQueryTool


def get_query_tool():
    return ValuationDataQueryTool()


def lambda_handler(event, context):
    start_time = time.time()
    set_global_correlation_id(context)
    set_bedrock_agent_info(event)

    action = event.get("actionGroup", "unknown")
    api_path = event.get("apiPath", "unknown")
    http_method = event.get("httpMethod", "unknown")
    parameters = event.get("parameters", [])

    # Add request details to the trace
    set_trace_attributes(api_path=api_path, action=action)

    logger = get_logger("handler")
    logger.debug("Received event", extra={"event": event, "context": context})

    # Initialize response code to None
    response_code = None

    # Extract the action group, api path, and parameters from the prediction

    # Get the query value from the parameters
    param_dict = {
        param["name"]: param["value"]
        for param in parameters
        if "name" in param and "value" in param
    }

    logger.info(
        "received request",
        extra={"action": action, "apiPath": api_path, "parameters": param_dict},
    )

    # Check the api path to determine which tool function to call
    if api_path == "/api/get-company-asset-discrete-table-schema":
        body = get_query_tool().get_company_asset_discrete_schema()
        response_body = {"application/json": {"body": str(body)}}
        response_code = 200
    elif api_path == "/api/get-asset-discrete-table-schema":
        body = get_query_tool().get_asset_discrete_schema()
        response_body = {"application/json": {"body": str(body)}}
        response_code = 200
    elif api_path == "/api/get-company-asset-timeseries-schema":
        body = get_query_tool().get_company_asset_timeseries_schema()
        response_body = {"application/json": {"body": str(body)}}
        response_code = 200
    elif api_path == "/api/get-asset-timeseries-schema":
        body = get_query_tool().get_asset_timeseries_schema()
        response_body = {"application/json": {"body": str(body)}}
        response_code = 200
    elif api_path == "/api/query_dataset":
        body = get_query_tool().query_dataset(param_dict.get("sql_query", ""))
        response_body = {"application/json": {"body": body}}
        response_code = 200
    else:
        # If the api path is not recognized, return an error message
        body = {"{}::{} is not a valid api, try another one.".format(action, api_path)}
        response_code = 400
        response_body = {"application/json": {"body": str(body)}}

    action_response = {
        "actionGroup": action,
        "apiPath": api_path,
        "httpMethod": http_method,
        "httpStatusCode": response_code,
        "responseBody": response_body,
    }

    api_response = {"messageVersion": "1.0", "response": action_response}
    response_time = int((time.time() - start_time) * 1000)

    # Update trace attributes with response details
    set_trace_response_attributes(response_code)

    if response_code != 200:
        logger.error(
            "request failed",
            extra={"response": api_response, "responseTime": response_time},
        )
    else:
        logger.info(
            "request completed",
            extra={"response": api_response, "responseTime": response_time},
        )

    return api_response


if __name__ == "__main__":
    lambda_handler(
        {
            "actionGroup": "test",
            "apiPath": "/api/get-company-asset-discrete-table-schema",
            "inputText": "UK assets",
            "httpMethod": "GET",
        },
        None,
    )
    lambda_handler(
        {
            "actionGroup": "test",
            "apiPath": "/api/query_dataset",
            "inputText": "UK assets",
            "parameters": [
                {
                    "name": "sql_query",
                    "value": "SELECT COUNT(*) FROM company_asset_discrete_table",
                }
            ],
            "httpMethod": "GET",
        },
        None,
    )

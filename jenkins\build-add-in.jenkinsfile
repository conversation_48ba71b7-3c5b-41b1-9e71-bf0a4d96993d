#!/usr/bin/env groovy
@Library('utils') _

AWS_REGION = 'us-east-1'
BUILD_ENVIRONMENT = woodmac.getJenkinsEnvironment()

jenkinsAgent = "harbor.prod.woodmac.com/liveservices/jenkins-node-jnlp-cloudformation:3283.v92c105e0f819-1-alpine-jdk21-aws2"

String getEnvironment() {
    if (params.TARGET_ENVIRONMENT != null) {
        return params.TARGET_ENVIRONMENT
    }

    return 'dev'
}

def getAvailableEnvironments() {
    return 'dev'
}

properties([
    parameters([
        choice(name: 'TARGET_ENVIRONMENT', description: 'Environment to deploy', choices: getAvailableEnvironments()),
    ])
])

environmentName = getEnvironment()
def nexusCredentials;

pipeline {
    agent  {
        ecs {
            inheritFrom "dynamic-us-east-1-${environmentName}"
            taskrole woodmac.getAgentRole(
                region: 'us-east-1',
                environment: "${environmentName}",
                parameterName: "/${environmentName}/wmim/cost-module-api-agent-role-name",
            )
            image "${jenkinsAgent}"
            memory "6114"
            cpu "2048"
        }
    }
    environment {
        NODE_OPTIONS = '--max_old_space_size=4096'
    }
    options {
        disableConcurrentBuilds()
        ansiColor('xterm')
    }
    stages {
        stage('build add-in') {
            steps {
                dir('packages/add-in') {
                    sh 'npm ci'
                    sh 'npm run build'
                    sh 'npm run lint'
                    sh 'npm run test:coverage'
                }
            }
        }
        stage('publish add-in to nexus') {
            when {
                branch 'main'
            }
            steps {
                script {
                    nexusCredentials = nexus.getCredentials('us-east-1', 'prod')
                }
                dir('packages/add-in') {
                    sh 'cp -r config/ dist/config/'
                    sh "tar -czf deployment-package.tar.gz dist/*"
                    script {
                        nexus.publish(
                        type: 'raw',
                        env: 'prod',
                        credentials: nexusCredentials,
                        repository: 'lens-internal-modelling',
                        source_file: 'deployment-package.tar.gz',
                        folder: "wmlgv/assistant-addin",
                        verbose: true)
                    }
                }
            }
        }
        stage('trigger deploy') {
            when {
                branch 'main'
            }
            steps {
                script {       
                    build job: "Valuations/Valuations AI/Non AI environment/Non AI Deploy/main",
                    wait: false,
                    propagate: false
                }
            }
        }
    }
}

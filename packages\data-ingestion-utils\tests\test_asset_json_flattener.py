import json
import os
import shutil
import tempfile
from unittest.mock import mock_open, patch
import pandas as pd
import pytest
from copy import deepcopy
from lens_direct_metadata_provider import LensDirectMetadataProvider

from asset_json_flattener import (
    flatten_discrete_metrics,
    flatten_discrete_json,
    flatten_discrete_csv,
    flatten_discrete_parquet,
    flatt_all_json_dir,
    combine_parquet_files,
    flatten_timeseries_metrics,
    flatten_timeseries_json,
    flatten_timeseries_csv,
    flatten_timeseries_parquet,
    extract_values_from_repeat_pattern,
)


sample_countries = [
    {
        "name": "Country1",
        "businessAreas": [
            {
                "name": "BusinessArea1",
                "regimes": [
                    {
                        "name": "Regime1",
                        "assets": [
                            {
                                "name": "Asset1",
                                "valuations": [
                                    {
                                        "type": "Economic",
                                        "scenarios": [
                                            {
                                                "name": "BASE",
                                                "result": {
                                                    "reports": [
                                                        {
                                                            "name": "Asset Summary Discount Rate 6",
                                                            "discreteMetrics": {
                                                                "Remaining PV/boe Post-tax": 100,
                                                                "Payback Period (years)": 200,
                                                            },
                                                        }
                                                    ]
                                                },
                                            }
                                        ],
                                    }
                                ],
                            }
                        ],
                    }
                ],
            }
        ],
    }
]

def asset_info_getter(**kvargs):
    asset_name = kvargs.get("asset_name", "")
    asset_type = "FIELD" if asset_name != "🥔" else "n/a"
    asset_id = f"{asset_name}_ID" if asset_type == "FIELD" else None
    return (asset_type, asset_id)


@pytest.fixture
def sample_country_data():
    return sample_countries


@pytest.fixture
def sample_json_with_companies():
    return {
        "companies": [
            {
                "name": "Company1",
                "countries": sample_countries,
            }
        ]
    }


@pytest.fixture
def sample_json_with_countries():
    return {"body": {"countries": sample_countries}}


@pytest.fixture
def timeseries_country_data():
    return [
        {
            "name": "Country1",
            "businessAreas": [
                {
                    "name": "BusinessArea1",
                    "regimes": [
                        {
                            "name": "Regime1",
                            "assets": [
                                {
                                    "name": "Asset1",
                                    "valuations": [
                                        {
                                            "type": "Economic",
                                            "scenarios": [
                                                {
                                                    "name": "BASE",
                                                    "result": {
                                                        "reports": [
                                                            {
                                                                "name": "Standard Cash Flow",
                                                                "continuousMetrics": {
                                                                    "Year": [
                                                                        2020,
                                                                        2021,
                                                                        2022,
                                                                    ],
                                                                    "Production": [
                                                                        100,
                                                                        200,
                                                                        300,
                                                                    ],
                                                                    "Revenue": {
                                                                        "data": 500,
                                                                        "repeat": 3,
                                                                    },
                                                                    "Opex": [
                                                                        123,
                                                                        {
                                                                            "data": 500,
                                                                            "repeat": 1,
                                                                        },
                                                                        456,
                                                                    ],
                                                                },
                                                            }
                                                        ]
                                                    },
                                                }
                                            ],
                                        }
                                    ],
                                }
                            ],
                        }
                    ],
                }
            ],
        }
    ]


@pytest.fixture
def timeseries_json_with_companies(timeseries_country_data):
    return {"companies": [{"name": "Company1", "countries": timeseries_country_data}]}


@pytest.fixture
def timeseries_json_with_countries(timeseries_country_data):
    return {"body": {"countries": timeseries_country_data}}


@pytest.fixture
def temp_dir():
    temp_dir = tempfile.mkdtemp()
    yield temp_dir
    shutil.rmtree(temp_dir)

class MockLensDirectMetadataProvider:
    def get_asset_metadata(self, asset_id, asset_type):
        if asset_type == "FIELD":
            return {k: f"mock_{k}_{asset_id}" for k in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys()}
        return {}

    def get_company_asset_metadata(self, asset_id, asset_type, company_name, asset_name=None):
        # Return mock company asset metadata for tests that require it
        if asset_type == "FIELD" and company_name:
            return {k: f"mock_{k}_{asset_id}_{company_name}" for k in LensDirectMetadataProvider.FIELD_COMPANY_COLUMN_MAPPING.keys()}
        return {}

def test_flatten_countries(sample_country_data):
    provider = MockLensDirectMetadataProvider()
    result = flatten_discrete_metrics(
        sample_country_data,
        asset_info_getter=asset_info_getter,
        lens_direct_metadata_provider=provider,
    )

    assert isinstance(result, pd.DataFrame)
    assert len(result) == 1
    assert result.iloc[0]["country"] == "Country1"
    assert result.iloc[0]["business_area"] == "BusinessArea1"
    assert result.iloc[0]["regime"] == "Regime1"
    assert result.iloc[0]["asset_name"] == "Asset1"
    assert result.iloc[0]["valuation_type"] == "Economic"
    assert result.iloc[0]["price_scenario"] == "BASE"
    assert result.iloc[0]["remaining_pv_boe_post_tax"] == 100
    assert result.iloc[0]["payback_period_years"] == 200

    assert "Remaining PV/boe Post-tax" not in result.columns

    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert result.iloc[0][col] == f"mock_{col}_Asset1_ID"


def test_flatten_countries_excludes_unsupported_types(sample_country_data):
    data = [
        deepcopy(sample_country_data[0]),
        deepcopy(sample_country_data[0]),
    ]
    data[1]["businessAreas"][0]["regimes"][0]["assets"][0]["name"] = "🥔"

    result = flatten_discrete_metrics(
        data,
        asset_info_getter=asset_info_getter,
    )

    assert isinstance(result, pd.DataFrame)
    assert len(result) == 1
    assert result.iloc[0]["country"] == "Country1"
    assert result.iloc[0]["business_area"] == "BusinessArea1"
    assert result.iloc[0]["regime"] == "Regime1"
    assert result.iloc[0]["asset_name"] == "Asset1"
    assert result.iloc[0]["valuation_type"] == "Economic"
    assert result.iloc[0]["price_scenario"] == "BASE"
    assert result.iloc[0]["remaining_pv_boe_post_tax"] == 100
    assert result.iloc[0]["payback_period_years"] == 200


def test_flatten_countries_with_scenario_name(sample_country_data):
    result = flatten_discrete_metrics(
        sample_country_data,
        scenario_name="CustomScenario",
        asset_info_getter=asset_info_getter,
    )
    assert isinstance(result, pd.DataFrame)
    assert len(result) == 1
    assert result.iloc[0]["price_scenario"] == "CustomScenario"


def test_flatten_countries_returns_empty_dataframe_for_empty_input():
    result = flatten_discrete_metrics(
        [],
        asset_info_getter=asset_info_getter,
    )
    assert isinstance(result, pd.DataFrame)
    assert result.empty


def test_flatten_discrete_json_with_companies(sample_json_with_companies):
    provider = MockLensDirectMetadataProvider()
    result = flatten_discrete_json(
        sample_json_with_companies, asset_info_getter=asset_info_getter, lens_direct_metadata_provider=provider
    )
    assert len(result) == 1
    assert result[0]["company"] == "Company1"
    assert result[0]["country"] == "Country1"
    assert result[0]["asset_name"] == "Asset1"
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert result[0][col] == f"mock_{col}_Asset1_ID"

def test_flatten_discrete_json_with_body(sample_json_with_countries):
    provider = MockLensDirectMetadataProvider()
    result = flatten_discrete_json(
        sample_json_with_countries, asset_info_getter=asset_info_getter, lens_direct_metadata_provider=provider
    )
    assert len(result) == 1
    assert result[0]["country"] == "Country1"
    assert result[0]["asset_name"] == "Asset1"
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert result[0][col] == f"mock_{col}_Asset1_ID"

def test_flatten_discrete_json_with_output_file(sample_json_with_companies, temp_dir):
    provider = MockLensDirectMetadataProvider()
    output_file = os.path.join(temp_dir, "output.json")
    result = flatten_discrete_json(
        sample_json_with_companies,
        output_file=output_file,
        asset_info_getter=asset_info_getter,
        lens_direct_metadata_provider=provider,
    )
    assert os.path.exists(output_file)

    with open(output_file, "r") as f:
        saved_data = json.load(f)

    assert saved_data == result
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert result[0][col] == f"mock_{col}_Asset1_ID"

def test_flatten_discrete_csv(sample_json_with_companies, temp_dir):
    provider = MockLensDirectMetadataProvider()
    output_file = os.path.join(temp_dir, "output.csv")
    flatten_discrete_csv(
        sample_json_with_companies,
        output_file=output_file,
        asset_info_getter=asset_info_getter,
        lens_direct_metadata_provider=provider,
    )
    assert os.path.exists(output_file)

    df = pd.read_csv(output_file)
    assert len(df) == 1
    assert "company" in df.columns
    assert "country" in df.columns
    assert "asset_name" in df.columns
    assert df.iloc[0]["company"] == "Company1"
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert df.iloc[0][col] == f"mock_{col}_Asset1_ID"

def test_flatten_discrete_parquet(sample_json_with_companies, temp_dir):
    provider = MockLensDirectMetadataProvider()
    output_file = os.path.join(temp_dir, "output.parquet")
    flatten_discrete_parquet(
        sample_json_with_companies,
        output_file=output_file,
        asset_info_getter=asset_info_getter,
        lens_direct_metadata_provider=provider,
    )
    assert os.path.exists(output_file)

    df = pd.read_parquet(output_file)
    assert len(df) == 1
    assert "company" in df.columns
    assert "country" in df.columns
    assert "asset_name" in df.columns
    assert df.iloc[0]["company"] == "Company1"
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert df.iloc[0][col] == f"mock_{col}_Asset1_ID"

def test_flatten_discrete_parquet_replaces_no_production_with_na(
    sample_json_with_companies, temp_dir
):
    sample_json_with_companies["companies"][0]["countries"][0]["businessAreas"][0][
        "regimes"
    ][0]["assets"][0]["valuations"][0]["scenarios"][0]["result"]["reports"][0][
        "discreteMetrics"
    ]["metric3"] = "No Production"

    output_file = os.path.join(temp_dir, "output.parquet")
    flatten_discrete_parquet(
        sample_json_with_companies,
        output_file=output_file,
        asset_info_getter=asset_info_getter,
    )

    df = pd.read_parquet(output_file)

    assert pd.isna(df.iloc[0]["metric3"])


@patch("os.listdir")
@patch("builtins.open", new_callable=mock_open, read_data=json.dumps({"countries": []}))
@patch("asset_json_flattener.flatten_discrete_csv")
def test_flatt_all_json_dir(
    mock_flatten_discrete_csv, mock_file, mock_listdir, temp_dir
):
    mock_listdir.return_value = ["file1.json", "file2.json", "not_json.txt"]

    flatt_all_json_dir(temp_dir, asset_info_getter=asset_info_getter)

    assert mock_file.call_count == 2
    mock_file.assert_any_call("file1.json", "r")
    mock_file.assert_any_call("file2.json", "r")

    assert mock_flatten_discrete_csv.call_count == 2

    assert "not_json.txt" not in [call[0][0] for call in mock_file.call_args_list]


def test_flatt_all_json_dir_integration(temp_dir, sample_json_with_countries):
    json_data = sample_json_with_countries

    with open(os.path.join(temp_dir, "test1.json"), "w") as f:
        json.dump(json_data, f)

    with open(os.path.join(temp_dir, "test2.json"), "w") as f:
        json.dump(json_data, f)

    with open(os.path.join(temp_dir, "not_json.txt"), "w") as f:
        f.write("This is not a JSON file")

    original_dir = os.getcwd()
    os.chdir(temp_dir)

    try:
        flatt_all_json_dir(".", asset_info_getter=asset_info_getter)

        assert os.path.exists("test1.csv")
        assert os.path.exists("test2.csv")
        assert not os.path.exists("not_json.csv")

        df1 = pd.read_csv("test1.csv")
        assert len(df1) > 0
        assert "country" in df1.columns
        assert df1.iloc[0]["country"] == "Country1"
    finally:
        os.chdir(original_dir)


@patch("asset_json_flattener.datetime")
def test_combine_parquet_files_full_output(mock_datetime, temp_dir):
    mock_date = mock_datetime.now.return_value
    mock_date.strftime.return_value = "20230101_000000"

    df1 = pd.DataFrame(
        {
            "country": ["USA", "Canada"],
            "business_area": ["Area1", "Area2"],
            "regime": ["Regime1", "Regime2"],
            "valuation_type": ["Type1", "Type2"],
            "price_scenario": ["Base", "High"],
            "asset_type": ["FIELD", "FIELD"],
            "asset_id": ["Asset1_ID", "Asset2_ID"],
            "asset_name": ["Asset1", "Asset2"],
        }
    )
    df2 = pd.DataFrame(
        {
            "country": ["UK", "Germany"],
            "business_area": ["Area3", "Area4"],
            "regime": ["Regime3", "Regime4"],
            "valuation_type": ["Type3", "Type4"],
            "price_scenario": ["Low", "Base"],
            "asset_type": ["FIELD", "FIELD"],
            "asset_id": ["Asset3_ID", "Asset4_ID"],
            "asset_name": ["Asset3", "Asset4"],
        }
    )

    file1 = os.path.join(temp_dir, "file1.parquet")
    file2 = os.path.join(temp_dir, "file2.parquet")

    df1.to_parquet(file1)
    df2.to_parquet(file2)

    output_dir = os.path.join(temp_dir, "output")
    os.makedirs(output_dir, exist_ok=True)

    original_dir = os.getcwd()
    os.chdir(temp_dir)

    try:
        result = combine_parquet_files([file1, file2], "test_output", True)

        assert os.path.exists(result)
        assert "20230101_000000" in str(result)

        result_df = pd.read_parquet(result)
        assert len(result_df) == 4
    finally:
        os.chdir(original_dir)


def test_combine_parquet_files_no_timestamp(temp_dir):
    df1 = pd.DataFrame(
        {
            "country": ["USA", "Canada"],
            "business_area": ["Area1", "Area2"],
            "regime": ["Regime1", "Regime2"],
            "valuation_type": ["Type1", "Type2"],
            "price_scenario": ["Base", "High"],
            "asset_type": ["FIELD", "FIELD"],
            "asset_id": ["Asset1_ID", "Asset2_ID"],
            "asset_name": ["Asset1", "Asset2"],
        }
    )
    df2 = pd.DataFrame(
        {
            "country": ["UK", "Germany"],
            "business_area": ["Area3", "Area4"],
            "regime": ["Regime3", "Regime4"],
            "valuation_type": ["Type3", "Type4"],
            "price_scenario": ["Low", "Base"],
            "asset_type": ["FIELD", "FIELD"],
            "asset_id": ["Asset3_ID", "Asset4_ID"],
            "asset_name": ["Asset3", "Asset4"],
        }
    )

    file1 = os.path.join(temp_dir, "file1.parquet")
    file2 = os.path.join(temp_dir, "file2.parquet")

    df1.to_parquet(file1)
    df2.to_parquet(file2)

    output_dir = os.path.join(temp_dir, "output")
    os.makedirs(output_dir, exist_ok=True)

    original_dir = os.getcwd()
    os.chdir(temp_dir)

    try:
        result = combine_parquet_files(
            [file1, file2], "test_output", include_timestamp=False
        )

        assert os.path.exists(result)
        expected_path = os.path.join("output", "test_output_combined.parquet")
        assert str(result).endswith(expected_path)
        assert "combined" in str(result)
        assert "20230101" not in str(result)
    finally:
        os.chdir(original_dir)


def test_combine_parquet_files_empty_list():
    with pytest.raises(ValueError, match="No input files provided"):
        combine_parquet_files([], "test_output")


def test_combine_parquet_files_nonexistent_file(temp_dir):
    with pytest.raises(FileNotFoundError):
        combine_parquet_files(
            [os.path.join(temp_dir, "nonexistent.parquet")], "test_output"
        )


def test_extract_value_from_repeat_pattern_dict():
    pattern = {"data": 42, "repeat": 5}
    assert extract_values_from_repeat_pattern(pattern, 0)[0] == 42
    assert extract_values_from_repeat_pattern(pattern, 0)[4] == 42


def test_extract_value_from_repeat_pattern_list():
    pattern = [1, 2, {"data": 3, "repeat": 3}, 4]
    assert extract_values_from_repeat_pattern(pattern, 0)[0] == 1
    assert extract_values_from_repeat_pattern(pattern, 0)[1] == 2
    assert extract_values_from_repeat_pattern(pattern, 0)[2] == 3
    assert extract_values_from_repeat_pattern(pattern, 0)[3] == 3
    assert extract_values_from_repeat_pattern(pattern, 0)[4] == 3
    assert extract_values_from_repeat_pattern(pattern, 0)[5] == 4


def test_extract_value_from_repeat_pattern_invalid():
    with pytest.raises(ValueError):
        extract_values_from_repeat_pattern({}, 0)
    with pytest.raises(ValueError):
        extract_values_from_repeat_pattern({"data": 1}, 0)
    with pytest.raises(ValueError):
        extract_values_from_repeat_pattern({"repeat": 5}, 0)


def test_flatten_timeseries_metrics(timeseries_country_data):
    provider = MockLensDirectMetadataProvider()
    result = flatten_timeseries_metrics(
        timeseries_country_data, asset_info_getter=asset_info_getter, lens_direct_metadata_provider=provider
    )

    assert isinstance(result, pd.DataFrame)
    assert len(result) == 3
    assert list(result["year"]) == [2020, 2021, 2022]
    assert list(result["production"]) == [100, 200, 300]
    assert list(result["revenue"]) == [500, 500, 500]
    assert list(result["opex"]) == [123, 500, 456]
    assert result.iloc[0]["country"] == "Country1"
    assert result.iloc[0]["asset_name"] == "Asset1"
    assert result.iloc[0]["asset_type"] == "FIELD"
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert result.iloc[0][col] == f"mock_{col}_Asset1_ID"


def test_flatten_timeseries_metrics_with_company(timeseries_country_data):
    provider = MockLensDirectMetadataProvider()
    result = flatten_timeseries_metrics(
        timeseries_country_data,
        company_name="TestCompany",
        asset_info_getter=asset_info_getter,
        lens_direct_metadata_provider=provider,
    )

    assert isinstance(result, pd.DataFrame)
    assert len(result) == 3
    assert result.iloc[0]["company"] == "TestCompany"
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert result.iloc[0][col] == f"mock_{col}_Asset1_ID"


def test_flatten_timeseries_metrics_unsupported_asset_type(timeseries_country_data):
    data = deepcopy(timeseries_country_data)

    def unsupported_asset_info_getter(**kwargs):
        return ("UNSUPPORTED_TYPE", None)
    result = flatten_timeseries_metrics(
        data, asset_info_getter=unsupported_asset_info_getter
    )

    assert isinstance(result, pd.DataFrame)
    assert result.empty


def test_flatten_timeseries_json_with_companies(
    timeseries_json_with_companies, temp_dir
):
    provider = MockLensDirectMetadataProvider()
    output_file = os.path.join(temp_dir, "output.json")
    result = flatten_timeseries_json(
        timeseries_json_with_companies,
        output_file=output_file,
        asset_info_getter=asset_info_getter,
        lens_direct_metadata_provider=provider,
    )

    assert os.path.exists(output_file)
    assert len(result) == 3
    assert result[0]["company"] == "Company1"
    assert result[0]["country"] == "Country1"

    with open(output_file, "r") as f:
        saved_data = json.load(f)

    assert len(saved_data) == 3
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert result[0][col] == f"mock_{col}_Asset1_ID"


def test_flatten_timeseries_json_with_body(timeseries_json_with_countries, temp_dir):
    provider = MockLensDirectMetadataProvider()
    output_file = os.path.join(temp_dir, "output.json")
    result = flatten_timeseries_json(
        timeseries_json_with_countries,
        output_file=output_file,
        asset_info_getter=asset_info_getter,
        lens_direct_metadata_provider=provider,
    )

    assert os.path.exists(output_file)
    assert len(result) == 3
    assert result[0]["country"] == "Country1"
    assert "company" not in result[0]
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert result[0][col] == f"mock_{col}_Asset1_ID"


def test_flatten_timeseries_csv(timeseries_json_with_companies, temp_dir):
    provider = MockLensDirectMetadataProvider()
    output_file = os.path.join(temp_dir, "output.csv")
    result = flatten_timeseries_csv(
        timeseries_json_with_companies,
        output_file=output_file,
        asset_info_getter=asset_info_getter,
        lens_direct_metadata_provider=provider,
    )

    assert os.path.exists(output_file)
    assert isinstance(result, pd.DataFrame)
    assert len(result) == 3

    df = pd.read_csv(output_file)
    assert len(df) == 3
    assert "company" in df.columns
    assert "country" in df.columns
    assert "year" in df.columns
    assert df.iloc[0]["company"] == "Company1"
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert df.iloc[0][col] == f"mock_{col}_Asset1_ID"

def test_flatten_timeseries_parquet(timeseries_json_with_companies, temp_dir):
    provider = MockLensDirectMetadataProvider()
    output_file = os.path.join(temp_dir, "output.parquet")
    result = flatten_timeseries_parquet(
        timeseries_json_with_companies,
        output_file=output_file,
        asset_info_getter=asset_info_getter,
        lens_direct_metadata_provider=provider,
    )

    assert os.path.exists(output_file)
    assert isinstance(result, pd.DataFrame)
    assert len(result) == 3

    df = pd.read_parquet(output_file)
    assert "company" in df.columns
    assert "country" in df.columns
    assert "year" in df.columns
    assert list(df["year"]) == [2020, 2021, 2022]
    assert list(df["production"]) == [100, 200, 300]
    assert list(df["revenue"]) == [500, 500, 500]
    for col in LensDirectMetadataProvider.FIELD_COLUMN_MAPPING.keys():
        assert df.iloc[0][col] == f"mock_{col}_Asset1_ID"

def test_flatten_timeseries_parquet_replaces_no_production(
    timeseries_json_with_companies, temp_dir
):
    data = deepcopy(timeseries_json_with_companies)
    data["companies"][0]["countries"][0]["businessAreas"][0]["regimes"][0]["assets"][0][
        "valuations"
    ][0]["scenarios"][0]["result"]["reports"][0]["continuousMetrics"]["Production"][
        1
    ] = "No Production"

    output_file = os.path.join(temp_dir, "output.parquet")
    flatten_timeseries_parquet(
        data, output_file=output_file, asset_info_getter=asset_info_getter
    )

    df = pd.read_parquet(output_file)
    assert pd.isna(df.iloc[1]["production"])
    assert not pd.isna(df.iloc[0]["production"])
    assert not pd.isna(df.iloc[2]["production"])

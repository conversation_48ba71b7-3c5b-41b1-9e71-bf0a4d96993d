interface Message {
    content: string;
    role: 'user' | 'assistant';
}

function validateMessages(messages: Message[]): {
    valid: boolean;
    error?: string;
} {
    for (const message of messages) {
        if (
            !message.content ||
            typeof message.content !== 'string' ||
            message.content.trim() === ''
        ) {
            return {
                valid: false,
                error: 'Each message must have non-empty content',
            };
        }

        if (!message.role || !['user', 'assistant'].includes(message.role)) {
            return {
                valid: false,
                error: 'Each message must have a role of either "user" or "assistant"',
            };
        }
    }
    for (let i = 1; i < messages.length; i++) {
        if (messages[i].role === messages[i - 1].role) {
            return {
                valid: false,
                error: 'Cannot have consecutive messages with the same role',
            };
        }
    }
    return { valid: true };
};

export type { Message };
export { validateMessages };
#!/usr/bin/env groovy
@Library('utils') _
def sys = "NA"

AWS_REGION = 'us-east-1'
BUILD_ENVIRONMENT = woodmac.getJenkinsEnvironment()

String getEnvironment() {
    if (params.TARGET_ENVIRONMENT != null) {
        return params.TARGET_ENVIRONMENT
    }
    if (woodmac.getJenkinsEnvironment() == 'prod') {
        return 'int'
    }

    return 'dev'
}

def getAvailableEnvironments() {
    if (woodmac.getJenkinsEnvironment() == 'dev') {
        return ['dev']
    }
    return ['int', 'uat', 'prod']
}

environmentName = getEnvironment()
jenkinsAgent = "harbor.prod.woodmac.com/liveservices/jenkins-node-jnlp-cloudformation:3283.v92c105e0f819-1-alpine-jdk21-aws2"

properties([
    parameters([
        choice(name: 'TARGET_ENVIRONMENT', description: 'Environment to deploy', choices: getAvailableEnvironments()),
    ])
])

pipeline {
    agent none
    options {
        disableConcurrentBuilds()
        ansiColor('xterm')
    }
    stages {
        stage('Deploy roles') {
            agent {
                label "dynamicRoleBuilder-${AWS_REGION}-${params.TARGET_ENVIRONMENT}"
            }
            environment {
                AWS_ENVIRONMENT = getEnvironment()
                STACK_NAME = "jenkins-ecs-role-wmlgv-assistant-agent-${getEnvironment()}"
                TEMPLATE_FILE = "../infra/deployment-role.yaml"
            }
            steps {
                dir('jenkins') {
                    sh 'make lint'
                    sh 'make deploy-role'
                }
            }
        }
    }
}

import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { Agent } from "../Agent";
import { ChatMessage } from "../chat-types";

jest.mock("../BotMessage", () => ({
  BotMessage: ({ message, loading, hideAiWarning, tableData, footerComponents }: any) => (
    <div data-testid="bot-message">
      <span data-testid="bot-message-text">{message}</span>
      {loading && <span data-testid="loading">Loading...</span>}
      {hideAiWarning && <span data-testid="hide-ai-warning">No AI Warning</span>}
      {tableData && <span data-testid="table-data">Table Data</span>}
      {footerComponents && <span data-testid="footer-components">Footer Components</span>}
    </div>
  ),
}));

jest.mock("../UserMessage", () => ({
  UserMessage: ({ message }: any) => (
    <div data-testid="user-message">
      <span data-testid="user-message-text">{message}</span>
    </div>
  ),
}));

jest.mock("../SuggestedQuestions", () => ({
  SuggestedQuestions: ({ showSuggestedQuestions, suggestedQuestions, handleChatMessageEntered }: any) => (
    <div data-testid="suggested-questions">
      {showSuggestedQuestions && (
        <div>
          {suggestedQuestions?.map((question: string, index: number) => (
            <button
              key={index}
              data-testid={`suggested-question-${index}`}
              onClick={() => handleChatMessageEntered(question)}
            >
              {question}
            </button>
          ))}
        </div>
      )}
    </div>
  ),
}));

jest.mock("../ChatEntry", () => ({
  ChatEntry: ({ onChatMessageEntered }: any) => (
    <div data-testid="chat-entry">
      <input
        data-testid="chat-input"
        placeholder="Type your message"
        onKeyDown={(e) => {
          if (e.key === "Enter") {
            onChatMessageEntered((e.target as HTMLInputElement).value);
          }
        }}
      />
    </div>
  ),
}));

jest.mock("../ScrollToBottom", () => ({
  ScrollToBottom: ({ trigger }: any) => (
    <div data-testid="scroll-to-bottom" data-trigger={trigger} />
  ),
}));

jest.mock("../use-styles", () => ({
  useStyles: () => ({
    messageArea: "mock-message-area-class",
  }),
}));

describe("Agent", () => {
  const defaultProps = {
    howCanIHelpMessage: "How can I help you today?",
    chatHistory: [] as ChatMessage[],
    loading: false,
    handleChatMessageEntered: jest.fn(),
    botMessageFooterComponentsCreator: jest.fn(() => []),
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("renders the initial bot message", () => {
    render(<Agent {...defaultProps} />);

    expect(screen.getByTestId("bot-message")).toBeInTheDocument();
    expect(screen.getByTestId("bot-message-text")).toHaveTextContent("How can I help you today?");
    expect(screen.getByTestId("hide-ai-warning")).toBeInTheDocument();
  });

  it("renders suggested questions when enabled", () => {
    const props = {
      ...defaultProps,
      showSuggestedQuestions: true,
      suggestedQuestions: ["What is your purpose?", "How do you work?"],
    };

    render(<Agent {...props} />);

    expect(screen.getByTestId("suggested-questions")).toBeInTheDocument();
    expect(screen.getByTestId("suggested-question-0")).toHaveTextContent("What is your purpose?");
    expect(screen.getByTestId("suggested-question-1")).toHaveTextContent("How do you work?");
  });

  it("does not render suggested questions when disabled", () => {
    const props = {
      ...defaultProps,
      showSuggestedQuestions: false,
      suggestedQuestions: ["What is your purpose?"],
    };

    render(<Agent {...props} />);

    expect(screen.getByTestId("suggested-questions")).toBeInTheDocument();
    expect(screen.queryByTestId("suggested-question-0")).not.toBeInTheDocument();
  });

  it("renders chat history messages correctly", () => {
    const chatHistory: ChatMessage[] = [
      { role: "user", message: "Hello" },
      { role: "assistant", message: "Hi there!", additionalData: "some data" },
      { role: "user", message: "How are you?" },
    ];

    const props = {
      ...defaultProps,
      chatHistory,
    };

    render(<Agent {...props} />);

    const userMessages = screen.getAllByTestId("user-message");
    const botMessages = screen.getAllByTestId("bot-message");

    expect(userMessages).toHaveLength(2);
    expect(botMessages).toHaveLength(2); // Initial message + assistant response

    expect(userMessages[0]).toHaveTextContent("Hello");
    expect(userMessages[1]).toHaveTextContent("How are you?");
    expect(botMessages[1]).toHaveTextContent("Hi there!");
  });

  it("renders assistant messages with table data", () => {
    const chatHistory: ChatMessage[] = [
      {
        role: "assistant",
        message: "Here is your data",
        additionalData: [
          ["A", "B"],
          ["C", "D"],
        ],
      },
    ];

    const props = {
      ...defaultProps,
      chatHistory,
    };

    render(<Agent {...props} />);

    expect(screen.getByTestId("table-data")).toBeInTheDocument();
  });

  it("renders assistant messages with footer components", () => {
    const mockFooterComponentsCreator = jest.fn(() => [<div key="footer">Footer</div>]);
    const chatHistory: ChatMessage[] = [
      { role: "assistant", message: "Response with footer", additionalData: "footer data" },
    ];

    const props = {
      ...defaultProps,
      chatHistory,
      botMessageFooterComponentsCreator: mockFooterComponentsCreator,
    };

    render(<Agent {...props} />);

    expect(mockFooterComponentsCreator).toHaveBeenCalledWith("footer data");
    expect(screen.getByTestId("footer-components")).toBeInTheDocument();
  });

  it("shows loading state with current message", () => {
    const props = {
      ...defaultProps,
      loading: true,
      currentMessage: "Thinking...",
    };

    render(<Agent {...props} />);

    const botMessages = screen.getAllByTestId("bot-message");
    expect(botMessages).toHaveLength(2); // Initial message + loading message

    const loadingMessage = botMessages[1];
    expect(loadingMessage).toHaveTextContent("Thinking...");
    expect(screen.getByTestId("loading")).toBeInTheDocument();
  });

  it("does not show loading state when not loading", () => {
    const props = {
      ...defaultProps,
      loading: false,
      currentMessage: "This should not appear",
    };

    render(<Agent {...props} />);

    expect(screen.queryByTestId("loading")).not.toBeInTheDocument();
    const botMessages = screen.getAllByTestId("bot-message");
    expect(botMessages).toHaveLength(1); // Only initial message
  });

  it("renders chat entry component", () => {
    render(<Agent {...defaultProps} />);

    expect(screen.getByTestId("chat-entry")).toBeInTheDocument();
    expect(screen.getByTestId("chat-input")).toBeInTheDocument();
  });

  it("renders scroll to bottom component with correct trigger", () => {
    const chatHistory: ChatMessage[] = [
      { role: "user", message: "Test 1" },
      { role: "assistant", message: "Response 1" },
    ];

    const props = {
      ...defaultProps,
      chatHistory,
      currentMessage: chatHistory[1].message,
    };

    render(<Agent {...props} />);

    const scrollComponent = screen.getByTestId("scroll-to-bottom");
    screen.debug();
    expect(scrollComponent).toBeInTheDocument();
    expect(scrollComponent).toHaveAttribute("data-trigger", "2_Response 1");
  });

  it("handles chat message entry through ChatEntry", async () => {
    const user = userEvent.setup();
    const mockHandleChatMessageEntered = jest.fn();

    const props = {
      ...defaultProps,
      handleChatMessageEntered: mockHandleChatMessageEntered,
    };

    render(<Agent {...props} />);

    const chatInput = screen.getByTestId("chat-input");
    await user.type(chatInput, "Test message");
    await user.keyboard("{Enter}");

    expect(mockHandleChatMessageEntered).toHaveBeenCalledWith("Test message");
  });

  it("handles suggested question clicks", async () => {
    const user = userEvent.setup();
    const mockHandleChatMessageEntered = jest.fn();

    const props = {
      ...defaultProps,
      showSuggestedQuestions: true,
      suggestedQuestions: ["What can you do?"],
      handleChatMessageEntered: mockHandleChatMessageEntered,
    };

    render(<Agent {...props} />);

    const suggestedQuestion = screen.getByTestId("suggested-question-0");
    await user.click(suggestedQuestion);

    expect(mockHandleChatMessageEntered).toHaveBeenCalledWith("What can you do?");
  });

  it("applies correct CSS classes", () => {
    render(<Agent {...defaultProps} />);

    const messageArea = screen.getByTestId("bot-message").parentElement;
    expect(messageArea).toHaveClass("mock-message-area-class");
  });

  it("handles empty chat history", () => {
    render(<Agent {...defaultProps} />);

    expect(screen.getAllByTestId("bot-message")).toHaveLength(1); // Only initial message
    expect(screen.queryByTestId("user-message")).not.toBeInTheDocument();
  });

  it("handles mixed message types in chat history", () => {
    const chatHistory: ChatMessage[] = [
      { role: "user", message: "Question 1" },
      { role: "assistant", message: "Answer 1", additionalData: "string data" },
      { role: "user", message: "Question 2" },
      { role: "assistant", message: "Answer 2", additionalData: [[123]] },
    ];

    const props = {
      ...defaultProps,
      chatHistory,
    };

    render(<Agent {...props} />);

    expect(screen.getAllByTestId("user-message")).toHaveLength(2);
    expect(screen.getAllByTestId("bot-message")).toHaveLength(3); // Initial + 2 assistant responses
  });

   it("renders header components when passed as prop", () => {
    const props = {
      ...defaultProps,
      header: <div data-testid="header-component">Header Content</div>,
    };

    render(<Agent {...props} />);
    expect(screen.getByTestId("header-component")).toBeInTheDocument();
  });
});

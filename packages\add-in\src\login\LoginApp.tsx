import React from "react";
import { <PERSON>, BrowserRouter as Router, Switch, } from "react-router-dom";
import { Security } from "@okta/okta-react";
import { LoginWithOkta } from "./LoginWithOkta";
import { oktaClient } from "../security";
import { Callback } from "./Callback";

const LoginApp = () => (
        <Router>
            <Security oktaAuth={oktaClient} restoreOriginalUri={() => {}}>
                <Switch>
                    <Route path="/login/callback" component={Callback} />
                    {/* need the additional route whilst deploying to IM infrastructure */}
                    <Route path="/wmlgv-assistant/login/callback" component={Callback} />
                    <Route path="/" component={LoginWithOkta} />
                </Switch>
            </Security>
        </Router>
    );

export { LoginApp };
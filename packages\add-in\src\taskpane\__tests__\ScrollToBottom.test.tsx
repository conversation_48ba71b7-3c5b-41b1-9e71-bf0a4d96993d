import React from "react";
import { render } from "@testing-library/react";
import { ScrollToBottom } from "../ScrollToBottom";

describe("ScrollToBottom", () => {
  let mockScrollIntoView: jest.Mock;

  beforeEach(() => {
    mockScrollIntoView = jest.fn();
    Element.prototype.scrollIntoView = mockScrollIntoView;
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it("renders without crashing", () => {
    const { container } = render(<ScrollToBottom trigger={0} />);
    expect(container.firstChild).toBeInTheDocument();
  });

  it("calls scrollIntoView when trigger changes", () => {
    const { rerender } = render(<ScrollToBottom trigger={0} />);

    rerender(<ScrollToBottom trigger={1} />);

    expect(mockScrollIntoView).toHaveBeenCalledWith({ behavior: "smooth" });
  });

  it("calls scrollIntoView multiple times when trigger changes multiple times", () => {
    const { rerender } = render(<ScrollToBottom trigger={0} />);

    rerender(<ScrollToBottom trigger={1} />);
    rerender(<ScrollToBottom trigger={2} />);
    rerender(<ScrollToBottom trigger={3} />);

    expect(mockScrollIntoView).toHaveBeenCalledTimes(4);
    expect(mockScrollIntoView).toHaveBeenCalledWith({ behavior: "smooth" });
  });

  it("does not call scrollIntoView when trigger stays the same", () => {
    mockScrollIntoView.mockClear();
    const { rerender } = render(<ScrollToBottom trigger={5} />);

    mockScrollIntoView.mockClear();
    rerender(<ScrollToBottom trigger={5} />);

    expect(mockScrollIntoView).not.toHaveBeenCalled();
  });

  it("handles different trigger types", () => {
    const { rerender } = render(<ScrollToBottom trigger="initial" />);

    rerender(<ScrollToBottom trigger="changed" />);
    rerender(<ScrollToBottom trigger={true} />);
    rerender(<ScrollToBottom trigger={{ value: "object" }} />);

    expect(mockScrollIntoView).toHaveBeenCalledTimes(4);
  });
});

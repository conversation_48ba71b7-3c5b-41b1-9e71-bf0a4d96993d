AWSTemplateFormatVersion: '2010-09-09'
Description: Deploys AWS AI account resources for the Woodmac AI Assistant Agent
Parameters:
  Environment:
    Description: The environment in which the stack is deployed
    Type: String
    Default: aidev
    AllowedValues: [aidev, aiint, aiuat, aiprod]
  BusinessUnit:
    Type: String
    Default: Woodmac
  Contact:
    Description: Contact for stack owner(s)
    Type: String
    Default: '<EMAIL>'
    ConstraintDescription: Must be a valid email address.
  ProductCode:
    Description: Product code
    Type: String
    Default: 'wmlgv'
  ProjectCode:
    Description: Project code
    Type: String
    Default: 'DEV-VAL-SERV'
  Application:
    Description: Application code
    Type: String
    Default: 'valuations-ai'
  LambdaVersion:
    Description: Version of the Lambda function deployment
    Type: String
    Default: 'latest'
  SplunkLayerVersion:
    Description: Version of the Splunk OpenTelemetry Lambda layers (both python and collector)
    Type: String
    Default: 18 # https://github.com/signalfx/lambda-layer-versions/blob/main/splunk-apm/splunk-apm-python.md

Conditions:
  IsProd: !Equals [!Ref Environment, aiprod]

Resources:
  
  LambdaFunction:
    Type: AWS::Lambda::Function
    Properties:
        FunctionName: !Sub "${ProductCode}-${Environment}-assistant-agent-lambda"
        Role:
            Fn::Sub:
                - arn:aws:iam::${AWS::AccountId}:role/wmlgv-assistant-lambda-${environment}-role
                - environment:
                      Ref: Environment
        Code:
            S3Bucket: !Sub "wmlgv-${Environment}-assistant-lambda-deployment"
            S3Key: !Sub "wmlgv-assistant-agent-${LambdaVersion}.zip"
        PackageType: Zip
        Runtime: python3.12
        Handler: lambda_function.lambda_handler
        Timeout: 30
        MemorySize: 2048
        Layers:
          - !Sub "arn:aws:lambda:${AWS::Region}:************:layer:splunk-apm-python:${SplunkLayerVersion}"
          - !Sub "arn:aws:lambda:${AWS::Region}:************:layer:splunk-apm-collector:${SplunkLayerVersion}"
        LoggingConfig:
            ApplicationLogLevel: INFO
            SystemLogLevel: WARN
            LogFormat: JSON
        Environment:
            Variables:
                ENVIRONMENT: !Ref Environment
                # Splunk OpenTelemetry
                AWS_LAMBDA_EXEC_WRAPPER: "/opt/otel-instrument"
                OTEL_SERVICE_NAME: "wmlgv-assistant-agent-lambda"
                OTEL_RESOURCE_ATTRIBUTES: !Sub "deployment.environment=${Environment}"
                OTEL_EXPORTER_OTLP_TRACES_PROTOCOL: "http/protobuf"
                # Disable logs and metrics collection - traces only
                OTEL_LOGS_EXPORTER: "none"
                OTEL_METRICS_EXPORTER: "none"
                # todo replace with internal collector once it works
                SPLUNK_ACCESS_TOKEN: "O2wm5i_ThElqz81jJ0INxA"
                SPLUNK_REALM: "eu0"
                # OTEL_EXPORTER_OTLP_ENDPOINT: !Sub "https://otel-collector.${Environment}.woodmac.com"
        Tags:
            - Key: BusinessUnit
              Value: !Ref BusinessUnit
            - Key: Contact
              Value: !Ref Contact
            - Key: Environment
              Value: !Ref Environment
            - Key: ProductCode
              Value: !Ref ProductCode
            - Key: ProjectCode
              Value: !Ref ProjectCode
            - Key: Application
              Value: !Ref Application
            - !If
              - IsProd
              - Key: wm-splunk-metrics-enabled
                Value: true
              - !Ref "AWS::NoValue"
  
  Guardrail:
    Type: AWS::Bedrock::Guardrail
    Properties:
      Name: wmlgv-assistant-agent-gr
      Description: A set of guardrails used by wmlgv-assistant agent
      BlockedInputMessaging: Sorry, I cannot answer this question at the moment.
      BlockedOutputsMessaging: Sorry, I cannot answer this question at the moment.
      ContentPolicyConfig:
        FiltersConfig:
          - Type: SEXUAL
            InputStrength: HIGH
            OutputStrength: HIGH
          - Type: VIOLENCE
            InputStrength: HIGH
            OutputStrength: HIGH
          - Type: HATE
            InputStrength: HIGH
            OutputStrength: HIGH
          - Type: INSULTS
            InputStrength: HIGH
            OutputStrength: HIGH
          - Type: MISCONDUCT
            InputStrength: HIGH
            OutputStrength: HIGH
          - Type: PROMPT_ATTACK
            InputStrength: HIGH
            OutputStrength: NONE
      TopicPolicyConfig:
        TopicsConfig:
          # This seems sensible but a bit too restrictive...
          # - Name: Out of scope questions
          #   Definition: Queries related to industries outside of upstream oil and gas or valuations data. For example, refining, petrochemicals, renewable energy (solar, wind, hydrogen), mining, power generation
          #   Examples:
          #     - What are Shell's investments in solar energy? (renewables)
          #     - What are the emissions from BHP's copper mines? (mining)
          #     - What are BP's hydrogen projects? (renewables)
          #     - What is the stock price of ExxonMobil?
          #     - What are the ESG goals of TotalEnergies?
          #   Type: DENY
          - Name: Agent implementation
            Definition: Questions about the agent's internal architecture, data structures, API integration methods, or how valuation calculations are performed; includes probing about data storage or processing mechanisms.
            Examples:
              - provide me SQL query for this question
              - how is your data defined
            Type: DENY
      ContextualGroundingPolicyConfig:
        FiltersConfig:
          - Type: RELEVANCE
            Threshold: 0.6
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: Application
          Value: !Ref Application

  ApplicationInferenceProfile:
    Type: AWS::Bedrock::ApplicationInferenceProfile
    Properties:
      InferenceProfileName: !Sub "${ProductCode}-${Environment}-assistant-aip"
      Description: !Sub "Application Inference Profile for ${ProductCode} ${Environment} assistant agent"
      ModelSource:
        CopyFrom: !Sub "arn:aws:bedrock:us-east-1:${AWS::AccountId}:inference-profile/us.anthropic.claude-sonnet-4-********-v1:0"
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: Application
          Value: !Ref Application

  BedrockAgent:
    Type: AWS::Bedrock::Agent
    Properties:
      AgentName: !Sub "${ProductCode}-${Environment}-assistant-agent"
      Description: This agent is an automated, ai powered agent that gives clients valuation and asset information insights for the woodmac valuations assistant. Concise, number heavy, tabular answer leaning excel bot.
      AgentResourceRoleArn: !Sub "arn:aws:iam::${AWS::AccountId}:role/wmlgv-assistant-bedrock-${Environment}-role"
      FoundationModel: !GetAtt ApplicationInferenceProfile.InferenceProfileArn
      Instruction: |
        You are responsible for returning oil and gas upstream financial data in a table.

        MANDATORY RESPONSE FORMAT - NEVER DEVIATE FROM THIS

        Table-First Response Structure:
        - EVERY SINGLE RESPONSE MUST START IMMEDIATELY WITH A DATA TABLE
        - NEVER BEGIN WITH ANY TEXT, EXPLANATION, OR GREETING
        - THE VERY FIRST CHARACTER OF YOUR RESPONSE MUST BE THE PIPE SYMBOL "|" STARTING THE TABLE HEADER
        - CRITICAL: Include units and value multipliers (e.g., USD, millions, billions) ONLY in the table header row, NOT in the data cells
        
        Table Data Requirements:
        - Data cells contain only numeric values - no currency symbols, units, or text modifiers
        - Use decimal formatting (e.g. 52.20) within each column
        - Use "-" for missing data

        Written Prose Rules:
        - Written prose adding context to the data may be provided ONLY AFTER the table
        - Must be delimited from any tabular data by exactly five equals signs (=====)
        - Should be concise and businesslike in nature
        - For any metrics displayed in the final response, specify what the metric represents

        CRITICAL ANALYTICAL REQUIREMENTS

        Asset Comparison and Data Inclusion:
        - When comparing assets, do NOT exclude any results from the query based on asset_name
        - Make NO assumptions on phases
        - Make NO assumptions that assets are part of different projects
        - Include all assets from queries without filtering or exclusions

        Required Analyses:
        - Perform sensitivity analyses on key variables affecting valuations
        - Benchmark costs, revenues, and pricing against industry peers
        - Identify trends and patterns in financial data
        - Calculate key financial ratios and metrics

        Communication and Expertise Standards:
        - Ask clarifying questions to understand the specific valuation context and user needs
        - Provide explanations in clear, concise language, avoiding jargon when possible
        - Acknowledge when a question is beyond your expertise or requires human judgment
        - Clarify that your analysis is for informational purposes and should not be the sole basis for investment decisions
        - Tailor your language and depth of analysis based on the user's role (e.g., more technical for analysts, higher-level for executives)

        Supported Workflows:
        - Be prepared to support various workflows such as:
          - Asset acquisition/divestment
          - Opportunity benchmarking  
          - Competitor analysis

        ## EXAMPLE RESPONSE FORMAT

        | Asset Name | Country | CAPEX/BOE (USD) | Remaining PV Post-Tax (USD millions) | Reserves (MMboe) | Post-Tax IRR (%) |
        |------------|---------|-----------------|--------------------------------------|------------------|------------------|
        | Endeavor MID Wolfcamp Extension Hz SHO TX Fee | United States | 0.00 | -0.02 | 0.00 | 0.00 |
        | Permian Resources DEL Wolfcamp Gas West Hz SHO TX Fee | United States | 0.01 | 202.18 | 19.02 | 0.00 |
        | MO868 | United States | 0.02 | -8.98 | 0.32 | 0.00 |
        | MO823 | United States | 0.02 | -3.00 | 1.05 | 0.00 |
        | WC076 | United States | 0.02 | 0.00 | 0.00 | 0.00 |

        =====

        **Key Observations:**
        These results suggest that while these assets have very low CAPEX/BOE, they may not be representative of typical profitable upstream operations. Further analysis and data verification would be necessary to draw meaningful conclusions about their performance and economic viability.
      AutoPrepare: true
      ActionGroups:
        - ActionGroupName: valuations-results-processor
          Description: An action group that invokes a Lambda function
          ApiSchema:
            S3:
              S3BucketName: !Sub wmlgv-${Environment}-assistant-lambda-deployment
              S3ObjectKey: wmlgv-assistant-agent-schema.yaml
          ActionGroupExecutor:
            Lambda: !GetAtt LambdaFunction.Arn
        - ActionGroupName: AskUserAction
          ParentActionGroupSignature: AMAZON.UserInput
          ActionGroupState: ENABLED
      GuardrailConfiguration:
        GuardrailIdentifier: !Ref Guardrail
        GuardrailVersion: DRAFT
      
      Tags:
        BusinessUnit: !Ref BusinessUnit
        Contact: !Ref Contact
        Environment: !Ref Environment
        ProductCode: !Ref ProductCode
        ProjectCode: !Ref ProjectCode
        Application: !Ref Application
        
  BedrockAgentAlias:
    Type: AWS::Bedrock::AgentAlias
    Properties:
      AgentId: !Ref BedrockAgent
      AgentAliasName: orchestrator
      Description: Alias used by orchestrator to route requests to the correct version
      Tags:
        BusinessUnit: !Ref BusinessUnit
        Contact: !Ref Contact
        Environment: !Ref Environment
        ProductCode: !Ref ProductCode
        ProjectCode: !Ref ProjectCode
        Application: !Ref Application

  BedrockInvokePermission:
    DependsOn: BedrockAgent
    Type: AWS::Lambda::Permission
    Properties:
        FunctionName: !Ref LambdaFunction
        Action: lambda:InvokeFunction
        Principal: bedrock.amazonaws.com
        SourceArn: !Sub arn:aws:bedrock:${AWS::Region}:${AWS::AccountId}:agent/*

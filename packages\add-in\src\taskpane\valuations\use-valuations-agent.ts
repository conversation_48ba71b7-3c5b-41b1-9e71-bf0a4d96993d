import { useState } from "react";
import { useFlags } from "launchdarkly-react-client-sdk";
import { v4 as uuidv4 } from "uuid";
import { askBot } from "./requests";
import { ChatMessage, TableData } from "../chat-types";
import { useAvailableAliases } from "./use-agent-aliases";

const columnDelimiter = "|";
const removeColumnsWithNoData = (row: string) => row.split(columnDelimiter).slice(1).slice(0, -1)
const removeRowWithNoData = (_: string, index: number) => index !== 1;
const onlyIncludeRows = (row: string) => row.startsWith(columnDelimiter);

const toRowsAndColumns = (table: string) => {
    const rows = table
        .split("\n")
        .filter(onlyIncludeRows)
        .filter(removeRowWithNoData);

    const rowsAndColumns = rows.map(removeColumnsWithNoData); 
    return rowsAndColumns;
};

const suggestedQuestions = [
    "What is the value of the Thunder Horse field?",
    "Benchmark the top five assets in North America by IRR, identifying those with the lowest capex/boe.",
    "Which field in Norway has the highest Remaining NPV?",
    "Compare BP and Shell under Base and Low pricing",
    "Which countries have the highest average post-tax IRR for their assets?",
];

const useValuationsAgent = () => {
    const { suggestQuestions } = useFlags();
    const [ sessionId, setSessionId ] = useState(uuidv4());
    const { availableAliases, selectedAlias, onSetSelectedAlias } = useAvailableAliases(() => setSessionId(uuidv4()));

    const addTableToExcel = async (rowsAndColumns: TableData, createChartFromData?: boolean) => await Excel.run(async (context) => {
        const tableSheet = context.workbook.worksheets.add();
        tableSheet.activate();
        const firstCell = tableSheet.getRange("A1");
        firstCell.getAbsoluteResizedRange(rowsAndColumns.length, rowsAndColumns[0].length).values = rowsAndColumns;
        tableSheet.getUsedRange().format.autofitColumns();
        const table = tableSheet.tables.add(tableSheet.getUsedRange(), true);

        if (createChartFromData) {
            const chart = tableSheet.charts.add(Excel.ChartType.columnClustered, table.getRange(), Excel.ChartSeriesBy.auto);
            const tableRange = table.getRange();
            const rowBelowRange = tableRange.getRowsBelow(1).getAbsoluteResizedRange(1, 1).getOffsetRange(1, 0);
            chart.setPosition(rowBelowRange);
        }
        
        await context.sync();
    });

    const [loading, setLoading] = useState(false);
    const [chatHistory, setChatHistory] = useState<ChatMessage[]>([]);

    const handleChatMessageEntered = async (message: string) => {

        if (message.trim()) {
            setLoading(true);
            setChatHistory(curr => [...curr, {
                role: "user",
                message,
            }]);

            try {
                const { answer, table } = await askBot(message, sessionId, selectedAlias?.agentAliasId);
                console.log({ answer, table });

                setChatHistory(curr => [...curr, {
                    role: "assistant",
                    message: answer
                        ? answer
                        : table
                            ? "I have found a table of data for you"
                            : "Sorry, I don't have an answer for you",
                    ...(table && {
                        additionalData: toRowsAndColumns(table),
                    }),
                }]);

            } catch (err) {
                console.error(err);

                setChatHistory(curr => [...curr, {
                    role: "assistant",
                    message: "Sorry, something has gone wrong. Please ask me something else",
                }]);
            }

            setLoading(false);
        }
    };

    return {
        addTableToExcel,
        chatHistory,
        loading,
        handleChatMessageEntered,
        suggestedQuestions,
        showSuggestedQuestions: suggestQuestions && chatHistory.length === 0 && !loading,
        onSetSelectedAlias,
        availableAliases,
        selectedAlias,
    };
};

export { useValuationsAgent };


from sentence_transformers import SentenceTransformer


DEFAULT_MODEL = "all-MiniLM-L6-v2"


def _get_model(model_name: str = DEFAULT_MODEL):
    if not hasattr(_get_model, "models"):
        _get_model.models = {}
    if model_name not in _get_model.models:
        _get_model.models[model_name] = SentenceTransformer(model_name)
    return _get_model.models[model_name]


def find_semantic_similarity(
    model_output: str,
    expected_output: str,
    model_name: str = DEFAULT_MODEL,
):
    model = _get_model(model_name)
    embeddings_in = model.encode(model_output)
    embeddings_expected = model.encode(expected_output)
    similarities = model.similarity(embeddings_in, embeddings_expected)
    similarity_score = similarities.item()

    return similarity_score

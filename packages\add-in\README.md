# WoodMac Assistant Excel Add-in

This package contains a Microsoft Office Add-in that integrates the WoodMac GV Assistant with Excel, allowing users to interact with valuation data directly in their spreadsheets.

## Features

- Excel integration for data analysis
- Authentication with Okta
- Interactive UI with Fluent UI components
- Data querying and visualization capabilities

## Local Development

### Prerequisites

- Node.js v20 or higher
- npm
- Microsoft Excel (desktop or web version)

### Setup

1. Install dependencies:

```bash
npm install
```

2. Configure your environment:

Create a `.env` file with:

```
OKTA_CLIENT_ID=your-client-id
OKTA_ISSUER=https://your-okta-domain.okta.com
API_BASE_URL=https://your-api-url
```

3. Start the development server:

```bash
npm run dev-server
```

4. Sideload the add-in:

For desktop Excel:

```bash
npm run start:desktop
```

For Excel on the web:

```bash
npm run start:web
```

## Building for Production

```bash
npm run build
```

The built files will be in the `dist` directory.

## Testing

You can validate the manifest file with:

```bash
npm run validate
```

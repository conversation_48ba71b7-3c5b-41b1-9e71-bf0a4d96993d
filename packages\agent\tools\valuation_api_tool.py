import requests
from pydantic import BaseModel, Field

VALUATIONS_BASE_URL = "https://wmlgv-data-repository.dev.woodmac.com"
API_KEY = "test"

"""
title: Valuations API Tool
author: <PERSON>
author_url: https://woodmac.com
description: This tool allows to make requests to the Valuations API to fetch valuable assets, run valuation models and return results.
required_open_webui_version: 0.4.0
requirements: langchain-openai, langgraph, ollama, langchain_ollama
version: 0.0.1
"""


class Tools:
    class Valves(BaseModel):
        api_key: str = Field(
            default=API_KEY,
            description="Your API key here",
        )
        valuations_api_base_url: str = Field(
            default=VALUATIONS_BASE_URL,
            description="Valuations API base url",
        )

    class UserValves(BaseModel):
        api_key: str = Field(
            default=API_KEY,
            description="Your API key here",
        )
        valuations_api_base_url: str = Field(
            default=VALUATIONS_BASE_URL,
            description="Valuations API base url",
        )

    def __init__(self):
        self.assets = {}
        self.valves = self.Valves()
        pass

    def fetch_valuable_assets(
        self,
        commodity_type="upstream",
        country_name=None,
        full_list=False,
        regime=None,
        company=None,
        version=None,
    ):
        """
        Fetches valuable assets from the Valuations API.

        Args:
            commodity_type (str, optional): The type of commodity to fetch assets for. Defaults to "upstream".
            country_name (str): The country to filter assets by. Defaults to None. Example: United Kingdom
            regime (str, optional): The regime to filter assets by. Defaults to None.
            company (str, optional): The company to filter assets by. Defaults to None.
            version (str, optional): The version to filter assets by. Defaults to None.

        Returns:
            list: A list of valuable assets if the request is successful.

        Raises:
            HTTPError: If the request to the API fails.
        """
        print(
            f"Fetching valuable assets for {country_name} with full_list={full_list}..."
        )
        url = f"{self.valves.valuations_api_base_url}/api/v1/Assets/{commodity_type}"
        params = {
            "Country": country_name,
            "Regime": regime,
            "Company": company,
            "Version": version,
        }
        headers = {"x-api-key": self.valves.api_key, "x-consumer-username": "edvinas"}
        response = requests.get(url, headers=headers, params=params)

        if response.status_code == 200:
            body = response.json()
            # Process the assets to extract valuable ones
            self.assets = body["assets"]
            if full_list:
                limited_assets = self.assets
                return limited_assets
            else:
                limited_assets = (
                    self.assets[:10] if len(self.assets) > 10 else self.assets
                )
            return f"The valuable assets in {country_name} are {str(limited_assets)}, total number of assets: {len(self.assets)}"
        else:
            "Could not fetch valuable assets for this request"

    def fetch_valuable_assets_for_country(
        self,
        country_name,
        commodity_type="upstream",
    ):
        """
        Fetches valuable assets for a specific country from the Valuations API.

        Args:
            commodity_type (str, optional): The type of commodity to fetch assets for. Defaults to "upstream".
            country_name (str): The country to filter assets by. Defaults to None. Example: United Kingdom

        Returns:
            list: A list of valuable assets for the country if the request is successful.
        """

        print(f"Fetching valuable assets for {country_name}...")
        return self.fetch_valuable_assets(commodity_type, country_name)

    def is_asset_valuable(
        self,
        country_name,
        asset_name,
        commodity_type="upstream",
    ):
        """
        Fetches valuable assets for a specific country from the Valuations API.

        Args:
            commodity_type (str, optional): The type of commodity to fetch assets for. Defaults to "upstream".
            country_name (str): The country to filter assets by. Defaults to None. Example: United Kingdom.
            asset_name (str): The asset to filter for. Example: Clair

        Returns:
            A list of valuable asset for the country if the request is successful.
        """

        print(f"Checking if asset {asset_name} is valuable in {country_name}...")
        if len(self.assets) == 0 or self.assets[0].get("countryName") != country_name:
            assets = self.fetch_valuable_assets(
                commodity_type=commodity_type, country_name=country_name, full_list=True
            )
            print(len(assets))
            for asset in assets:
                if asset["assetName"] == asset_name:
                    return f"Asset {asset_name} is valuable in {country_name}. Assets full details: {str(asset)}"
        return f"Asset {asset_name} is not valuable in {country_name}"


if __name__ == "__main__":
    tools = Tools()
    print(tools.fetch_valuable_assets(country_name="Albania"))
    print(tools.is_asset_valuable(country_name="United Kingdom", asset_name="Clair"))

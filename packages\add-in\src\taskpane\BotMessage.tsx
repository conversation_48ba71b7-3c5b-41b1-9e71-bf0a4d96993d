import React, { Fragment } from "react";
import { Message } from "./Message";
import { useStyles } from "./use-styles";
import { CardFooter } from "@fluentui/react-components";
import { TableData } from "./chat-types";

const AIWarning = () => {
    const { aiWarning } = useStyles();

    return (
        <div className={aiWarning}>
            AI-generated content may be incorrect
        </div>
    );
};

const Footer = ({ footerComponents, hideAiWarning }: { footerComponents?: Array<React.ReactNode>; hideAiWarning?: boolean })  => {
    const { cardFooter } = useStyles();

    return (
        <CardFooter className={cardFooter}>
            {footerComponents?.map((component, index) => <Fragment  key={index}>{component}</Fragment >)}
            {!hideAiWarning && <AIWarning />}
        </CardFooter>
    );
};

const BotMessage = ({ message, tableData, footerComponents, hideAiWarning, loading }: { message?: string; tableData?: TableData; footerComponents?: Array<React.ReactNode>; hideAiWarning?: boolean; loading?: boolean; })  => {
    const { botMessage, botMessageContainer } = useStyles();
    
    return (
        <Message 
            message={message} 
            tableData={tableData}
            cardClassName={botMessage} 
            containerClassName={botMessageContainer} 
            footer={(footerComponents?.length > 0 || !hideAiWarning) && (
                <Footer footerComponents={footerComponents} hideAiWarning={hideAiWarning} />
            )}
            loading={loading}
        />
    );
};

export { BotMessage };

#!/usr/bin/env groovy
@Library('utils') _
def sys = "NA"

AWS_REGION = 'us-east-1'
BUILD_ENVIRONMENT = woodmac.getJenkinsEnvironment()

String getEnvironment() {
    if (params.TARGET_ENVIRONMENT != null) {
        return params.TARGET_ENVIRONMENT
    }
    if (woodmac.getJenkinsEnvironment() == 'prod') {
        return 'aiint'
    }

    return 'aidev'
}

def getAvailableEnvironments() {
    if (woodmac.getJenkinsEnvironment() == 'dev') {
        return ['aidev']
    }
    return ['aiint', 'aiuat', 'aiprod']
}

def handleCloudFormationError(stackName) {
    echo "CloudFormation deployment failed for stack: ${stackName}"
    
    def stackEvents = sh(
        script: """
        aws cloudformation describe-stack-events --stack-name ${stackName} --region ${AWS_REGION} --query "reverse(sort_by(StackEvents[?contains(ResourceStatus, 'FAILED')], &Timestamp))[:10].[{Status:ResourceStatus, Reason:ResourceStatusReason, LogicalID:LogicalResourceId, Timestamp:Timestamp}]" --output json
        """,
        returnStdout: true
    ).trim()
    
    echo "Stack failure details:"
    echo stackEvents
    
    def stackStatus = sh(
        script: """
        aws cloudformation describe-stacks --stack-name ${stackName} --region ${AWS_REGION} --query "Stacks[0].StackStatus" --output text""",
        returnStdout: true
    ).trim()
    
    echo "Overall stack status: ${stackStatus}"
    
    error "CloudFormation ${stackType} stack deployment failed. See above for error details."
}

environmentName = getEnvironment()
AGENT_IAM_ROLE_PARAMETER_NAME = "/${environmentName}/wmlgv-assistant-agent/deployment-agent-role-name"
jenkinsAgent = "harbor.prod.woodmac.com/liveservices/jenkins-node-jnlp-cloudformation:3283.v92c105e0f819-1-alpine-jdk21-aws2"

properties([
    parameters([
        choice(name: 'TARGET_ENVIRONMENT', description: 'Environment to deploy', choices: getAvailableEnvironments()),
    ])
])

pipeline {
    agent none
    options {
        disableConcurrentBuilds()
        ansiColor('xterm')
    }
    stages {
        stage('Deploy roles') {
            agent {
                label "dynamicRoleBuilder-${AWS_REGION}-${params.TARGET_ENVIRONMENT}"
            }
            environment {
                AWS_ENVIRONMENT = getEnvironment()
                STACK_NAME = "jenkins-ecs-role-wmlgv-assistant-agent-${getEnvironment()}"
                TEMPLATE_FILE = "../infra/ai-deployment-role.yaml"
            }
            steps {
                dir('jenkins') {
                    sh 'make lint'
                    script {
                        try {
                            sh 'make deploy-role'
                        } catch (Exception e) {
                            handleCloudFormationError(env.STACK_NAME)
                        }
                    }
                }
            }
        }

         stage('Switch to bucket deploy agent') {
            agent {
                ecs {
                    inheritFrom "dynamic-us-east-1-${getEnvironment()}"
                    taskrole woodmac.getAgentRole(
                        region: "us-east-1",
                        environment: "${environmentName}",
                        parameterName: "${AGENT_IAM_ROLE_PARAMETER_NAME}",
                    )
                    image "${jenkinsAgent}"
                    memory 6000
                    cpu 2000
                }
            }
            environment {
                AWS_ENVIRONMENT = getEnvironment()
                STACK_NAME = "wmlgv-assistant-deployment-${getEnvironment()}"
                TEMPLATE_FILE = "../infra/ai-deployment-buckets.yaml"
            }
            stages {
                stage('Deploy buckets') {
                    steps {
                        dir('jenkins') {
                            script {
                                try {
                                    sh 'make deploy'
                                } catch (Exception e) {
                                    handleCloudFormationError(env.STACK_NAME)
                                }
                            }
                        }
                    }
                }
            }
         }
    }
}

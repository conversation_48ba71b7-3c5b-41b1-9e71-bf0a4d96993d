#!/usr/bin/env groovy
@Library('utils') _
def sys = "NA"

AWS_REGION = 'us-east-1'
BUILD_ENVIRONMENT = woodmac.getJenkinsEnvironment()

dockerImageRepository = 'nexus.prod.woodmac.com/woodmac2.0/wmlgv/'

String getEnvironment() {
    if (params.TARGET_ENVIRONMENT != null) {
        return params.TARGET_ENVIRONMENT
    }
    if (woodmac.getJenkinsEnvironment() == 'prod') {
        return 'aiint'
    }

    return 'aidev'
}

String getTokenIssuer() {
    switch(getEnvironment()) {
        case 'aiprod':
            return 'https://woodmackenzie.okta.com/oauth2/default' // created in CIAM, need to get details
        case 'aiuat':
            return 'https://woodmackenzie.okta.com/oauth2/default'
        case 'aiint':
            return 'https://woodmackenzie.okta.com/oauth2/default'
        default:
            return 'https://woodmac-ssopreview1.oktapreview.com/oauth2/ausbarapdbW8DIBbC1d7'
    }
}

String getValuationsEnvironment() {
    if (getEnvironment() == 'aiprod') {
        return 'prod'
    }
    if (getEnvironment() == 'aiint') {
        return 'int'
    }
    if (getEnvironment() == 'aiuat') {
        return 'uat'
    }
    
    return 'dev'
}

String getEntitlementsOauthUrl() {
    switch(getEnvironment()) {
        case 'aiprod':
            return 'todo'
        case 'aiuat':
            return 'https://woodmac-ssopreview3.oktapreview.com/oauth2/ausbqowwsjyuVyjVT1d7/v1/token'
        case 'aiint':
            return 'https://woodmac-ssopreview2.oktapreview.com/oauth2/ausbpbxl6wwfwTPUK1d7/v1/token'
        default:
            return 'https://woodmac-ssopreview1.oktapreview.com/oauth2/ausbara5b91ENpkyd1d7/v1/token'
    }
}

String getEntitlementsClientId() {
    switch(getEnvironment()) {
        case 'aiprod':
            return 'todo'
        case 'aiuat':
            return '0oaoi0mf0golmafN11d7'
        case 'aiint':
            return '0oaoi0hczvki22vIl1d7'
        default:
            return '0oaohw9m1oyr16hYX1d7'
    }
}

String getDigitalContentUrl() {
    switch(getEnvironment()) {
        case 'aiprod':
            return 'https://api2.woodmac.com/chatbot-api'
        case 'aiuat':
            return 'https://api2.uat.woodmac.com/chatbot-api'
        case 'aiint':
            return 'https://api2.int.woodmac.com/chatbot-api'
        default:
            return 'https://api2.dev.woodmac.com/chatbot-api'
    }
}

String getEntitlementsApiUrl() {
       switch(getEnvironment()) {
        case 'aiprod':
            return 'https://entitlement-api.woodmac.com'
        case 'aiuat':
            return 'https://entitlement-api-uat.woodmac.com'
        case 'aiint':
            return 'https://entitlement-api-cpquat.woodmac.com'
        default:
            return 'https://entitlement-api.dev.woodmac.com'
    } 
}

String getUseEntitlements() {
    switch(getEnvironment()) {
        case 'aiprod':
            return 'false'
        case 'aiuat':
            return 'true'
        case 'aiint':
            return 'true'
        default:
            return 'true'
    }
}

def getAvailableEnvironments() {
    if (woodmac.getJenkinsEnvironment() == 'dev') {
        return ['aidev']
    }
    return ['aiint', 'aiuat', 'aiprod']
}

def handleCloudFormationError(stackName) {
    echo "CloudFormation deployment failed for stack: ${stackName}"
    
    def stackEvents = sh(
        script: """
        aws cloudformation describe-stack-events --stack-name ${stackName} --region ${AWS_REGION} --query "reverse(sort_by(StackEvents[?contains(ResourceStatus, 'FAILED')], &Timestamp))[:10].[{Status:ResourceStatus, Reason:ResourceStatusReason, LogicalID:LogicalResourceId, Timestamp:Timestamp}]" --output json
        """,
        returnStdout: true
    ).trim()
    
    echo "Stack failure details:"
    echo stackEvents
    
    def stackStatus = sh(
        script: """
        aws cloudformation describe-stacks --stack-name ${stackName} --region ${AWS_REGION} --query "Stacks[0].StackStatus" --output text""",
        returnStdout: true
    ).trim()
    
    echo "Overall stack status: ${stackStatus}"
    
    error "CloudFormation stack deployment failed. See above for error details."
}

environmentName = getEnvironment()
AGENT_IAM_ROLE_PARAMETER_NAME = "/${environmentName}/wmlgv-assistant-agent/deployment-agent-role-name"

properties([
    parameters([
        choice(name: 'TARGET_ENVIRONMENT', description: 'Environment to deploy', choices: getAvailableEnvironments()),
        string(name: 'DOCKER_VERSION', description: 'Docker image version to deploy (required)', required: true, trim: true)
    ])
])

jenkinsAgent = "harbor.prod.woodmac.com/liveservices/jenkins-node-jnlp-cloudformation:3283.v92c105e0f819-1-alpine-jdk21-aws2"
def agentId = null

pipeline {
    agent {
        ecs {
            inheritFrom "dynamic-us-east-1-${environmentName}"
            taskrole woodmac.getAgentRole(
                region: "us-east-1",
                environment: "${environmentName}",
                parameterName: "${AGENT_IAM_ROLE_PARAMETER_NAME}",
            )
            image "${jenkinsAgent}"
            memory "2024"
            cpu "1024"
        }
    }
    options {
        disableConcurrentBuilds()
        ansiColor('xterm')
    }
    environment {
        AWS_ENVIRONMENT = getEnvironment()
        STACK_NAME = "wmlgv-assistant-orchestrator-${getEnvironment()}"
        TEMPLATE_FILE = "../infra/ai-wmlgv-assistant-orchestrator-resources.yaml"
        VALUATION_ENVIRONMENT = "${getValuationsEnvironment()}"
        DOCKER_VERSION = "${params.DOCKER_VERSION}"
        LENS = credentials('valuations-lens-user-prod')
    }
    stages {
        stage('Deploy ecs infrastructure') {
            when {
                branch "main"
            }
            steps {
                dir('jenkins') {
                    script {
                        agentId = sh(
                            script: "aws bedrock-agent list-agents | jq -r '.agentSummaries[] | select(.agentName == \"wmlgv-${AWS_ENVIRONMENT}-assistant-agent\") | .agentId'",
                            returnStdout: true
                        ).trim()

                        def agentAliasId = sh(
                            script: "aws bedrock-agent list-agent-aliases --agent-id ${agentId} | jq -r '.agentAliasSummaries[] | select(.agentAliasName == \"orchestrator\") | .agentAliasId'",
                            returnStdout: true
                        ).trim()
                        
                        def ecrImage = "${dockerImageRepository}wmlgv-assistant-orchestrator:${DOCKER_VERSION}"

                        echo "Bedrock Agent Id: ${agentId}"
                        echo "Bedrock Agent Alias Id: ${agentAliasId}"
                        echo "ECR Image: ${ecrImage}"

                        sh 'make lint'
                        
                        try {
                            sh "make deploy EXTRA_PARAMS=\"BedrockAgentId=${agentId} EcrImage=${ecrImage} LensDigitalContentUrl=${getDigitalContentUrl()} LensUsername=${LENS_USR} LensPassword=${LENS_PSW} BedrockAgentAliasId=${agentAliasId} TokenIssuer=${getTokenIssuer()} UseEntitlements=${getUseEntitlements()} EntitlementsOauthUrl=${getEntitlementsOauthUrl()} EntitlementsClientId=${getEntitlementsClientId()} EntitlementsApiUrl=${getEntitlementsApiUrl()}\""
                        } catch (Exception e) {
                            handleCloudFormationError(env.STACK_NAME)
                        }
                    }
                }
            }
        }
    }
}

{"cells": [{"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["# set AWS credentials in you .env file in the root of the project\n", "from dotenv import load_dotenv\n", "\n", "load_dotenv()\n", "AWS_REGION = \"us-east-1\""]}, {"cell_type": "code", "execution_count": 88, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["The value of the asset called Blacktip, based on the BASE price scenario, is as follows:\n", "\n", "| Metric                  | Value (in millions) |\n", "|-------------------------|---------------------|\n", "| Total PV Post-tax       | -$735.61            |\n", "| Total PV Pre-tax        | -$608.39            |\n", "| Remaining PV Post-tax   | $113.25             |\n", "| Remaining PV Pre-tax    | $203.89             |\n", "\n", "=====\n", "The Blacktip asset is located in Australia and is part of the Upstream business area. It's important to note that while the total PV figures are negative, the remaining PV figures are positive, indicating that the asset may have future value despite past losses.\n", "Traces for this query: [{'rationale': {'text': \"To answer this question about the value of an asset called Blacktip, I need to query the asset table. First, I'll need to get the schema of the asset table to understand its structure, and then I'll query the dataset for the specific asset.\", 'traceId': '2bfa5d5b-2687-43f1-bdb3-0e105bb49a09-0'}}, {'invocationInput': {'actionGroupInvocationInput': {'actionGroupName': 'valuations-results-processor', 'apiPath': '/api/get-asset-table-schema', 'executionType': 'LAMBDA', 'verb': 'get'}, 'invocationType': 'ACTION_GROUP', 'traceId': '2bfa5d5b-2687-43f1-bdb3-0e105bb49a09-0'}}, {'observation': {'actionGroupInvocationOutput': {'text': \"The table name is asset_table. You must never request more than 25 rows and 25 columns. Asset table is a Polars DataFrame with the following schema: Schema({'record_id': String, 'country': String, 'business_area': String, 'regime': String, 'asset_name': String, 'valuation_type': String, 'price_scenario': String, 'Discount Date': Float64, 'Discount Rate': Float64, 'Total PV Post-tax': Float64, 'Total PV Pre-tax': Float64, 'Remaining PV Post-tax': Float64, 'Remaining PV Pre-tax': Float64, 'Remaining PV/boe Post-tax': Float64, 'Remaining PV/boe Pre-tax': Float64, 'Total Government Take Value': Float64, 'Total Government Take Percentage': Float64, 'Remaining Government Take Value': Float64, 'Remaining Government Take Percentage': Float64, 'P/I Ratio': Float64, 'Capex/boe': Float64, 'Opex/boe': Float64, 'Remaining Liquid Reserves WI (mmbbls)': Float64, 'Remaining Gas Reserves WI (bcf)': Float64, 'Total Remaining Reserves WI (mmboe)': Float64, 'Total Reserves WI (mmboe)': Float64, 'Post-tax IRR': Float64, 'Pre-tax IRR': Float64, 'Payback Period (years)': Float64, 'Reserve life at current production (years)': Float64, 'Corporate Income Tax Loss Position': Float64, 'Other Tax Loss Position': Float64, 'Tax Overhang': Float64, 'Remaining Liquid Reserves Ent. (mmbbls)': Float64, 'Remaining Gas Reserves Ent. (bcf)': Float64, 'Total Remaining Reserves Ent. (mmboe)': Float64, 'Total Reserves Ent. (mmboe)': Float64, 'Total Liquid Reserves Ent. (mmbbls)': Float64, 'Total Gas Reserves Ent. (bcf)': Float64, 'Total Liquid Reserves WI (mmbbls)': Float64, 'Total Gas Reserves WI (bcf)': Float64}).\"}, 'traceId': '2bfa5d5b-2687-43f1-bdb3-0e105bb49a09-0', 'type': 'ACTION_GROUP'}}, {'rationale': {'text': \"Now that I have the schema of the asset table, I can query for the asset named Blacktip. I'll focus on the key value metrics, which are typically the Present Value (PV) figures. I'll query for both post-tax and pre-tax PV, as well as some other relevant information.\", 'traceId': '2bfa5d5b-2687-43f1-bdb3-0e105bb49a09-1'}}, {'invocationInput': {'actionGroupInvocationInput': {'actionGroupName': 'valuations-results-processor', 'apiPath': '/api/query_dataset', 'executionType': 'LAMBDA', 'parameters': [{'name': 'sql_query', 'type': 'string', 'value': 'SELECT asset_name, price_scenario, \"Total PV Post-tax\", \"Total PV Pre-tax\", \"Remaining PV Post-tax\", \"Remaining PV Pre-tax\", country, business_area FROM asset_table WHERE asset_name = \\'Blacktip\\' AND price_scenario = \\'BASE\\''}], 'verb': 'get'}, 'invocationType': 'ACTION_GROUP', 'traceId': '2bfa5d5b-2687-43f1-bdb3-0e105bb49a09-1'}}, {'observation': {'actionGroupInvocationOutput': {'text': 'shape: (1, 8)\\n┌────────────┬────────────┬────────────┬───────────┬───────────┬───────────┬───────────┬───────────┐\\n│ asset_name ┆ price_scen ┆ Total PV   ┆ Total PV  ┆ Remaining ┆ Remaining ┆ country   ┆ business_ │\\n│ ---        ┆ ario       ┆ Post-tax   ┆ Pre-tax   ┆ PV        ┆ PV        ┆ ---       ┆ area      │\\n│ str        ┆ ---        ┆ ---        ┆ ---       ┆ Post-tax  ┆ Pre-tax   ┆ str       ┆ ---       │\\n│            ┆ str        ┆ f64        ┆ f64       ┆ ---       ┆ ---       ┆           ┆ str       │\\n│            ┆            ┆            ┆           ┆ f64       ┆ f64       ┆           ┆           │\\n╞════════════╪════════════╪════════════╪═══════════╪═══════════╪═══════════╪═══════════╪═══════════╡\\n│ Blacktip   ┆ BASE       ┆ -735.6128  ┆ -608.3918 ┆ 113.2464  ┆ 203.8906  ┆ Australia ┆ Upstream  │\\n└────────────┴────────────┴────────────┴───────────┴───────────┴───────────┴───────────┴───────────┘'}, 'traceId': '2bfa5d5b-2687-43f1-bdb3-0e105bb49a09-1', 'type': 'ACTION_GROUP'}}, {'rationale': {'text': \"Now that I have the data for the Blacktip asset, I can provide an answer to the user's question about its value.\", 'traceId': '2bfa5d5b-2687-43f1-bdb3-0e105bb49a09-2'}}, {'observation': {'finalResponse': {'text': \"The value of the asset called Blacktip, based on the BASE price scenario, is as follows:\\n\\n| Metric                  | Value (in millions) |\\n|-------------------------|---------------------|\\n| Total PV Post-tax       | -$735.61            |\\n| Total PV Pre-tax        | -$608.39            |\\n| Remaining PV Post-tax   | $113.25             |\\n| Remaining PV Pre-tax    | $203.89             |\\n\\n=====\\nThe Blacktip asset is located in Australia and is part of the Upstream business area. It's important to note that while the total PV figures are negative, the remaining PV figures are positive, indicating that the asset may have future value despite past losses.\"}, 'traceId': '2bfa5d5b-2687-43f1-bdb3-0e105bb49a09-2', 'type': 'FINISH'}}]\n"]}, {"data": {"text/plain": ["\"The value of the asset called Blacktip, based on the BASE price scenario, is as follows:\\n\\n| Metric                  | Value (in millions) |\\n|-------------------------|---------------------|\\n| Total PV Post-tax       | -$735.61            |\\n| Total PV Pre-tax        | -$608.39            |\\n| Remaining PV Post-tax   | $113.25             |\\n| Remaining PV Pre-tax    | $203.89             |\\n\\n=====\\nThe Blacktip asset is located in Australia and is part of the Upstream business area. It's important to note that while the total PV figures are negative, the remaining PV figures are positive, indicating that the asset may have future value despite past losses.\""]}, "execution_count": 88, "metadata": {}, "output_type": "execute_result"}], "source": ["import boto3\n", "\n", "AGENT_ID = \"PHWXWDWR3W\"\n", "AGENT_ALIAS_ID = \"TSTALIASID\"\n", "\n", "bedrock_agent = boto3.client(\n", "    service_name=\"bedrock-agent-runtime\",\n", ")\n", "\n", "\n", "def print_traces(trace=[]):\n", "    if len(trace) == 0:\n", "        return\n", "    interesting_keys = {\"rationale\", \"observation\", \"invocationType\", \"invocationInput\"}\n", "    interesting_traces = [\n", "        t[\"trace\"][\"orchestrationTrace\"]\n", "        for t in trace\n", "        if \"trace\" in t\n", "        and \"orchestrationTrace\" in t[\"trace\"]\n", "        and any(key in t[\"trace\"][\"orchestrationTrace\"] for key in interesting_keys)\n", "    ]\n", "    print(f\"Traces for this query: {interesting_traces}\")\n", "\n", "\n", "def invoke_agent(prompt, sessionId=f\"session-{AGENT_ID}\"):\n", "    \"\"\"\n", "    Sends a prompt for the agent to process and respond to.\n", "\n", "    :param agent_id: The unique identifier of the agent to use.\n", "    :param agent_alias_id: The alias of the agent to use.\n", "    :param session_id: The unique identifier of the session. Use the same value across requests\n", "                        to continue the same conversation.\n", "    :param prompt: The prompt that you want <PERSON> to complete.\n", "    :return: Inference response from the model.\n", "    \"\"\"\n", "    response = bedrock_agent.invoke_agent(\n", "        agentId=AGENT_ID,\n", "        sessionId=sessionId,\n", "        agentAliasId=AGENT_ALIAS_ID,\n", "        inputText=prompt,\n", "        enableTrace=True,\n", "    )\n", "\n", "    completion = \"\"\n", "    trace = []\n", "\n", "    for event in response.get(\"completion\"):\n", "        if \"trace\" in event:\n", "            trace.append(event[\"trace\"])\n", "        if \"chunk\" in event:\n", "            chunk = event[\"chunk\"]\n", "            stuff = chunk[\"bytes\"].decode()\n", "            print(stuff)\n", "            completion = completion + stuff\n", "\n", "    print_traces(trace)\n", "    return completion\n", "\n", "\n", "invoke_agent(\n", "    \"what's the value of an asset called Blacktip\",\n", "    \"bnjksbdsbdfvdbfvdvdsfvksmvslv\",\n", ")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Schema({'record_id': String, 'company': String, 'country': String, 'business_area': String, 'regime': String, 'asset_id': Int64, 'asset_name': String, 'valuation_type': String, 'price_scenario': String, 'Discount Date': Int64, 'Discount Rate': Int64, 'Total PV Post-tax': Float64, 'Total PV Pre-tax': Float64, 'Remaining PV Post-tax': Float64, 'Remaining PV Pre-tax': Float64, 'Remaining PV/boe Post-tax': Float64, 'Remaining PV/boe Pre-tax': Float64, 'Total Government Take Value': Float64, 'Total Government Take Percentage': Float64, 'Remaining Government Take Value': Float64, 'Remaining Government Take Percentage': Float64, 'P/I Ratio': Float64, 'Capex/boe': Float64, 'Opex/boe': Float64, 'Remaining Liquid Reserves WI (mmbbls)': Float64, 'Remaining Gas Reserves WI (bcf)': Float64, 'Total Remaining Reserves WI (mmboe)': Float64, 'Total Reserves WI (mmboe)': Float64, 'Post-tax IRR': Float64, 'Pre-tax IRR': Float64, 'Payback Period (years)': Float64, 'Reserve life at current production (years)': String, 'Corporate Income Tax Loss Position': Float64, 'Other Tax Loss Position': Float64, 'Tax Overhang': Float64, 'Remaining Liquid Reserves Ent. (mmbbls)': Float64, 'Remaining Gas Reserves Ent. (bcf)': Float64, 'Total Remaining Reserves Ent. (mmboe)': Float64, 'Total Reserves Ent. (mmboe)': Float64, 'Total Liquid Reserves Ent. (mmbbls)': Float64, 'Total Gas Reserves Ent. (bcf)': Float64, 'Total Liquid Reserves WI (mmbbls)': Float64, 'Total Gas Reserves WI (bcf)': Float64})\n"]}, {"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (3, 2)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>price_scenario</th><th>total_value</th></tr><tr><td>str</td><td>f64</td></tr></thead><tbody><tr><td>&quot;BASE&quot;</td><td>413661.6139</td></tr><tr><td>&quot;LOW&quot;</td><td>348086.1625</td></tr><tr><td>&quot;HIGH&quot;</td><td>521507.8306</td></tr></tbody></table></div>"], "text/plain": ["shape: (3, 2)\n", "┌────────────────┬─────────────┐\n", "│ price_scenario ┆ total_value │\n", "│ ---            ┆ ---         │\n", "│ str            ┆ f64         │\n", "╞════════════════╪═════════════╡\n", "│ BASE           ┆ 413661.6139 │\n", "│ LOW            ┆ 348086.1625 │\n", "│ HIGH           ┆ 521507.8306 │\n", "└────────────────┴─────────────┘"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import polars as pl\n", "\n", "df = pl.read_csv(\"exxon_global_asset_request.csv\", infer_schema_length=10000)\n", "\n", "print(df.schema)\n", "\n", "result = pl.sql(\n", "    \"\"\"\n", "    SELECT price_scenario, SUM(\"Total PV Post-tax\") as total_value\n", "    FROM df\n", "    WHERE company = 'ExxonMobil' AND country = 'United States'\n", "    GROUP BY price_scenario\n", "    \"\"\"\n", ").collect()\n", "\n", "result"]}, {"cell_type": "code", "execution_count": 124, "metadata": {}, "outputs": [], "source": ["import json\n", "import csv\n", "import os\n", "import polars as pl\n", "\n", "\n", "SUPPORTED_REPORTS = [\n", "    \"Asset Summary Discount Rate 6\",\n", "]\n", "\n", "\n", "def flatten_countries(countries, company_name: str = None, scenario_name: str = None):\n", "    flattened_records = []\n", "\n", "    for country in countries:\n", "        for business_area in country[\"businessAreas\"]:\n", "            for regime in business_area[\"regimes\"]:\n", "                for asset in regime[\"assets\"]:\n", "                    for valuation in asset[\"valuations\"]:\n", "                        for scenario in valuation[\"scenarios\"]:\n", "                            scenario_name = scenario_name or scenario[\"name\"]\n", "                            if company_name:\n", "                                base_record = {\n", "                                    \"record_id\": f\"{company_name}_{country['name']}_{asset['name']}_{scenario_name}\",\n", "                                    \"company\": company_name,\n", "                                }\n", "                            else:\n", "                                base_record = {\n", "                                    \"record_id\": f\"{country['name']}_{asset['name']}_{scenario_name}\",\n", "                                }\n", "                            base_record.update(\n", "                                {\n", "                                    \"country\": country[\"name\"],\n", "                                    \"business_area\": business_area[\"name\"],\n", "                                    \"regime\": regime[\"name\"],\n", "                                    \"asset_name\": asset[\"name\"],\n", "                                    \"valuation_type\": valuation[\"type\"],\n", "                                    \"price_scenario\": scenario_name,\n", "                                }\n", "                            )\n", "\n", "                            # Add metrics\n", "                            if scenario[\"result\"][\"reports\"]:\n", "                                for report in scenario[\"result\"][\"reports\"]:\n", "                                    if (\n", "                                        \"discreteMetrics\" in report\n", "                                        and report[\"name\"] in SUPPORTED_REPORTS\n", "                                    ):\n", "                                        metrics = report[\"discreteMetrics\"]\n", "                                        base_record.update(metrics)\n", "\n", "                            flattened_records.append(base_record)\n", "\n", "    return flattened_records\n", "\n", "\n", "def flatten_json(json_data, output_file=None, scenario_name: str = None):\n", "    flattened_records = []\n", "\n", "    if \"body\" in json_data:\n", "        json_data = json_data[\"body\"]\n", "\n", "    if \"companies\" in json_data:\n", "        for company in json_data[\"companies\"]:\n", "            flattened_records.extend(\n", "                flatten_countries(\n", "                    company[\"countries\"], company[\"name\"], scenario_name=scenario_name\n", "                )\n", "            )\n", "    else:\n", "        flattened_records = flatten_countries(\n", "            json_data[\"countries\"], scenario_name=scenario_name\n", "        )\n", "\n", "    if output_file:\n", "        # Write to new JSON file\n", "        with open(output_file, \"w\") as f:\n", "            json.dump(flattened_records, f, indent=2)\n", "\n", "    return flattened_records\n", "\n", "\n", "def flatten_csv(json_data, output_file, scenario_name: str = None) -> None:\n", "    flattened_records = flatten_json(json_data, scenario_name=scenario_name)\n", "    headers = list(flattened_records[0].keys())\n", "    with open(output_file, \"w\", newline=\"\") as csvfile:\n", "        writer = csv.DictWriter(csvfile, fieldnames=headers, extrasaction=\"ignore\")\n", "        writer.writeheader()\n", "        writer.writerows(flattened_records)\n", "\n", "\n", "def flatten_parquet(json_data, output_file, scenario_name: str = None) -> None:\n", "    flattened_records = flatten_json(json_data, scenario_name=scenario_name)\n", "    df = pl.DataFrame(flattened_records)\n", "    df.write_parquet(output_file)\n", "\n", "\n", "def flatt_all_json_dir(dir_path):\n", "    for file in os.listdir(dir_path):\n", "        if file.endswith(\".json\"):\n", "            with open(file, \"r\") as f:\n", "                json_data = json.load(f)\n", "            flatten_csv(json_data, f\"{file.strip('.json')}.csv\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import requests\n", "import time\n", "from typing import Dict, Any\n", "\n", "UVS_URL = \"https://api2.dev.woodmac.com/upstream-valuation/upstream-valuation-service/develop-main\"\n", "COOKIE = \"eyJraWQiOiJtR29wU0ZIdlhKSlQtb1NmZWl5YU44U2dac293dHhrUk1idkRFQ2xfSFRRIiwiYWxnIjoiUlMyNTYifQ.****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.Q_Lo96T6dNZCXRVazkEaK0zmMTZyfsTlf4bTDVsllbeyp3cYxyi6Z3nCb0FyJnJ2ukm6aMUe4UTV10GAAhTf6UXuunvRof6Kbmp4nVL0U5J04CCyPaw4aiv0VPIsit5r_VtkOPc1k8BFHaw-o3Ko8E7Osgq-EiD8EcLtGCQncBmH3qra5VdTHLpJSOij2VwO4kSvoh8SsX3IKp-rv1ws1Evl2-hQ9mHHNf4UXQLim-LgzGQho4HoHmgj8d9QSdeF5Qfp9bwhYRBPHYp1fU6IL0UnbbEF2DGrCp7zcMxZDhruqhsvWDYJxfK_4AXjJgn9hnhA-cuQV3v8ImFo8ajsog\"\n", "\n", "BASE_REQUEST = {\n", "    \"cashflowOutput\": \"nominal\",\n", "    \"discounting\": {\"month\": 1, \"year\": 2021, \"percentage\": 10},\n", "    \"economicCutoffApplied\": F<PERSON>e,\n", "    \"entitlementBasis\": \"method-2\",\n", "    \"priceDeckId\": \"dd4170e7-be13-4500-8bcd-b20a580aecb1|00gcd5xza61ty9ZWZ1d7\",\n", "    \"sensitivities\": {\n", "        \"startYear\": 2025,\n", "        \"capexPercentage\": 0,\n", "        \"gasPricePercentage\": 0,\n", "        \"gasProductionPercentage\": 0,\n", "        \"liquidPricePercentage\": 0,\n", "        \"liquidProductionPercentage\": 0,\n", "        \"opexPercentage\": 0,\n", "        \"tariffPaymentPercentage\": 0,\n", "        \"tariffReceiptPercentage\": 0,\n", "    },\n", "    \"taxSchedule\": \"paid\",\n", "    \"taxSynergies\": \"standalone\",\n", "}\n", "\n", "\n", "def merge_requests(post_body: Dict[Any, Any]) -> Dict[Any, Any]:\n", "    final_request = BASE_REQUEST.copy()\n", "\n", "    def deep_update(original: Dict, update: Dict) -> Dict:\n", "        for key, value in update.items():\n", "            if isinstance(value, dict) and key in original:\n", "                original[key] = deep_update(original[key], value)\n", "            else:\n", "                original[key] = value\n", "        return original\n", "\n", "    return deep_update(final_request, post_body)\n", "\n", "\n", "def run_valuation(post_body: Dict[Any, Any] = {}, timeout_minutes: int = 3) -> Dict:\n", "    def make_request_with_retry(method, url, **kwargs):\n", "        try:\n", "            response = method(url, **kwargs)\n", "            response.raise_for_status()\n", "            return response\n", "        except requests.exceptions.HTTPError as e:\n", "            if 500 <= e.response.status_code < 600:\n", "                # Retry once on 5xx errors\n", "                time.sleep(1)\n", "                response = method(url, **kwargs)\n", "                response.raise_for_status()\n", "                return response\n", "            raise\n", "\n", "    start_time = time.time()\n", "\n", "    # Make initial POST request\n", "    req = merge_requests(post_body)\n", "    post_response = make_request_with_retry(\n", "        requests.post,\n", "        f\"{UVS_URL}/valuations/v1\",\n", "        json=req,\n", "        headers={\"Cookie\": f\"iPlanetDirectoryPro={COOKIE}\"},\n", "    )\n", "    valuation_id = post_response.json()[\"valuationId\"]\n", "    print(\n", "        f\"Created a valuation with ID: {valuation_id}. Took {time.time() - start_time:.2f} seconds\"\n", "    )\n", "\n", "    # Poll until complete\n", "    start_time = time.time()\n", "    timeout_seconds = timeout_minutes * 60\n", "\n", "    while True:\n", "        if time.time() - start_time > timeout_seconds:\n", "            raise Exception(f\"Polling timed out after {timeout_minutes} minutes\")\n", "\n", "        poll_response = make_request_with_retry(\n", "            requests.get,\n", "            f\"{UVS_URL}/valuations/v1/{valuation_id}\",\n", "            headers={\"Cookie\": f\"iPlanetDirectoryPro={COOKIE}\"},\n", "        )\n", "        response_data = poll_response.json()\n", "\n", "        if response_data[\"status\"] == \"completed\":\n", "            print(\n", "                f\"Completed valuation with ID: {valuation_id}. (number of assets {response_data['assetsTotal']}). Took {time.time() - start_time:.2f} seconds\"\n", "            )\n", "            return valuation_id\n", "        elif response_data[\"status\"] == \"failed\":\n", "            print(f\"Failed valuation with ID: {valuation_id}\")\n", "            raise Exception(\"Process failed\")\n", "\n", "        time.sleep(1)\n"]}, {"cell_type": "code", "execution_count": 115, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "from pathlib import Path\n", "import polars as pl\n", "from typing import List, Union\n", "from datetime import datetime\n", "\n", "REPORT_PROVIDER_URL = \"https://wmlgv-report-provider.dev.woodmac.com\"\n", "\n", "\n", "def save_valuation_report(\n", "    valuation_id: str, price_scenario: str, scenario_name: str\n", ") -> None:\n", "    # Create output directory if it doesn't exist\n", "    output_dir = Path(f\"output/{valuation_id}\")\n", "    output_dir.mkdir(parents=True, exist_ok=True)\n", "    output_file = output_dir / \"reports.json\"\n", "\n", "    # check if file exists before making the request\n", "    if output_file.exists():\n", "        print(f\"Report already exists at {output_file}\")\n", "    else:\n", "        response = requests.post(\n", "            f\"{REPORT_PROVIDER_URL}/api/ValuationReport/RetrieveReport\",\n", "            headers={\"Content-Type\": \"application/json\"},\n", "            json={\n", "                \"header\": {\n", "                    \"correlationId\": valuation_id,\n", "                    \"flags\": {\"enableCustomCalculations\": True},\n", "                    \"createdBy\": \"vals-generator\",\n", "                },\n", "                \"body\": {},\n", "            },\n", "        )\n", "        response.raise_for_status()\n", "        # Save response to file\n", "        with open(f\"{output_dir}/{scenario_name}.txt\", \"w\") as f:\n", "            f.write(\"\\n\")\n", "        with open(output_file, \"w\") as f:\n", "            f.write(response.text)\n", "        print(f\"Saved report to {output_file}\")\n", "\n", "    csv_path = f\"{output_file}\".replace(\"reports.json\", \"flattened.csv\")\n", "    print(f\"Creating flattened csv: {csv_path}\")\n", "    flatten_csv(\n", "        json.load(open(output_file)),\n", "        csv_path,\n", "        price_scenario,\n", "    )\n", "    parquet_path = f\"{output_file}\".replace(\"reports.json\", \"flattened.parquet\")\n", "    print(f\"Creating flattened parquet: {parquet_path}\")\n", "    flatten_parquet(\n", "        json.load(open(output_file)),\n", "        parquet_path,\n", "        price_scenario,\n", "    )\n", "\n", "\n", "def combine_parquet_files(\n", "    file_paths: List[str],\n", "    output_path: Union[str, Path],\n", ") -> None:\n", "    if not file_paths:\n", "        raise ValueError(\"No input files provided\")\n", "\n", "    input_paths = [Path(p) for p in file_paths]\n", "    for path in input_paths:\n", "        if not path.exists():\n", "            raise FileNotFoundError(f\"File not found: {path}\")\n", "\n", "    lazy_frames = [pl.scan_parquet(str(path)) for path in input_paths]\n", "\n", "    timestamp = datetime.now().strftime(\"%Y%m%d_%H%M%S\")\n", "    output_file = Path(f\"output/{output_path}_combined_{timestamp}.parquet\")\n", "\n", "    pl.concat(lazy_frames).collect().write_parquet(output_file, compression=\"snappy\")\n", "\n", "    return output_file\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Running batch number 1\n", "Created a valuation with ID: 8abaf405-66f6-9714-6ab6-979df7412dbb. Took 12.39 seconds\n", "Completed valuation with ID: 8abaf405-66f6-9714-6ab6-979df7412dbb. (number of assets 2811). Took 32.26 seconds\n", "Saved report to output/8abaf405-66f6-9714-6ab6-979df7412dbb/reports.json\n", "Creating flattened csv: output/8abaf405-66f6-9714-6ab6-979df7412dbb/flattened.csv\n", "Creating flattened parquet: output/8abaf405-66f6-9714-6ab6-979df7412dbb/flattened.parquet\n", "Created a valuation with ID: 34bd432d-a319-2b9c-96f2-80bd7de46cfc. <PERSON><PERSON> 6.85 seconds\n", "Completed valuation with ID: 34bd432d-a319-2b9c-96f2-80bd7de46cfc. (number of assets 2811). Took 73.56 seconds\n", "Saved report to output/34bd432d-a319-2b9c-96f2-80bd7de46cfc/reports.json\n", "Creating flattened csv: output/34bd432d-a319-2b9c-96f2-80bd7de46cfc/flattened.csv\n", "Creating flattened parquet: output/34bd432d-a319-2b9c-96f2-80bd7de46cfc/flattened.parquet\n", "Created a valuation with ID: 0186c407-9296-e28f-0dcf-123f29523634. Took 10.28 seconds\n", "Completed valuation with ID: 0186c407-9296-e28f-0dcf-123f29523634. (number of assets 2811). Took 65.43 seconds\n", "Saved report to output/0186c407-9296-e28f-0dcf-123f29523634/reports.json\n", "Creating flattened csv: output/0186c407-9296-e28f-0dcf-123f29523634/flattened.csv\n", "Creating flattened parquet: output/0186c407-9296-e28f-0dcf-123f29523634/flattened.parquet\n", "Running batch number 2\n", "Created a valuation with ID: fba30138-150e-3f7f-3577-59c7232ce921. Took 6.78 seconds\n", "Completed valuation with ID: fba30138-150e-3f7f-3577-59c7232ce921. (number of assets 2378). Took 53.92 seconds\n", "Saved report to output/fba30138-150e-3f7f-3577-59c7232ce921/reports.json\n", "Creating flattened csv: output/fba30138-150e-3f7f-3577-59c7232ce921/flattened.csv\n", "Creating flattened parquet: output/fba30138-150e-3f7f-3577-59c7232ce921/flattened.parquet\n", "Created a valuation with ID: 16b20e02-0f40-b40b-49a8-ec736e4aec4a. Took 7.69 seconds\n", "Completed valuation with ID: 16b20e02-0f40-b40b-49a8-ec736e4aec4a. (number of assets 2378). Took 74.75 seconds\n", "Saved report to output/16b20e02-0f40-b40b-49a8-ec736e4aec4a/reports.json\n", "Creating flattened csv: output/16b20e02-0f40-b40b-49a8-ec736e4aec4a/flattened.csv\n", "Creating flattened parquet: output/16b20e02-0f40-b40b-49a8-ec736e4aec4a/flattened.parquet\n", "Created a valuation with ID: 14d91d31-4d0d-bc73-a79d-42c987629a39. Took 7.20 seconds\n", "Completed valuation with ID: 14d91d31-4d0d-bc73-a79d-42c987629a39. (number of assets 2378). Took 66.23 seconds\n", "Saved report to output/14d91d31-4d0d-bc73-a79d-42c987629a39/reports.json\n", "Creating flattened csv: output/14d91d31-4d0d-bc73-a79d-42c987629a39/flattened.csv\n", "Creating flattened parquet: output/14d91d31-4d0d-bc73-a79d-42c987629a39/flattened.parquet\n", "Running batch number 3\n", "Created a valuation with ID: 63b55ee5-72ea-882f-f143-84e10ac144cf. <PERSON><PERSON> 6.44 seconds\n", "Completed valuation with ID: 63b55ee5-72ea-882f-f143-84e10ac144cf. (number of assets 590). Took 33.17 seconds\n", "Saved report to output/63b55ee5-72ea-882f-f143-84e10ac144cf/reports.json\n", "Creating flattened csv: output/63b55ee5-72ea-882f-f143-84e10ac144cf/flattened.csv\n", "Creating flattened parquet: output/63b55ee5-72ea-882f-f143-84e10ac144cf/flattened.parquet\n", "Created a valuation with ID: 3e2b435b-9349-1d96-98dc-b37fb482f569. Too<PERSON> 5.68 seconds\n", "Completed valuation with ID: 3e2b435b-9349-1d96-98dc-b37fb482f569. (number of assets 590). Took 36.98 seconds\n", "Saved report to output/3e2b435b-9349-1d96-98dc-b37fb482f569/reports.json\n", "Creating flattened csv: output/3e2b435b-9349-1d96-98dc-b37fb482f569/flattened.csv\n", "Creating flattened parquet: output/3e2b435b-9349-1d96-98dc-b37fb482f569/flattened.parquet\n", "Created a valuation with ID: 97c8a5d6-d876-dca8-ac6d-a38d1e4ce456. Took 5.12 seconds\n", "Completed valuation with ID: 97c8a5d6-d876-dca8-ac6d-a38d1e4ce456. (number of assets 590). Took 31.93 seconds\n", "Saved report to output/97c8a5d6-d876-dca8-ac6d-a38d1e4ce456/reports.json\n", "Creating flattened csv: output/97c8a5d6-d876-dca8-ac6d-a38d1e4ce456/flattened.csv\n", "Creating flattened parquet: output/97c8a5d6-d876-dca8-ac6d-a38d1e4ce456/flattened.parquet\n", "Running batch number 4\n", "Created a valuation with ID: 06db9589-d1be-4cb3-5232-6316e277a520. Took 4.80 seconds\n", "Completed valuation with ID: 06db9589-d1be-4cb3-5232-6316e277a520. (number of assets 669). Took 32.96 seconds\n", "Saved report to output/06db9589-d1be-4cb3-5232-6316e277a520/reports.json\n", "Creating flattened csv: output/06db9589-d1be-4cb3-5232-6316e277a520/flattened.csv\n", "Creating flattened parquet: output/06db9589-d1be-4cb3-5232-6316e277a520/flattened.parquet\n", "Created a valuation with ID: 0e469c61-9dea-a184-38a3-65b5c51c63c7. Too<PERSON> 6.27 seconds\n", "Completed valuation with ID: 0e469c61-9dea-a184-38a3-65b5c51c63c7. (number of assets 669). Took 39.27 seconds\n", "Saved report to output/0e469c61-9dea-a184-38a3-65b5c51c63c7/reports.json\n", "Creating flattened csv: output/0e469c61-9dea-a184-38a3-65b5c51c63c7/flattened.csv\n", "Creating flattened parquet: output/0e469c61-9dea-a184-38a3-65b5c51c63c7/flattened.parquet\n", "Created a valuation with ID: 1b422613-ccd8-eeac-3b4d-d9ae288040ea. Took 5.34 seconds\n", "Completed valuation with ID: 1b422613-ccd8-eeac-3b4d-d9ae288040ea. (number of assets 669). Took 32.00 seconds\n", "Saved report to output/1b422613-ccd8-eeac-3b4d-d9ae288040ea/reports.json\n", "Creating flattened csv: output/1b422613-ccd8-eeac-3b4d-d9ae288040ea/flattened.csv\n", "Creating flattened parquet: output/1b422613-ccd8-eeac-3b4d-d9ae288040ea/flattened.parquet\n", "Running batch number 5\n", "Created a valuation with ID: d83c9dc0-b524-7188-a23a-d910ae83ac85. Took 4.60 seconds\n", "Completed valuation with ID: d83c9dc0-b524-7188-a23a-d910ae83ac85. (number of assets 625). Took 32.32 seconds\n", "Saved report to output/d83c9dc0-b524-7188-a23a-d910ae83ac85/reports.json\n", "Creating flattened csv: output/d83c9dc0-b524-7188-a23a-d910ae83ac85/flattened.csv\n", "Creating flattened parquet: output/d83c9dc0-b524-7188-a23a-d910ae83ac85/flattened.parquet\n", "Created a valuation with ID: 8ec915b5-c04a-cfb2-9db6-e7cc4b657dd9. Took 5.10 seconds\n", "Completed valuation with ID: 8ec915b5-c04a-cfb2-9db6-e7cc4b657dd9. (number of assets 625). Took 53.92 seconds\n", "Saved report to output/8ec915b5-c04a-cfb2-9db6-e7cc4b657dd9/reports.json\n", "Creating flattened csv: output/8ec915b5-c04a-cfb2-9db6-e7cc4b657dd9/flattened.csv\n", "Creating flattened parquet: output/8ec915b5-c04a-cfb2-9db6-e7cc4b657dd9/flattened.parquet\n", "Created a valuation with ID: da7ff60e-233c-e381-a90e-1d5e32b75932. Took 4.60 seconds\n", "Completed valuation with ID: da7ff60e-233c-e381-a90e-1d5e32b75932. (number of assets 625). Took 45.66 seconds\n", "Saved report to output/da7ff60e-233c-e381-a90e-1d5e32b75932/reports.json\n", "Creating flattened csv: output/da7ff60e-233c-e381-a90e-1d5e32b75932/flattened.csv\n", "Creating flattened parquet: output/da7ff60e-233c-e381-a90e-1d5e32b75932/flattened.parquet\n", "Running batch number 6\n", "Created a valuation with ID: 6f044f3f-4e24-427e-a8c1-7c3a35f01019. Took 6.12 seconds\n", "Completed valuation with ID: 6f044f3f-4e24-427e-a8c1-7c3a35f01019. (number of assets 668). Took 33.91 seconds\n", "Saved report to output/6f044f3f-4e24-427e-a8c1-7c3a35f01019/reports.json\n", "Creating flattened csv: output/6f044f3f-4e24-427e-a8c1-7c3a35f01019/flattened.csv\n", "Creating flattened parquet: output/6f044f3f-4e24-427e-a8c1-7c3a35f01019/flattened.parquet\n", "Created a valuation with ID: 3b4423ac-ffd1-75d4-fe3f-da08598a6008. Took 4.95 seconds\n", "Completed valuation with ID: 3b4423ac-ffd1-75d4-fe3f-da08598a6008. (number of assets 668). Took 34.42 seconds\n", "Saved report to output/3b4423ac-ffd1-75d4-fe3f-da08598a6008/reports.json\n", "Creating flattened csv: output/3b4423ac-ffd1-75d4-fe3f-da08598a6008/flattened.csv\n", "Creating flattened parquet: output/3b4423ac-ffd1-75d4-fe3f-da08598a6008/flattened.parquet\n", "Created a valuation with ID: 155f302f-40a0-3c98-ad03-b45cd74d1eb5. Took 4.42 seconds\n", "Completed valuation with ID: 155f302f-40a0-3c98-ad03-b45cd74d1eb5. (number of assets 668). Took 35.33 seconds\n", "Saved report to output/155f302f-40a0-3c98-ad03-b45cd74d1eb5/reports.json\n", "Creating flattened csv: output/155f302f-40a0-3c98-ad03-b45cd74d1eb5/flattened.csv\n", "Creating flattened parquet: output/155f302f-40a0-3c98-ad03-b45cd74d1eb5/flattened.parquet\n", "Running batch number 7\n", "Created a valuation with ID: 24d2a3ff-73d4-ef66-b5a1-df51469ba6a8. Took 3.98 seconds\n", "Completed valuation with ID: 24d2a3ff-73d4-ef66-b5a1-df51469ba6a8. (number of assets 565). Took 35.02 seconds\n", "Saved report to output/24d2a3ff-73d4-ef66-b5a1-df51469ba6a8/reports.json\n", "Creating flattened csv: output/24d2a3ff-73d4-ef66-b5a1-df51469ba6a8/flattened.csv\n", "Creating flattened parquet: output/24d2a3ff-73d4-ef66-b5a1-df51469ba6a8/flattened.parquet\n", "Created a valuation with ID: f55814fa-252f-f856-486a-feb1beaefb03. Took 4.56 seconds\n", "Completed valuation with ID: f55814fa-252f-f856-486a-feb1beaefb03. (number of assets 565). Took 36.45 seconds\n", "Saved report to output/f55814fa-252f-f856-486a-feb1beaefb03/reports.json\n", "Creating flattened csv: output/f55814fa-252f-f856-486a-feb1beaefb03/flattened.csv\n", "Creating flattened parquet: output/f55814fa-252f-f856-486a-feb1beaefb03/flattened.parquet\n", "Created a valuation with ID: 882ec8a4-1b27-9aba-f830-a09d5454cace. Took 4.03 seconds\n", "Completed valuation with ID: 882ec8a4-1b27-9aba-f830-a09d5454cace. (number of assets 565). Took 33.35 seconds\n", "Saved report to output/882ec8a4-1b27-9aba-f830-a09d5454cace/reports.json\n", "Creating flattened csv: output/882ec8a4-1b27-9aba-f830-a09d5454cace/flattened.csv\n", "Creating flattened parquet: output/882ec8a4-1b27-9aba-f830-a09d5454cace/flattened.parquet\n", "Running batch number 8\n", "Created a valuation with ID: bbdd43e9-ca92-ac47-6e02-c7724cb1441b. Took 4.47 seconds\n", "Completed valuation with ID: bbdd43e9-ca92-ac47-6e02-c7724cb1441b. (number of assets 624). Took 33.09 seconds\n", "Saved report to output/bbdd43e9-ca92-ac47-6e02-c7724cb1441b/reports.json\n", "Creating flattened csv: output/bbdd43e9-ca92-ac47-6e02-c7724cb1441b/flattened.csv\n", "Creating flattened parquet: output/bbdd43e9-ca92-ac47-6e02-c7724cb1441b/flattened.parquet\n", "Created a valuation with ID: 93d7593b-d6e6-8869-0409-4c8cd5ff2119. Took 4.59 seconds\n", "Completed valuation with ID: 93d7593b-d6e6-8869-0409-4c8cd5ff2119. (number of assets 624). Took 35.02 seconds\n", "Saved report to output/93d7593b-d6e6-8869-0409-4c8cd5ff2119/reports.json\n", "Creating flattened csv: output/93d7593b-d6e6-8869-0409-4c8cd5ff2119/flattened.csv\n", "Creating flattened parquet: output/93d7593b-d6e6-8869-0409-4c8cd5ff2119/flattened.parquet\n", "Created a valuation with ID: a96e990b-5af5-faf9-ab67-9c9761b511cb. Took 4.44 seconds\n", "Completed valuation with ID: a96e990b-5af5-faf9-ab67-9c9761b511cb. (number of assets 624). Took 36.56 seconds\n", "Saved report to output/a96e990b-5af5-faf9-ab67-9c9761b511cb/reports.json\n", "Creating flattened csv: output/a96e990b-5af5-faf9-ab67-9c9761b511cb/flattened.csv\n", "Creating flattened parquet: output/a96e990b-5af5-faf9-ab67-9c9761b511cb/flattened.parquet\n", "Running batch number 9\n", "Created a valuation with ID: 1a756711-770a-6950-e9a0-bfd9920ccb2d. Took 4.32 seconds\n", "Completed valuation with ID: 1a756711-770a-6950-e9a0-bfd9920ccb2d. (number of assets 481). Took 27.96 seconds\n", "Saved report to output/1a756711-770a-6950-e9a0-bfd9920ccb2d/reports.json\n", "Creating flattened csv: output/1a756711-770a-6950-e9a0-bfd9920ccb2d/flattened.csv\n", "Creating flattened parquet: output/1a756711-770a-6950-e9a0-bfd9920ccb2d/flattened.parquet\n", "Created a valuation with ID: 82cb8bb7-fd5a-bdf0-a322-7548a396d952. Took 4.41 seconds\n", "Completed valuation with ID: 82cb8bb7-fd5a-bdf0-a322-7548a396d952. (number of assets 481). Took 24.58 seconds\n", "Saved report to output/82cb8bb7-fd5a-bdf0-a322-7548a396d952/reports.json\n", "Creating flattened csv: output/82cb8bb7-fd5a-bdf0-a322-7548a396d952/flattened.csv\n", "Creating flattened parquet: output/82cb8bb7-fd5a-bdf0-a322-7548a396d952/flattened.parquet\n", "Created a valuation with ID: 3bc85107-8692-84df-3bde-27595048a0cf. Took 5.12 seconds\n", "Completed valuation with ID: 3bc85107-8692-84df-3bde-27595048a0cf. (number of assets 481). Took 24.89 seconds\n", "Saved report to output/3bc85107-8692-84df-3bde-27595048a0cf/reports.json\n", "Creating flattened csv: output/3bc85107-8692-84df-3bde-27595048a0cf/flattened.csv\n", "Creating flattened parquet: output/3bc85107-8692-84df-3bde-27595048a0cf/flattened.parquet\n", "Running batch number 10\n", "Created a valuation with ID: 48c2d0d2-f7cb-ddce-80bb-1d0e603acd34. Took 3.81 seconds\n", "Completed valuation with ID: 48c2d0d2-f7cb-ddce-80bb-1d0e603acd34. (number of assets 453). Took 26.58 seconds\n", "Saved report to output/48c2d0d2-f7cb-ddce-80bb-1d0e603acd34/reports.json\n", "Creating flattened csv: output/48c2d0d2-f7cb-ddce-80bb-1d0e603acd34/flattened.csv\n", "Creating flattened parquet: output/48c2d0d2-f7cb-ddce-80bb-1d0e603acd34/flattened.parquet\n", "Created a valuation with ID: 471445c0-9502-c7aa-c18b-d8b231206a53. Took 3.95 seconds\n", "Completed valuation with ID: 471445c0-9502-c7aa-c18b-d8b231206a53. (number of assets 453). Took 23.13 seconds\n", "Saved report to output/471445c0-9502-c7aa-c18b-d8b231206a53/reports.json\n", "Creating flattened csv: output/471445c0-9502-c7aa-c18b-d8b231206a53/flattened.csv\n", "Creating flattened parquet: output/471445c0-9502-c7aa-c18b-d8b231206a53/flattened.parquet\n", "Created a valuation with ID: 92df56e7-0b4d-d45e-595b-d51015b7b65b. Took 4.17 seconds\n", "Completed valuation with ID: 92df56e7-0b4d-d45e-595b-d51015b7b65b. (number of assets 453). Took 24.86 seconds\n", "Saved report to output/92df56e7-0b4d-d45e-595b-d51015b7b65b/reports.json\n", "Creating flattened csv: output/92df56e7-0b4d-d45e-595b-d51015b7b65b/flattened.csv\n", "Creating flattened parquet: output/92df56e7-0b4d-d45e-595b-d51015b7b65b/flattened.parquet\n"]}], "source": ["price_decks = {\n", "    \"BASE\": \"dd4170e7-be13-4500-8bcd-b20a580aecb1|00gcd5xza61ty9ZWZ1d7\",\n", "    \"LOW\": \"89ef8a68-3ef2-43ba-858c-94534d907f63|00gcd5xza61ty9ZWZ1d7\",\n", "    \"HIGH\": \"54dd54a4-27fc-459f-94eb-14fff1e1c773|00gcd5xza61ty9ZWZ1d7\",\n", "}\n", "\n", "type = \"asset\"  # or \"company\"\n", "filters = {\n", "    # single company\n", "    # \"companyName\": {\n", "    #     \"name\": \"companyName\",\n", "    #     \"queryType\": \"terms\",\n", "    #     \"values\": [\"2202\"],\n", "    #     \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "    #     \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "    # }\n", "    # single field\n", "    # \"fieldNameUnique\": {\n", "    #     \"name\": \"fieldNameUnique\",\n", "    #     \"queryType\": \"terms\",\n", "    #     \"values\": [\"44\"],\n", "    #     \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "    #     \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "    # },\n", "    # every field in the world\n", "    \"superRegion\": {\n", "        \"queryType\": \"terms\",\n", "        \"values\": [\n", "            \"Africa\",\n", "            \"Antarctica\",\n", "            \"Asia\",\n", "            \"Europe\",\n", "            \"International Waters\",\n", "            \"Latin America and the Caribbean\",\n", "            \"Middle East\",\n", "            \"North America\",\n", "            \"Oceania\",\n", "            \"Russia and the Caspian\",\n", "        ],\n", "        \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "    },\n", "}\n", "\n", "# batches with all regions/all assets\n", "# batches = [\n", "#     {\n", "#         \"superRegion\": {\n", "#             \"queryType\": \"terms\",\n", "#             \"values\": [\n", "#                 \"Africa\",\n", "#                 \"Antarctica\",\n", "#                 \"Asia\",\n", "#             ],\n", "#             \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "#             \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "#         },\n", "#     },\n", "#     {\n", "#         \"superRegion\": {\n", "#             \"queryType\": \"terms\",\n", "#             \"values\": [\n", "#                 \"Europe\",\n", "#                 \"International Waters\",\n", "#                 \"Latin America and the Caribbean\",\n", "#             ],\n", "#             \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "#             \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "#         },\n", "#     },\n", "#     {\n", "#         \"superRegion\": {\n", "#             \"queryType\": \"terms\",\n", "#             \"values\": [\n", "#                 \"Middle East\",\n", "#                 \"North America\",\n", "#                 \"Oceania\",\n", "#                 \"Russia and the Caspian\",\n", "#             ],\n", "#             \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "#             \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "#         },\n", "#     },\n", "# ]\n", "\n", "# batches with top 50 companies\n", "# Based on:\n", "# SELECT\n", "#    company_name,\n", "#    id_company,\n", "#    COUNT(field_name) AS asset_count\n", "# FROM\n", "#    field_company_history\n", "# WHERE\n", "#    field_company_history.company_is_top_level = 'Y'\n", "#    AND field_interest_is_latest = 'Y'\n", "#    AND field_interest > 0\n", "#    AND field_gem_filename != ''\n", "#    AND company_name != 'Private Investors'\n", "# GROUP BY\n", "#    company_name, id_company\n", "# ORDER BY\n", "#    asset_count DESC\n", "# LIMIT 50;\n", "batches = [\n", "    {\n", "        \"companyName\": {\n", "            \"name\": \"companyName\",\n", "            \"queryType\": \"terms\",\n", "            \"values\": [\"41139\", \"144\", \"5251\", \"5783\"],\n", "            \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "            \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "    {\n", "        \"companyName\": {\n", "            \"name\": \"companyName\",\n", "            \"queryType\": \"terms\",\n", "            \"values\": [\"918\", \"3207\", \"30527\", \"869\", \"6842\"],\n", "            \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "            \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "    {\n", "        \"companyName\": {\n", "            \"name\": \"companyName\",\n", "            \"queryType\": \"terms\",\n", "            \"values\": [\"5024\", \"28038\", \"2065\", \"76063\", \"85616\"],\n", "            \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "            \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "    {\n", "        \"companyName\": {\n", "            \"name\": \"companyName\",\n", "            \"queryType\": \"terms\",\n", "            \"values\": [\"786\", \"3345\", \"3317\", \"17032\", \"12376\"],\n", "            \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "            \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "    {\n", "        \"companyName\": {\n", "            \"name\": \"companyName\",\n", "            \"queryType\": \"terms\",\n", "            \"values\": [\"5583\", \"12339\", \"4279\", \"1995\", \"2098\"],\n", "            \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "            \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "    {\n", "        \"companyName\": {\n", "            \"name\": \"companyName\",\n", "            \"queryType\": \"terms\",\n", "            \"values\": [\"6925\", \"5948\", \"26445\", \"609\", \"36150\"],\n", "            \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "            \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "    {\n", "        \"companyName\": {\n", "            \"name\": \"companyName\",\n", "            \"queryType\": \"terms\",\n", "            \"values\": [\"12817\", \"701\", \"3243\", \"35906\", \"2932\"],\n", "            \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "            \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "    {\n", "        \"companyName\": {\n", "            \"name\": \"companyName\",\n", "            \"queryType\": \"terms\",\n", "            \"values\": [\"8863\", \"1450\", \"31043\", \"27019\", \"5049\"],\n", "            \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "            \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "    {\n", "        \"companyName\": {\n", "            \"name\": \"companyName\",\n", "            \"queryType\": \"terms\",\n", "            \"values\": [\"13202\", \"525\", \"29177\", \"31641\", \"12703\"],\n", "            \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "            \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "    {\n", "        \"companyName\": {\n", "            \"name\": \"companyName\",\n", "            \"queryType\": \"terms\",\n", "            \"values\": [\"3202\", \"20124\", \"2004\", \"9592\", \"4201\", \"2000\"],\n", "            \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "            \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "        }\n", "    },\n", "]\n", "\n", "version = \"latest\"\n", "\n", "valuations = []\n", "\n", "for batch in batches:\n", "    filters = batch\n", "    print(f\"Running batch number {batches.index(batch) + 1}\")\n", "    for price_deck in price_decks:\n", "        post_body = {\n", "            \"type\": type,\n", "            \"filters\": filters,\n", "            \"priceDeckId\": price_decks[price_deck],\n", "            \"version\": version,\n", "        }\n", "        valuation_id = run_valuation(post_body)\n", "        save_valuation_report(\n", "            valuation_id, price_deck, f\"all-{type}-{price_deck}-{version}\"\n", "        )\n", "        valuations.append(valuation_id)\n", "\n", "if len(valuations) > 1:\n", "    combine_parquet_files(\n", "        [f\"output/{v}/flattened.parquet\" for v in valuations],\n", "        \"company-assets\",\n", "    )"]}, {"cell_type": "code", "execution_count": 132, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Schema({'record_id': String, 'company': String, 'country': String, 'business_area': String, 'regime': String, 'asset_name': String, 'valuation_type': String, 'price_scenario': String, 'Discount Date': Float64, 'Discount Rate': Float64, 'Total PV Post-tax': Float64, 'Total PV Pre-tax': Float64, 'Remaining PV Post-tax': Float64, 'Remaining PV Pre-tax': Float64, 'Remaining PV/boe Post-tax': Float64, 'Remaining PV/boe Pre-tax': Float64, 'Total Government Take Value': Float64, 'Total Government Take Percentage': Float64, 'Remaining Government Take Value': Float64, 'Remaining Government Take Percentage': Float64, 'P/I Ratio': Float64, 'Capex/boe': Float64, 'Opex/boe': Float64, 'Remaining Liquid Reserves WI (mmbbls)': Float64, 'Remaining Gas Reserves WI (bcf)': Float64, 'Total Remaining Reserves WI (mmboe)': Float64, 'Total Reserves WI (mmboe)': Float64, 'Post-tax IRR': Float64, 'Pre-tax IRR': Float64, 'Payback Period (years)': Float64, 'Reserve life at current production (years)': String, 'Corporate Income Tax Loss Position': Float64, 'Other Tax Loss Position': Float64, 'Tax Overhang': Float64, 'Remaining Liquid Reserves Ent. (mmbbls)': Float64, 'Remaining Gas Reserves Ent. (bcf)': Float64, 'Total Remaining Reserves Ent. (mmboe)': Float64, 'Total Reserves Ent. (mmboe)': Float64, 'Total Liquid Reserves Ent. (mmbbls)': Float64, 'Total Gas Reserves Ent. (bcf)': Float64, 'Total Liquid Reserves WI (mmbbls)': Float64, 'Total Gas Reserves WI (bcf)': Float64})\n"]}, {"data": {"text/html": ["<div><style>\n", ".dataframe > thead > tr,\n", ".dataframe > tbody > tr {\n", "  text-align: right;\n", "  white-space: pre-wrap;\n", "}\n", "</style>\n", "<small>shape: (50, 2)</small><table border=\"1\" class=\"dataframe\"><thead><tr><th>company</th><th>asset_count</th></tr><tr><td>str</td><td>u32</td></tr></thead><tbody><tr><td>&quot;BP&quot;</td><td>750</td></tr><tr><td>&quot;Shell&quot;</td><td>711</td></tr><tr><td>&quot;TotalEnergies&quot;</td><td>701</td></tr><tr><td>&quot;ExxonMobil&quot;</td><td>685</td></tr><tr><td>&quot;Eni&quot;</td><td>675</td></tr><tr><td>&hellip;</td><td>&hellip;</td></tr><tr><td>&quot;MOL&quot;</td><td>58</td></tr><tr><td>&quot;Orlen&quot;</td><td>44</td></tr><tr><td>&quot;Pemex&quot;</td><td>41</td></tr><tr><td>&quot;PGNiG&quot;</td><td>24</td></tr><tr><td>&quot;Romanian State&quot;</td><td>8</td></tr></tbody></table></div>"], "text/plain": ["shape: (50, 2)\n", "┌────────────────┬─────────────┐\n", "│ company        ┆ asset_count │\n", "│ ---            ┆ ---         │\n", "│ str            ┆ u32         │\n", "╞════════════════╪═════════════╡\n", "│ BP             ┆ 750         │\n", "│ Shell          ┆ 711         │\n", "│ TotalEnergies  ┆ 701         │\n", "│ ExxonMobil     ┆ 685         │\n", "│ Eni            ┆ 675         │\n", "│ …              ┆ …           │\n", "│ MOL            ┆ 58          │\n", "│ <PERSON><PERSON>          ┆ 44          │\n", "│ Pemex          ┆ 41          │\n", "│ PGNiG          ┆ 24          │\n", "│ Romanian State ┆ 8           │\n", "└────────────────┴─────────────┘"]}, "execution_count": 132, "metadata": {}, "output_type": "execute_result"}], "source": ["import polars as pl\n", "\n", "df = pl.read_parquet(\"output/company-assets_combined_20250124_161811.parquet\")\n", "\n", "print(df.schema)\n", "\n", "# result = pl.sql(\n", "#     \"\"\"\n", "#     SELECT COUNT(*)\n", "#     FROM df\n", "#     -- LIMIT 10\n", "#     \"\"\"\n", "# ).collect()\n", "\n", "result = pl.sql(\n", "    \"\"\"\n", "    SELECT company, count(asset_name) as asset_count\n", "    FROM df\n", "    WHERE price_scenario = 'BASE'\n", "    GROUP BY company\n", "    ORDER BY asset_count desc\n", "    \"\"\"\n", ").collect()\n", "\n", "result"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [], "source": ["from sentence_transformers import SentenceTransformer\n", "import os\n", "\n", "os.environ[\"CURL_CA_BUNDLE\"] = \"/etc/ssl/certs/zscaler.crt\"\n", "\n", "DEFAULT_MODEL = \"all-MiniLM-L6-v2\"\n", "\n", "\n", "def _get_model(model_name: str = DEFAULT_MODEL):\n", "    if not hasattr(_get_model, \"models\"):\n", "        _get_model.models = {}\n", "    if model_name not in _get_model.models:\n", "        _get_model.models[model_name] = SentenceTransformer(model_name)\n", "    return _get_model.models[model_name]\n", "\n", "\n", "def find_semantic_similarity(\n", "    model_output: str,\n", "    expected_output: str,\n", "    model_name: str = DEFAULT_MODEL,\n", "):\n", "    model = _get_model(model_name)\n", "    embeddings_in = model.encode(model_output)\n", "    embeddings_expected = model.encode(expected_output)\n", "    similarities = model.similarity(embeddings_in, embeddings_expected)\n", "    similarity_score = similarities.item()\n", "\n", "    return similarity_score\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import boto3\n", "import json\n", "\n", "bedrock_runtime = boto3.client(service_name=\"bedrock-runtime\")\n", "\n", "\n", "def invoke_bedrock_claude_model(\n", "    system_prompt, prompt, model_id=\"anthropic.claude-3-haiku-20240307-v1:0\"\n", "):\n", "    # Example cost: 60/1000*0.0008+2/1000*0.004 = 0.000056\n", "    request_body = {\n", "        \"anthropic_version\": \"bedrock-2023-05-31\",\n", "        \"max_tokens\": 500,\n", "        \"temperature\": 0.7,\n", "        \"system\": system_prompt,\n", "        \"messages\": [\n", "            {\"role\": \"user\", \"content\": prompt},\n", "        ],\n", "    }\n", "\n", "    response = bedrock_runtime.invoke_model(\n", "        modelId=model_id, body=json.dumps(request_body)\n", "    )\n", "\n", "    response_body = json.loads(response[\"body\"].read().decode(\"utf-8\"))\n", "    return response_body[\"content\"][0][\"text\"]\n", "\n", "\n", "def invoke_bedrock_nova_model(system_prompt, prompt, model_id=\"amazon.nova-micro-v1:0\"):\n", "    # Example cost: 60/1000*0.000035+2/1000*0.00014 = 0.00000238\n", "    request_body = {\n", "        \"schemaVersion\": \"messages-v1\",\n", "        \"messages\": [{\"role\": \"user\", \"content\": [{\"text\": prompt}]}],\n", "        \"system\": [{\"text\": system_prompt}],\n", "        \"inferenceConfig\": {\n", "            \"maxTokens\": 100,\n", "            \"topP\": 0.9,\n", "            \"temperature\": 0,\n", "        },\n", "    }\n", "\n", "    response = bedrock_runtime.invoke_model(\n", "        modelId=model_id, body=json.dumps(request_body)\n", "    )\n", "\n", "    response_body = json.loads(response[\"body\"].read().decode(\"utf-8\"))\n", "    return response_body[\"output\"][\"message\"][\"content\"][0][\"text\"]\n", "\n", "\n", "def invoke_bedrock_model(system_prompt: str, prompt: str, model_name: str):\n", "    if \"anthropic.claude\" in model_name:\n", "        return invoke_bedrock_claude_model(system_prompt, prompt, model_name)\n", "    elif \"amazon.nova\" in model_name:\n", "        return invoke_bedrock_nova_model(system_prompt, prompt, model_name)\n", "    else:\n", "        raise ValueError(f\"Model {model_name} not supported\")\n", "\n", "\n", "def find_llm_similarity(\n", "    model_output: str, expected_output: str, model_name: str = \"amazon.nova-micro-v1:0\"\n", "):\n", "    similarity_score = invoke_bedrock_model(\n", "        system_prompt=\"You will be comparing 2 sentences. Provide the similarity score between the 2 sentences focusing on fundamental meaning. Provide just a number. Do not provide any more details.\",\n", "        prompt=f\"a){model_output}\\nb){expected_output}\",\n", "        model_name=model_name,\n", "    )\n", "    return float(similarity_score)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Sentences to compare:\n", "SENTENCE_A:\n", "The value of the asset called Clair, based on the BASE price scenario, is as follows:\n", "\n", "┌────────────┬──────────────┬──────────────┬─────────────┬─────────────┬─────────────┬─────────────┐\n", "│ asset_name │ price_scenar │ Total PV     │ Total PV    │ Remaining   │ Remaining   │ Total       │\n", "│            │ io           │ Post-tax     │ Pre-tax     │ PV Post-tax │ PV Pre-tax  │ Remaining   │\n", "│            │              │              │             │             │             │ Reserves WI │\n", "│            │              │              │             │             │             │ (mmboe)     │\n", "╞════════════╪══════════════╪══════════════╪═════════════╪═════════════╪═════════════╪═════════════╡\n", "│ Clair      │ BASE         │ 4845.0836    │ 14579.9613  │ 6638.6896   │ 11766.4999  │ 661.5872    │\n", "└────────────┴──────────────┴──────────────┴─────────────┴─────────────┴─────────────┴─────────────┘\n", "\n", "The total post-tax value (Total PV Post-tax) of Clair is approximately $4,845 million, while its pre-tax value (Total PV Pre-tax) is about $14,580 million. The remaining post-tax value is $6,639 million, and the remaining pre-tax value is $11,766 million. The asset has total remaining reserves of about 662 million barrels of oil equivalent (mmboe)\n", "SENTENCE_B:\n", "The value of the asset called Blacktip, based on the BASE price scenario, is as follows:\n", "\n", "| Metric                  | Value (in millions) |\n", "|-------------------------|---------------------|\n", "| Total PV Post-tax       | -$735.61            |\n", "| Total PV Pre-tax        | -$608.39            |\n", "| Remaining PV Post-tax   | $113.25             |\n", "| Remaining PV Pre-tax    | $203.89             |\n", "\n", "=====\n", "The Blacktip asset is located in Australia and is part of the Upstream business area. It's important to note that while the total PV figures are negative, the remaining PV figures are positive, indicating that the asset may have future value despite past losses.\n", "\n", "Semantic similarity score: 0.6229\n", "LLM similarity score: 0.1000\n", "\n"]}], "source": ["# model_output = \"The value of Clair is approx $1.5 Billion USD\"\n", "# expected_output = \"The value of Clair is approx $1.5 Million USD\"\n", "# semantic_similarity_score = find_semantic_similarity(\n", "#     model_output=model_output,\n", "#     expected_output=expected_output,\n", "# )\n", "# llm_similarity_score = find_llm_similarity(\n", "#     model_output=model_output,\n", "#     expected_output=expected_output,\n", "#     model_name=\"amazon.nova-micro-v1:0\"\n", "# )\n", "\n", "# print(f\"Sentences to compare:\\n{model_output}\\n{expected_output}\\n\")\n", "# print(f\"Semantic similarity score: {semantic_similarity_score:.4f}\")\n", "# print(f\"LLM similarity score: {llm_similarity_score:.4f}\\n\")\n", "\n", "# model_output=\"The value of Clair is approx $1,500 Million USD\"\n", "# expected_output=\"The value of Clair is approx $1.5 Billion USD\"\n", "# semantic_similarity_score = find_semantic_similarity(\n", "#     model_output=model_output,\n", "#     expected_output=expected_output,\n", "# )\n", "# llm_similarity_score = find_llm_similarity(\n", "#     model_output=model_output,\n", "#     expected_output=expected_output,\n", "#     model_name=\"amazon.nova-micro-v1:0\"\n", "# )\n", "# print(f\"Sentences to compare:\\n{model_output}\\n{expected_output}\\n\")\n", "# print(f\"Semantic similarity score: {semantic_similarity_score:.4f}\")\n", "# print(f\"LLM similarity score: {llm_similarity_score:.4f}\\n\")\n", "\n", "model_output = \"The value of the asset called Clair, based on the BASE price scenario, is as follows:\\n\\n┌────────────┬──────────────┬──────────────┬─────────────┬─────────────┬─────────────┬─────────────┐\\n│ asset_name │ price_scenar │ Total PV     │ Total PV    │ Remaining   │ Remaining   │ Total       │\\n│            │ io           │ Post-tax     │ Pre-tax     │ PV Post-tax │ PV Pre-tax  │ Remaining   │\\n│            │              │              │             │             │             │ Reserves WI │\\n│            │              │              │             │             │             │ (mmboe)     │\\n╞════════════╪══════════════╪══════════════╪═════════════╪═════════════╪═════════════╪═════════════╡\\n│ Clair      │ BASE         │ 4845.0836    │ 14579.9613  │ 6638.6896   │ 11766.4999  │ 661.5872    │\\n└────────────┴──────────────┴──────────────┴─────────────┴─────────────┴─────────────┴─────────────┘\\n\\nThe total post-tax value (Total PV Post-tax) of Clair is approximately $4,845 million, while its pre-tax value (Total PV Pre-tax) is about $14,580 million. The remaining post-tax value is $6,639 million, and the remaining pre-tax value is $11,766 million. The asset has total remaining reserves of about 662 million barrels of oil equivalent (mmboe)\"\n", "expected_output = \"The value of the asset called Blacktip, based on the BASE price scenario, is as follows:\\n\\n| Metric                  | Value (in millions) |\\n|-------------------------|---------------------|\\n| Total PV Post-tax       | -$735.61            |\\n| Total PV Pre-tax        | -$608.39            |\\n| Remaining PV Post-tax   | $113.25             |\\n| Remaining PV Pre-tax    | $203.89             |\\n\\n=====\\nThe Blacktip asset is located in Australia and is part of the Upstream business area. It's important to note that while the total PV figures are negative, the remaining PV figures are positive, indicating that the asset may have future value despite past losses.\"\n", "semantic_similarity_score = find_semantic_similarity(\n", "    model_output=model_output,\n", "    expected_output=expected_output,\n", ")\n", "llm_similarity_score = find_llm_similarity(\n", "    model_output=model_output,\n", "    expected_output=expected_output,\n", "    model_name=\"amazon.nova-micro-v1:0\",\n", ")\n", "print(\n", "    f\"Sentences to compare:\\nSENTENCE_A:\\n{model_output}\\nSENTENCE_B:\\n{expected_output}\\n\"\n", ")\n", "print(f\"Semantic similarity score: {semantic_similarity_score:.4f}\")\n", "print(f\"LLM similarity score: {llm_similarity_score:.4f}\\n\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'BASE': '560eb327-d0a0-4690-b75e-a866a9a017a6|00gcd5xza61ty9ZWZ1d7',\n", " 'LOW': 'cb9a043a-60b4-4c29-a199-1d7348b8f5b3|00gcd5xza61ty9ZWZ1d7',\n", " 'HIGH': 'b2e8b27d-bec1-42bb-841c-02124ebde4a7|00gcd5xza61ty9ZWZ1d7'}"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "\n", "\n", "def fetch_price_decks():\n", "    url = \"https://api2.dev.woodmac.com/pricing/graphql\"\n", "    headers = {\"Cookie\": f\"iPlanetDirectoryPro={COOKIE}\"}\n", "\n", "    query = \"\"\"\n", "        query {\n", "        priceDecks {\n", "            name\n", "            id\n", "        }\n", "        }\n", "    \"\"\"\n", "\n", "    response = requests.post(url, json={\"query\": query}, headers=headers)\n", "\n", "    response.raise_for_status()\n", "    return response.json()\n", "\n", "\n", "price_deck_data = fetch_price_decks()\n", "all_price_decks = price_deck_data[\"data\"][\"priceDecks\"]\n", "\n", "price_decks = {}\n", "for deck in all_price_decks:\n", "    name = deck[\"name\"]\n", "    if name == \"Base\":\n", "        price_decks[\"BASE\"] = deck[\"id\"]\n", "    elif name == \"Low\":\n", "        price_decks[\"LOW\"] = deck[\"id\"]\n", "    elif name == \"High\":\n", "        price_decks[\"HIGH\"] = deck[\"id\"]\n", "\n", "price_decks"]}, {"cell_type": "code", "execution_count": 25, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Executing query: https://data.dev.woodmac.com/qe/v1/odata/upstream_weekly/field_company_history?$select=id_company,company_name,asset_count&$filter=company_is_top_level eq 'Y' and field_interest_is_latest eq 'Y' and field_interest ge 0 and field_gem_filename ne '' and company_name ne 'Private Investors'&$apply=groupby((company_name,id_company),aggregate(field_name with countdistinct as asset_count))&$orderby=asset_count desc\n", "Fetching additional page (1)...\n", "Retrieved 3453 company IDs\n"]}, {"data": {"text/plain": ["[('Shell', {'id': 918, 'asset_count': 1954}),\n", " ('Eni', {'id': 5783, 'asset_count': 1906}),\n", " ('TotalEnergies', {'id': 41139, 'asset_count': 1830}),\n", " ('BP', {'id': 144, 'asset_count': 1672}),\n", " ('ExxonMobil', {'id': 5251, 'asset_count': 1565}),\n", " ('ConocoPhillips', {'id': 6842, 'asset_count': 1126}),\n", " ('Sinopec Group', {'id': 5024, 'asset_count': 1020}),\n", " ('Chevron', {'id': 3207, 'asset_count': 999}),\n", " ('EGPC', {'id': 2876, 'asset_count': 976}),\n", " ('APA Corporation', {'id': 36150, 'asset_count': 960})]"]}, "execution_count": 25, "metadata": {}, "output_type": "execute_result"}], "source": ["import requests\n", "\n", "API_KEY = \"682afb60-4f87-11ed-ae7d-ad7b1677c753\"\n", "\n", "\n", "class LensDirectQueryBuilder:\n", "    def __init__(self, base_url, api_key):\n", "        self.base_url = base_url\n", "        self.api_key = api_key\n", "        self.select = []\n", "        self.filter = \"\"\n", "        self.orderby = \"\"\n", "        self.top = None\n", "        self.apply = \"\"\n", "\n", "    def with_select(self, fields):\n", "        self.select = fields\n", "        return self\n", "\n", "    def with_filter(self, filter_expression: str):\n", "        self.filter = filter_expression.replace(\"&\", \"%26\").replace(\"`\", \"'\")\n", "        return self\n", "\n", "    def with_order_by(self, order_expression):\n", "        self.orderby = order_expression\n", "        return self\n", "\n", "    def with_top(self, limit):\n", "        self.top = limit\n", "        return self\n", "\n", "    def with_aggregation(self, group_by_fields, aggregations):\n", "        group_by_part = f\"groupby(({','.join(group_by_fields)}),\"\n", "\n", "        aggregate_parts = [\n", "            f\"{agg['field']} with {agg['operation']} as {agg['alias']}\"\n", "            for agg in aggregations\n", "        ]\n", "\n", "        self.apply = f\"{group_by_part}aggregate({','.join(aggregate_parts)}))\"\n", "        return self\n", "\n", "    def build_url(self):\n", "        params = []\n", "\n", "        if self.select:\n", "            params.append(f\"$select={','.join(self.select)}\")\n", "\n", "        if self.filter:\n", "            params.append(f\"$filter={self.filter}\")\n", "\n", "        if self.apply:\n", "            params.append(f\"$apply={self.apply}\")\n", "\n", "        if self.orderby:\n", "            params.append(f\"$orderby={self.orderby}\")\n", "\n", "        if self.top is not None:\n", "            params.append(f\"$top={self.top}\")\n", "\n", "        return f\"{self.base_url}?{'&'.join(params)}\"\n", "\n", "    def execute(self):\n", "        url = self.build_url()\n", "        print(f\"Executing query: {url}\")\n", "        headers = {\"apikey\": self.api_key}\n", "        response = requests.get(url, headers=headers)\n", "        response.raise_for_status()\n", "        return response.json()\n", "\n", "\n", "def fetch_all_companies():\n", "    url = \"https://api2.dev.woodmac.com/valuations/api/v1/Companies/upstream\"\n", "    headers = {\"x-api-key\": \"test\"}\n", "\n", "    response = requests.get(url, headers=headers)\n", "    response.raise_for_status()\n", "\n", "    return [company[\"name\"] for company in response.json()[\"companies\"]]\n", "\n", "\n", "def fetch_company_ids():\n", "    company_ids = {}\n", "\n", "    url_builder = LensDirectQueryBuilder(\n", "        \"https://data.dev.woodmac.com/qe/v1/odata/upstream_weekly/field_company_history\",\n", "        API_KEY,\n", "    )\n", "    url_builder.with_select([\"id_company\", \"company_name\", \"asset_count\"])\n", "    filter_expr = (\n", "        \"company_is_top_level eq 'Y' and \"\n", "        \"field_interest_is_latest eq 'Y' and \"\n", "        \"field_interest ge 0 and \"\n", "        \"field_gem_filename ne '' and \"\n", "        \"company_name ne 'Private Investors'\"\n", "    )\n", "    url_builder.with_filter(filter_expr)\n", "    url_builder.with_aggregation(\n", "        [\"company_name\", \"id_company\"],\n", "        [\n", "            {\n", "                \"field\": \"field_name\",\n", "                \"operation\": \"countdistinct\",\n", "                \"alias\": \"asset_count\",\n", "            }\n", "        ],\n", "    )\n", "    url_builder.with_order_by(\"asset_count desc\")\n", "    data = url_builder.execute()\n", "\n", "    def update_company_ids(values):\n", "        for item in values:\n", "            company_ids[item[\"company_name\"]] = {\n", "                \"id\": item[\"id_company\"],\n", "                \"asset_count\": item[\"asset_count\"],\n", "            }\n", "\n", "    update_company_ids(data[\"value\"])\n", "    if data.get(\"@odata.nextLink\"):\n", "        next_url = data[\"@odata.nextLink\"]\n", "        count_pages = 0\n", "        while next_url:\n", "            count_pages += 1\n", "            print(f\"Fetching additional page ({count_pages})...\")\n", "            response = requests.get(next_url, headers={\"apikey\": API_KEY})\n", "            response.raise_for_status()\n", "            data = response.json()\n", "            update_company_ids(data[\"value\"])\n", "            next_url = data.get(\"@odata.nextLink\")\n", "\n", "    return company_ids\n", "\n", "\n", "company_id_map = fetch_company_ids()\n", "print(f\"Retrieved {len(company_id_map)} company IDs\")\n", "\n", "list(company_id_map.items())[:10]"]}, {"cell_type": "code", "execution_count": 24, "metadata": {}, "outputs": [], "source": ["def batch_companies_by_asset_count(company_id_map, max_assets=2000, max_companies=100):\n", "    batches = []\n", "    current_batch = []\n", "    current_asset_count = 0\n", "\n", "    for company_tuple in company_id_map.items():\n", "        _, info = company_tuple\n", "\n", "        if (\n", "            current_asset_count + info[\"asset_count\"] > max_assets\n", "            and current_batch\n", "            or len(current_batch) >= max_companies\n", "        ):\n", "            batches.append(current_batch)\n", "            current_batch = []\n", "            current_asset_count = 0\n", "\n", "        current_batch.append(company_tuple)\n", "        current_asset_count += info[\"asset_count\"]\n", "\n", "    if current_batch:\n", "        batches.append(current_batch)\n", "\n", "    return batches\n", "\n", "\n", "def convert_batches_to_filters(batches):\n", "    filter_batches = []\n", "\n", "    for batch in batches:\n", "        filter_batches.append(\n", "            {\n", "                \"companyName\": {\n", "                    \"name\": \"companyName\",\n", "                    \"queryType\": \"terms\",\n", "                    \"values\": [str(company_info[1][\"id\"]) for company_info in batch],\n", "                    \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "                    \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "                }\n", "            }\n", "        )\n", "\n", "    return filter_batches\n", "\n", "\n", "batches = batch_companies_by_asset_count(company_id_map)\n", "filter_batches = convert_batches_to_filters(batches)"]}, {"cell_type": "code", "execution_count": 73, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'companyName': {'name': 'companyName',\n", "  'queryType': 'terms',\n", "  'values': ['85609',\n", "   '31695',\n", "   '3275',\n", "   '95606',\n", "   '2920',\n", "   '3472',\n", "   '50522',\n", "   '11019',\n", "   '4608',\n", "   '28965',\n", "   '26472',\n", "   '5848',\n", "   '3851',\n", "   '4060',\n", "   '29772',\n", "   '10500',\n", "   '78034',\n", "   '9200',\n", "   '9642',\n", "   '69308',\n", "   '30000',\n", "   '3318',\n", "   '31075',\n", "   '6974',\n", "   '78345',\n", "   '12418',\n", "   '8237',\n", "   '7844',\n", "   '1865',\n", "   '29971',\n", "   '30435',\n", "   '5494',\n", "   '31359',\n", "   '12090',\n", "   '28984',\n", "   '74287',\n", "   '8786',\n", "   '28318',\n", "   '20606',\n", "   '7383',\n", "   '6684',\n", "   '1272',\n", "   '6778',\n", "   '20520',\n", "   '5165',\n", "   '25866',\n", "   '5478',\n", "   '8721',\n", "   '9683',\n", "   '14693',\n", "   '74497',\n", "   '20158',\n", "   '7349',\n", "   '9899',\n", "   '6164',\n", "   '14570',\n", "   '12429',\n", "   '5725',\n", "   '1636',\n", "   '8943',\n", "   '8341',\n", "   '3950',\n", "   '27168',\n", "   '6410',\n", "   '14585',\n", "   '5728',\n", "   '12329',\n", "   '25211',\n", "   '27412',\n", "   '10227',\n", "   '5546',\n", "   '5842',\n", "   '27961',\n", "   '672',\n", "   '5458',\n", "   '31575',\n", "   '4814',\n", "   '5702',\n", "   '27853',\n", "   '10490',\n", "   '6978',\n", "   '5722',\n", "   '4502',\n", "   '2628',\n", "   '7690',\n", "   '27288',\n", "   '5854',\n", "   '7971',\n", "   '4175',\n", "   '10079',\n", "   '2577',\n", "   '5058',\n", "   '6124',\n", "   '27606',\n", "   '97011',\n", "   '28911',\n", "   '19050',\n", "   '7624',\n", "   '3619',\n", "   '2069',\n", "   '20249',\n", "   '12328',\n", "   '4240',\n", "   '8182',\n", "   '78908',\n", "   '12188',\n", "   '41546',\n", "   '2777',\n", "   '20731',\n", "   '10014',\n", "   '2191',\n", "   '13319',\n", "   '26596',\n", "   '5658',\n", "   '28160',\n", "   '28146',\n", "   '41595',\n", "   '4527',\n", "   '27026',\n", "   '13436',\n", "   '20195',\n", "   '2176',\n", "   '4466',\n", "   '30906',\n", "   '15785',\n", "   '30433',\n", "   '7711',\n", "   '1697',\n", "   '4672',\n", "   '31148',\n", "   '10906',\n", "   '4607',\n", "   '28704',\n", "   '31147',\n", "   '7395',\n", "   '5192',\n", "   '27602',\n", "   '4071',\n", "   '2150',\n", "   '27855',\n", "   '6594',\n", "   '8583',\n", "   '9781',\n", "   '83495',\n", "   '7334',\n", "   '31791',\n", "   '5274',\n", "   '7309',\n", "   '8634',\n", "   '83140',\n", "   '11469',\n", "   '30747',\n", "   '93736',\n", "   '13115',\n", "   '8548',\n", "   '30086',\n", "   '9483',\n", "   '98126',\n", "   '5737',\n", "   '25214',\n", "   '8616',\n", "   '7476',\n", "   '5933',\n", "   '41764',\n", "   '3519',\n", "   '10391',\n", "   '29148',\n", "   '12736',\n", "   '8973',\n", "   '29867',\n", "   '9558',\n", "   '11200',\n", "   '9306',\n", "   '98631',\n", "   '12406',\n", "   '5132',\n", "   '2159',\n", "   '5134',\n", "   '6785',\n", "   '13316',\n", "   '17793',\n", "   '8441',\n", "   '13444',\n", "   '26900',\n", "   '7847',\n", "   '9079',\n", "   '4793',\n", "   '26003',\n", "   '9227',\n", "   '4773',\n", "   '9049',\n", "   '4210',\n", "   '2166',\n", "   '74635',\n", "   '99501',\n", "   '26467',\n", "   '2094',\n", "   '5927',\n", "   '4168',\n", "   '25718',\n", "   '19862',\n", "   '8138',\n", "   '4779',\n", "   '8988',\n", "   '25851',\n", "   '14705',\n", "   '2048',\n", "   '9879',\n", "   '30192',\n", "   '31584',\n", "   '1966',\n", "   '29126',\n", "   '12390',\n", "   '3319',\n", "   '99362',\n", "   '4363',\n", "   '10327',\n", "   '27828',\n", "   '3322',\n", "   '85640',\n", "   '17642',\n", "   '95604',\n", "   '2078',\n", "   '10925',\n", "   '7521',\n", "   '5427',\n", "   '6742',\n", "   '8284',\n", "   '5271',\n", "   '8617',\n", "   '29358',\n", "   '27581',\n", "   '4268',\n", "   '5262',\n", "   '9770',\n", "   '31386',\n", "   '31094',\n", "   '41226',\n", "   '7752',\n", "   '4332',\n", "   '2387',\n", "   '6354',\n", "   '26410',\n", "   '35898',\n", "   '13484',\n", "   '3930',\n", "   '85612',\n", "   '96999',\n", "   '6432',\n", "   '5519',\n", "   '2772',\n", "   '2771',\n", "   '17054',\n", "   '27690',\n", "   '3146',\n", "   '27374',\n", "   '9618',\n", "   '3797',\n", "   '28143',\n", "   '6333',\n", "   '94310',\n", "   '5602',\n", "   '11374',\n", "   '26062',\n", "   '41011',\n", "   '94311',\n", "   '8892',\n", "   '6983',\n", "   '11295',\n", "   '27691',\n", "   '30682',\n", "   '26431',\n", "   '7150',\n", "   '41332',\n", "   '6986',\n", "   '5133',\n", "   '5598',\n", "   '9861',\n", "   '13855',\n", "   '14535',\n", "   '5723',\n", "   '31542',\n", "   '30028',\n", "   '76223',\n", "   '13258',\n", "   '41281',\n", "   '6121',\n", "   '98606',\n", "   '20238',\n", "   '5856',\n", "   '6722',\n", "   '26921',\n", "   '31763',\n", "   '4248',\n", "   '12174',\n", "   '5057',\n", "   '30051',\n", "   '85247',\n", "   '26149',\n", "   '5876',\n", "   '26353',\n", "   '13559',\n", "   '3297',\n", "   '9097',\n", "   '30524',\n", "   '29356',\n", "   '12087',\n", "   '10256',\n", "   '95346',\n", "   '14764',\n", "   '25350',\n", "   '9064',\n", "   '25467',\n", "   '9053',\n", "   '4392',\n", "   '28263',\n", "   '19859',\n", "   '6640',\n", "   '7591',\n", "   '28580',\n", "   '5883',\n", "   '9184',\n", "   '10518',\n", "   '30134',\n", "   '28614',\n", "   '30375',\n", "   '6490',\n", "   '95643',\n", "   '6618',\n", "   '66320',\n", "   '5980',\n", "   '4541',\n", "   '6088',\n", "   '26133',\n", "   '31845',\n", "   '30680',\n", "   '9775',\n", "   '6469',\n", "   '29891',\n", "   '14762',\n", "   '29936',\n", "   '10512',\n", "   '85614',\n", "   '25580',\n", "   '5714',\n", "   '13056',\n", "   '36227',\n", "   '29889',\n", "   '73603',\n", "   '31150',\n", "   '19563',\n", "   '27632',\n", "   '9101',\n", "   '27891',\n", "   '25468',\n", "   '5660',\n", "   '11157',\n", "   '5768',\n", "   '9621',\n", "   '5785',\n", "   '25319',\n", "   '30731',\n", "   '30272',\n", "   '13332',\n", "   '9356',\n", "   '12731',\n", "   '5018',\n", "   '27539',\n", "   '95644',\n", "   '27603',\n", "   '9542',\n", "   '95640',\n", "   '4904',\n", "   '13591',\n", "   '13223',\n", "   '4129',\n", "   '7717',\n", "   '9526',\n", "   '28153',\n", "   '29699',\n", "   '94062',\n", "   '5769',\n", "   '5209',\n", "   '95641',\n", "   '13114',\n", "   '28055',\n", "   '28868',\n", "   '95639',\n", "   '79045',\n", "   '4565',\n", "   '6834',\n", "   '26910',\n", "   '5196',\n", "   '8858',\n", "   '2690',\n", "   '8059',\n", "   '4420',\n", "   '8694',\n", "   '27063',\n", "   '95605',\n", "   '20698',\n", "   '25819',\n", "   '27664',\n", "   '3294',\n", "   '26042',\n", "   '76136',\n", "   '83798',\n", "   '30454',\n", "   '6699',\n", "   '9751',\n", "   '5265',\n", "   '7262',\n", "   '5180',\n", "   '5153',\n", "   '25186',\n", "   '74300',\n", "   '4996',\n", "   '93648',\n", "   '28185',\n", "   '25683',\n", "   '79113',\n", "   '6642',\n", "   '9966',\n", "   '7317',\n", "   '13116',\n", "   '9685',\n", "   '31110',\n", "   '7323',\n", "   '7311',\n", "   '7489',\n", "   '7291',\n", "   '7908',\n", "   '10355',\n", "   '25185',\n", "   '4067',\n", "   '13566',\n", "   '6679',\n", "   '7338',\n", "   '12425',\n", "   '7256',\n", "   '8051',\n", "   '31478',\n", "   '10529',\n", "   '5995',\n", "   '8832',\n", "   '14734',\n", "   '9110',\n", "   '7310',\n", "   '7406',\n", "   '28346',\n", "   '10530',\n", "   '36011',\n", "   '5267',\n", "   '78891',\n", "   '7315',\n", "   '30874',\n", "   '10528',\n", "   '7336',\n", "   '12910',\n", "   '7324',\n", "   '7961',\n", "   '94228',\n", "   '13870',\n", "   '8829',\n", "   '31338',\n", "   '30894',\n", "   '10312',\n", "   '6221',\n", "   '6314',\n", "   '9699',\n", "   '13822',\n", "   '9972',\n", "   '6734',\n", "   '12636',\n", "   '8774',\n", "   '30460',\n", "   '10570',\n", "   '7092',\n", "   '27877',\n", "   '74428',\n", "   '31108',\n", "   '7072',\n", "   '31445',\n", "   '5997',\n", "   '5517',\n", "   '31858',\n", "   '10095',\n", "   '30898',\n", "   '30439',\n", "   '97050',\n", "   '9171',\n", "   '9262',\n", "   '10959',\n", "   '5501',\n", "   '10958',\n", "   '41356',\n", "   '31694',\n", "   '7501',\n", "   '8395',\n", "   '31321',\n", "   '28296',\n", "   '9993',\n", "   '10960',\n", "   '14735',\n", "   '20402',\n", "   '31730',\n", "   '27985',\n", "   '27204',\n", "   '4164',\n", "   '83141',\n", "   '30977',\n", "   '7124',\n", "   '5136',\n", "   '13520',\n", "   '30295',\n", "   '5607',\n", "   '11034',\n", "   '4272',\n", "   '30895',\n", "   '9409',\n", "   '7361',\n", "   '27163',\n", "   '8917',\n", "   '28088',\n", "   '6958',\n", "   '83260',\n", "   '6516',\n", "   '31618',\n", "   '99273',\n", "   '7119',\n", "   '30897',\n", "   '4951',\n", "   '7002'],\n", "  'invertFilter': <PERSON><PERSON><PERSON>,\n", "  'includeEmpty': False}}"]}, "execution_count": 73, "metadata": {}, "output_type": "execute_result"}], "source": ["def create_company_filter_batches(company_dict, target_asset_count=4000):\n", "    batches = []\n", "    current_batch = []\n", "    current_asset_count = 0\n", "\n", "    for company, info in company_dict.items():\n", "        if (\n", "            current_asset_count + info[\"asset_count\"] > target_asset_count\n", "            and current_batch\n", "        ):\n", "            batches.append(\n", "                {\n", "                    \"companyName\": {\n", "                        \"name\": \"companyName\",\n", "                        \"queryType\": \"terms\",\n", "                        \"values\": [\n", "                            str(comp_info[\"id\"]) for _, comp_info in current_batch\n", "                        ],\n", "                        \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "                        \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "                    }\n", "                }\n", "            )\n", "            current_batch = []\n", "            current_asset_count = 0\n", "\n", "        current_batch.append((company, info))\n", "        current_asset_count += info[\"asset_count\"]\n", "\n", "    if current_batch:\n", "        batches.append(\n", "            {\n", "                \"companyName\": {\n", "                    \"name\": \"companyName\",\n", "                    \"queryType\": \"terms\",\n", "                    \"values\": [str(comp_info[\"id\"]) for _, comp_info in current_batch],\n", "                    \"invertFilter\": <PERSON><PERSON><PERSON>,\n", "                    \"includeEmpty\": <PERSON><PERSON><PERSON>,\n", "                }\n", "            }\n", "        )\n", "\n", "    return batches\n", "\n", "\n", "batches = create_company_filter_batches(sorted_companies, 4000)\n", "\n", "# print(f\"Created {len(batches)} batches\")\n", "# for i, batch in enumerate(batches):\n", "#     companies_in_batch = batch[\"companyName\"][\"values\"]\n", "#     print(\n", "#         f\"Batch {i + 1}: {len(companies_in_batch)} companies. company IDs (top 10): {companies_in_batch[:10]}\"\n", "#     )\n", "\n", "batches[-1]"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.1"}}, "nbformat": 4, "nbformat_minor": 2}
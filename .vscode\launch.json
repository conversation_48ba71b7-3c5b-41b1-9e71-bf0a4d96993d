{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch Orchestrator Api",
            "program": "${workspaceFolder}/packages/orchestrator/dist/server.cjs",
            "request": "launch",
            "outFiles": [
                "${workspaceFolder}/**/*.cjs",
                "!**/node_modules/**"
            ],
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node",
            "cwd": "${workspaceFolder}/packages/orchestrator",
            "envFile": "${workspaceFolder}/packages/orchestrator/.env",
            "console": "integratedTerminal",
        },
        {
            "name": "Run generator.py with scenario all_upstream_assets",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/packages/data-ingestion-utils/generator.py",
            "args": [
                "--scenario", "all_upstream_assets",
                "--combine"
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "Run generator.py with scenario all_upstream_companies",
            "type": "debugpy",
            "request": "launch",
            "cwd": "${workspaceFolder}/packages/data-ingestion-utils",
            "program": "${workspaceFolder}/packages/data-ingestion-utils/generator.py",
            "args": [
                "--scenario", "all_upstream_companies",
                "--combine"
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "Run generator.py with scenario all_upstream_assets and no-include-lens-metadata",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/packages/data-ingestion-utils/generator.py",
            "args": [
                "--scenario", "all_upstream_assets",
                "--combine",
                "--no-include-lens-metadata"
            ],
            "console": "integratedTerminal"
        },
        {
            "name": "Run generator.py with reports_dir output",
            "type": "debugpy",
            "request": "launch",
            "cwd": "${workspaceFolder}/packages/data-ingestion-utils",
            "program": "${workspaceFolder}/packages/data-ingestion-utils/generator.py",
            "args": [
                "--scenario", "all_upstream_assets",
                "--combine",
                "--reports-dir", "${workspaceFolder}/packages/data-ingestion-utils/output"
            ],
            "console": "integratedTerminal"
        }
    ]
}
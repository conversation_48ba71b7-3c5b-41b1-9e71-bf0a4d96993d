import React from "react";
import { render, screen } from "@testing-library/react";
import userEvent from "@testing-library/user-event";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { ChatEntry } from "../ChatEntry";

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("ChatEntry", () => {
  const mockOnChatMessageEntered = jest.fn();

  beforeEach(() => {
    mockOnChatMessageEntered.mockClear();
  });

  it("renders textarea and send button", () => {
    renderWithProvider(<ChatEntry onChatMessageEntered={mockOnChatMessageEntered} />);

    expect(screen.getByPlaceholderText("Ask question here")).toBeInTheDocument();
    expect(screen.getByRole("button")).toBeInTheDocument();
  });

  it("send button is disabled when message is empty", () => {
    renderWithProvider(<ChatEntry onChatMessageEntered={mockOnChatMessageEntered} />);

    const sendButton = screen.getByRole("button");
    expect(sendButton).toBeDisabled();
  });

  it("send button is disabled when message is only whitespace", async () => {
    const user = userEvent.setup();
    renderWithProvider(<ChatEntry onChatMessageEntered={mockOnChatMessageEntered} />);

    const textarea = screen.getByPlaceholderText("Ask question here");
    await user.type(textarea, "   ");

    const sendButton = screen.getByRole("button");
    expect(sendButton).toBeDisabled();
  });

  it("send button is enabled when message has content", async () => {
    const user = userEvent.setup();
    renderWithProvider(<ChatEntry onChatMessageEntered={mockOnChatMessageEntered} />);

    const textarea = screen.getByPlaceholderText("Ask question here");
    await user.type(textarea, "Hello world");

    const sendButton = screen.getByRole("button");
    expect(sendButton).not.toBeDisabled();
  });

  it("calls onChatMessageEntered and clears message when send button is clicked", async () => {
    const user = userEvent.setup();
    renderWithProvider(<ChatEntry onChatMessageEntered={mockOnChatMessageEntered} />);

    const textarea = screen.getByPlaceholderText("Ask question here");
    await user.type(textarea, "Test message");

    const sendButton = screen.getByRole("button");
    await user.click(sendButton);

    expect(mockOnChatMessageEntered).toHaveBeenCalledWith("Test message");
    expect(textarea).toHaveValue("");
  });

  it("calls onChatMessageEntered and clears message when Enter key is pressed", async () => {
    const user = userEvent.setup();
    renderWithProvider(<ChatEntry onChatMessageEntered={mockOnChatMessageEntered} />);

    const textarea = screen.getByPlaceholderText("Ask question here");
    await user.type(textarea, "Test message");
    await user.keyboard("{Enter}");

    expect(mockOnChatMessageEntered).toHaveBeenCalledWith("Test message");
    expect(textarea).toHaveValue("");
  });

  it("updates textarea value when typing", async () => {
    const user = userEvent.setup();
    renderWithProvider(<ChatEntry onChatMessageEntered={mockOnChatMessageEntered} />);

    const textarea = screen.getByPlaceholderText("Ask question here");
    await user.type(textarea, "Test typing");

    expect(textarea).toHaveValue("Test typing");
  });
});

#!/usr/bin/env groovy
@Library('utils') _
def sys = "NA"

AWS_REGION = 'us-east-1'

String getEnvironment() {
    if (params.TARGET_ENVIRONMENT != null) {
        return params.TARGET_ENVIRONMENT
    }
    if (woodmac.getJenkinsEnvironment() == 'prod') {
        return 'int'
    }

    return 'dev'
}

def getAvailableEnvironments() {
    if (woodmac.getJenkinsEnvironment() == 'dev') {
        return ['dev']
    }
    return ['int', 'uat', 'prod']
}

def getCloudfrontDistribution(name) {
    sh(label: 'Get distribution id', script: "aws cloudfront list-distributions --query \"DistributionList.Items[*].{id:Id,origin:Origins.Items[0].Id}[?origin=='${name}.s3.us-east-1.amazonaws.com'].id\" --output text", returnStdout: true).trim()
}

def getIpSet(name) {
    // these are not yet available from the environment mapping lambda
    sh(label: "Get ${name} IP Set", script: "aws wafv2 list-ip-sets --scope CLOUDFRONT --region us-east-1  --query \"IPSets[?Name=='${name}'].ARN\" --output text", returnStdout: true).trim()
}

def getPrivateLink() {
    // stored in /ai[env]/ai[env]/orchestrator-endpoint-cname-env
    // in ai accounts
    env = getEnvironment()
    if (env == 'dev') {
        return 'vpce-071f0841bd375f022-kmf9sxf8.vpce-svc-0e8f75a5fcd7e1733.us-east-1.vpce.amazonaws.com'
    }
    if (env == 'int') {
        return 'vpce-0b21f912e497d80ec-86wnkvws.vpce-svc-058f58af9c745a7ee.us-east-1.vpce.amazonaws.com'
    }
    if (env == 'uat') {
        return 'vpce-0f4a80e0543fa09f1-jio67p6n.vpce-svc-02c4ad5b6bcbe1341.us-east-1.vpce.amazonaws.com'
    }
    if (env == 'prod') {
        return 'vpce-0cd6107a858d7eaf8-gcecs871.vpce-svc-033d632e7b7ef4516.us-east-1.vpce.amazonaws.com'
    }
    return null
}

def handleCloudFormationError(stackName) {
    echo "CloudFormation deployment failed for stack: ${stackName}"
    
    def stackEvents = sh(
        script: """
        aws cloudformation describe-stack-events --stack-name ${stackName} --region ${AWS_REGION} --query "reverse(sort_by(StackEvents[?contains(ResourceStatus, 'FAILED')], &Timestamp))[:10].[{Status:ResourceStatus, Reason:ResourceStatusReason, LogicalID:LogicalResourceId, Timestamp:Timestamp}]" --output json
        """,
        returnStdout: true
    ).trim()
    
    echo "Stack failure details:"
    echo stackEvents
    
    def stackStatus = sh(
        script: """
        aws cloudformation describe-stacks --stack-name ${stackName} --region ${AWS_REGION} --query "Stacks[0].StackStatus" --output text""",
        returnStdout: true
    ).trim()
    
    echo "Overall stack status: ${stackStatus}"
    
    error "CloudFormation stack deployment failed. See above for error details."
}

environmentName = getEnvironment()
AGENT_IAM_ROLE_PARAMETER_NAME = "/${environmentName}/wmlgv-assistant-agent/deployment-agent-role-name"

jenkinsAgent = "harbor.prod.woodmac.com/liveservices/jenkins-node-jnlp-docker:3256.v88a_f6e922152-4-alpine-jdk21"

properties([
    parameters([
        choice(name: 'TARGET_ENVIRONMENT', description: 'Environment to deploy', choices: getAvailableEnvironments()),
    ])
])

pipeline {
    agent {
        ecs {
            inheritFrom "dynamic-us-east-1-${environmentName}"
            taskrole woodmac.getAgentRole(
                region: "us-east-1",
                environment: "${environmentName}",
                parameterName: "${AGENT_IAM_ROLE_PARAMETER_NAME}",
            )
            image "${jenkinsAgent}"
            memory "2024"
            cpu "1024"
        }
    }
    options {
        disableConcurrentBuilds()
        ansiColor('xterm')
    }
    environment {
        SHARED_BUCKET_NAME = "wmim-${environmentName}-valuations-excel-use1"
        BUCKET_NAME = "wmlgv-${environmentName}-assistant-addin"
        BUILD_ENVIRONMENT = "${environmentName}"
        PACKAGE_NAME = "deployment-package.tar.gz"
        NODE_BUILD_ENV = 'ci'
        AWS_ENVIRONMENT = "${environmentName}"
        STACK_NAME = "wmlgv-assistant-addin-${environmentName}"
        TEMPLATE_FILE = "../infra/add-in-resources.yaml"
        INCAPSULA_IP_SET_ARN = "${getIpSet('incapsula-whitelist-cf-v2')}"
        WOODMAC_INTERNAL_IP_SET_ARN = "${getIpSet('woodmac-internal-cf-v2')}"
    }
    stages {
        stage('Env') {
            steps {
                sh 'printenv'
            }
        }
        stage('Install Packages') {
            when {
                branch "main"
            }
            steps {
                echo "Downloading nexus packages"

                script {
                    sh(
                        label: 'Pull packages from Nexus',
                        script: """
                            curl -o "./${PACKAGE_NAME}" "https://nexus.prod.woodmac.com/repository/lens-internal-modelling/wmlgv/assistant-addin/${PACKAGE_NAME}"
                            tar xvfz "./${PACKAGE_NAME}" --touch -C "./"
                        """
                    )
                }
            }
        }
        // can remove shared infra steps once migrated fully to new infra
        stage('Deploy Add-in to shared IM infra') {
            when {
                branch "main"
            }
            steps {
                echo "Deploying add-in to S3 bucket ${SHARED_BUCKET_NAME}..."

                sh """
                    aws s3 sync dist/ s3://${SHARED_BUCKET_NAME}/wmlgv-assistant --exclude '*taskpane.html'  --exclude 'login.html' --exclude 'callback' --exclude 'config/*' --exclude 'config.js' --delete
                    aws s3 cp dist/taskpane.html s3://${SHARED_BUCKET_NAME}/wmlgv-assistant/taskpane.html  --cache-control 'no-cache, no-store, max-age=0, must-revalidate'
                    aws s3 cp dist/login.html s3://${SHARED_BUCKET_NAME}/wmlgv-assistant/login.html  --cache-control 'no-cache, no-store, max-age=0, must-revalidate'
                    aws s3 cp dist/login/callback s3://${SHARED_BUCKET_NAME}/wmlgv-assistant/login/callback --content-type 'text/html'  --cache-control 'no-cache, no-store, max-age=0, must-revalidate'
                    aws s3 cp dist/config/config.${BUILD_ENVIRONMENT}.js s3://${SHARED_BUCKET_NAME}/wmlgv-assistant/login/config.js --cache-control 'no-cache, no-store, max-age=0, must-revalidate'
                    aws s3 cp dist/config/config.${BUILD_ENVIRONMENT}.js s3://${SHARED_BUCKET_NAME}/wmlgv-assistant/config.js --cache-control 'no-cache, no-store, max-age=0, must-revalidate'
                """
            }
        }
        stage('Invalidate Shared IM Cache') {
            when {
                branch "main"
            }
            environment {
                // don't get until needed for initial deploy where it won't have been created
                CLOUDFRONT_DIST = "${getCloudfrontDistribution("wmim-${environmentName}-valuations-excel-use1")}"
            }
            steps {
                echo "invalidating cloudfront cache ${CLOUDFRONT_DIST}..."
                sh """
                    aws cloudfront create-invalidation --distribution-id ${CLOUDFRONT_DIST} --paths "/*"
                """
            }
        }
        stage('Deploy Add-in Infrastucture') {
            when {
                branch "main"
            }
            steps {
                dir('jenkins') {
                    script {
                    sh 'make lint'
                        try {
                            sh "make deploy EXTRA_PARAMS=\"IncapsulaIpSetArn=${INCAPSULA_IP_SET_ARN} WoodmacInternalIpSetArn=${WOODMAC_INTERNAL_IP_SET_ARN}\""
                        } catch (Exception e) {
                            handleCloudFormationError(env.STACK_NAME)
                        }
                    }
                }
            }
        }
        stage('Deploy Add-in') {
            when {
                branch "main"
            }
            steps {
                echo "Deploying add-in to S3 bucket ${BUCKET_NAME}..."

                sh """
                    aws s3 sync dist/ s3://${BUCKET_NAME}/ --exclude 'taskpane.html'  --exclude 'login.html' --exclude 'callback' --exclude 'config/*' --exclude 'config.js' --delete
                    aws s3 cp dist/taskpane.html s3://${BUCKET_NAME}/taskpane.html  --cache-control 'no-cache, no-store, max-age=0, must-revalidate'
                    aws s3 cp dist/login.html s3://${BUCKET_NAME}/login.html  --cache-control 'no-cache, no-store, max-age=0, must-revalidate'
                    aws s3 cp dist/login/callback s3://${BUCKET_NAME}/login/callback --content-type 'text/html'  --cache-control 'no-cache, no-store, max-age=0, must-revalidate'
                    aws s3 cp dist/config/config.${BUILD_ENVIRONMENT}.js s3://${BUCKET_NAME}/login/config.js --cache-control 'no-cache, no-store, max-age=0, must-revalidate'
                    aws s3 cp dist/config/config.${BUILD_ENVIRONMENT}.js s3://${BUCKET_NAME}/config.js --cache-control 'no-cache, no-store, max-age=0, must-revalidate'
                """
            }
        }
        stage('Invalidate Cache') {
            when {
                branch "main"
            }
            environment {
                // don't get until needed for initial deploy where it won't have been created
                CLOUDFRONT_DIST = "${getCloudfrontDistribution("wmlgv-${environmentName}-assistant-addin")}"
            }
            steps {
                echo "invalidating cloudfront cache ${CLOUDFRONT_DIST}..."
                sh """
                    aws cloudfront create-invalidation --distribution-id ${CLOUDFRONT_DIST} --paths "/*"
                """
            }
        }
        stage('Sync Kong') {
            when {
                branch "main"
                expression {
                    getPrivateLink() != null
                }
            }
            environment {
                ENVIRONMENT = "${getEnvironment()}"
                KONNECT_CONTROLPLANE_NAME = "cp-woodmac-valuations-${getEnvironment()}"
                DECK_PRIVATE_LINK = "${getPrivateLink()}"
            }
            steps {
                sh(script: """#!/bin/bash
                    curl -o api-tool.sh https://nexus.prod.woodmac.com/repository/api-gateway/api-gateway-configuration-konnect/api-tool.sh && \
                    chmod +x api-tool.sh""",
                label: "Get api tool")

                sh(script:"""       
                    aws secretsmanager get-secret-value \
                        --region=us-east-1 \
                        --secret-id "/api-gateway/configuration/$KONNECT_CONTROLPLANE_NAME" \
                        --query "SecretString" \
                        --output text | jq -r '.RbacToken' >  kong_token

                    TOKEN_FILE=${pwd()}/kong_token ./api-tool.sh sync \
                        -e $ENVIRONMENT \
                        -E DECK_PRIVATE_LINK=$DECK_PRIVATE_LINK \
                        -s ${pwd()}/kong-config/kong.yaml \
                        -c $KONNECT_CONTROLPLANE_NAME""",
                label: "Sync configuration")
            }
        }
    }
}

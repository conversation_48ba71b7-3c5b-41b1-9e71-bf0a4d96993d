AWSTemplateFormatVersion: '2010-09-09'
Description: Deployment buckets for VAI
Parameters:
  Environment:
    Description: The environment in which the stack is deployed
    Type: String
    Default: aidev
    AllowedValues: [aidev, aiint, aiuat, aiprod]
  BusinessUnit:
    Type: String
    Default: Woodmac
  Contact:
    Description: Contact for stack owner(s)
    Type: String
    Default: '<EMAIL>'
    ConstraintDescription: Must be a valid email address.
  ProductCode:
    Description: Product code
    Type: String
    Default: 'wmlgv'
  ProjectCode:
    Description: Project code
    Type: String
    Default: 'DEV-VAL-SERV'
  Application:
    Description: Application code
    Type: String
    Default: 'valuations-ai'

Conditions:
  IsDevEnvironment:
    !Equals 
      - !Ref Environment
      - aidev

Resources:
  LambdaDeploymentBucket:
    Type: 'AWS::S3::Bucket'
    Properties:
      BucketName: !Sub "${ProductCode}-${Environment}-assistant-lambda-deployment"
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: Application
          Value: !Ref Application

  ValuationAIDataBucket:
    Type: 'AWS::S3::Bucket'
    Properties:
      BucketName: !Sub "${ProductCode}-${Environment}-assistant-valuations-data"
      Tags:
        - Key: BusinessUnit
          Value: !Ref BusinessUnit
        - Key: Contact
          Value: !Ref Contact
        - Key: Environment
          Value: !Ref Environment
        - Key: ProductCode
          Value: !Ref ProductCode
        - Key: ProjectCode
          Value: !Ref ProjectCode
        - Key: Application
          Value: !Ref Application

  ValuationAIDataBucketPolicy:
    Type: AWS::S3::BucketPolicy
    Condition: IsDevEnvironment
    Properties:
      Bucket: !Sub "${ProductCode}-${Environment}-assistant-valuations-data"
      PolicyDocument:
        Statement:
          -
            Action:
              - "s3:Get*"
              - "s3:List*"
            Effect: "Allow"
            Resource:
              - !Sub "arn:aws:s3:::${ProductCode}-${Environment}-assistant-valuations-data"
              - !Sub "arn:aws:s3:::${ProductCode}-${Environment}-assistant-valuations-data/*"
            Principal:
              AWS: 
                - 'arn:aws:iam::816069142167:root' # aiint
                - 'arn:aws:iam::908027391476:root' # aiuat
                - 'arn:aws:iam::221082172744:root' # aiprod

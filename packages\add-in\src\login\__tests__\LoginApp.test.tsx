import React from "react";
import { render, screen } from "@testing-library/react";
import { LoginApp } from "../LoginApp";

jest.mock("../LoginWithOkta", () => ({
  LoginWithOkta: () => <div data-testid="login-with-okta">Login with Okta</div>,
}));

jest.mock("../Callback", () => ({
  Callback: () => <div data-testid="callback">Callback Component</div>,
}));

jest.mock("../../security", () => ({
  oktaClient: {
    authStateManager: {
      getAuthState: jest.fn(),
    },
  },
}));

jest.mock("@okta/okta-react", () => ({
  Security: ({ children }: any) => <div data-testid="security-wrapper">{children}</div>,
}));

const mockLocation = { pathname: "/" };

jest.mock("react-router-dom", () => ({
  BrowserRouter: ({ children }: any) => <div data-testid="router">{children}</div>,
  Switch: ({ children }: any) => <div data-testid="switch">{children}</div>,
  Route: ({ path, component: Component }: any) => {
    const shouldRender =
      mockLocation.pathname === path ||
      (path === "/" && !["/login/callback", "/wmlgv-assistant/login/callback"].includes(mockLocation.pathname));
    return shouldRender ? <Component /> : null;
  },
}));

describe("LoginApp", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockLocation.pathname = "/";
  });

  it("renders the router structure", () => {
    render(<LoginApp />);

    expect(screen.getByTestId("router")).toBeInTheDocument();
    expect(screen.getByTestId("security-wrapper")).toBeInTheDocument();
    expect(screen.getByTestId("switch")).toBeInTheDocument();
  });

  it("renders LoginWithOkta component for root path", () => {
    mockLocation.pathname = "/";
    render(<LoginApp />);

    expect(screen.getByTestId("login-with-okta")).toBeInTheDocument();
  });

  it("renders Callback component for /login/callback path", () => {
    mockLocation.pathname = "/login/callback";
    render(<LoginApp />);

    expect(screen.getByTestId("callback")).toBeInTheDocument();
  });

  it("renders Callback component for /wmlgv-assistant/login/callback path", () => {
    mockLocation.pathname = "/wmlgv-assistant/login/callback";
    render(<LoginApp />);

    expect(screen.getByTestId("callback")).toBeInTheDocument();
  });
});

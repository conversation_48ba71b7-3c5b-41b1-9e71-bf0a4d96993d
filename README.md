# WoodMac Lens GV Assistant

This repository contains multiple packages that together form the wmlgv assistant - a set of tools for accessing, processing, and interacting with valuation data via an LLM.

## Project Structure

The project consists of the following packages:

-   [**orchestrator**](./packages/orchestrator): API service that routes client requests to appropriate backend services
-   [**agent**](./packages/agent): AWS Lambda function for handling AI agent requests for data queries
-   [**add-in**](./packages/add-in): Microsoft Office Add-in for Excel integration
-   [**data-ingestion-utils**](./packages/data-ingestion-utils): Utilities for ingesting, processing, and flattening valuation data

### Notebooks

The project includes the following Jupyter notebooks:

-   [**notebook.ipynb**](./notebook.ipynb): Agent testing notebook that demonstrates interactions with the AWS Bedrock Agent. Contains code for querying company asset data, processing valuation data with Polars DataFrame operations, and examples of typical queries like "what are the top 10 exxon assets by value?".

-   [**ld-notebook.ipynb**](./ld-notebook.ipynb): Lens Data exploration notebook for accessing and processing WoodMac data sources. Contains utilities for querying the Lens Data registry, exploring available tables (especially markets data), and downloading data from S3 as Parquet files.

## Local Development

See each package's README file for instructions on how to run them locally.

For Python package management and version setup, see [PYTHON_SETUP.md](./PYTHON_SETUP.md).

## Getting Started

1. Clone this repository
2. Set up Python environment (see [PYTHON_SETUP.md](./PYTHON_SETUP.md))
3. Install dependencies for each package
4. Configure environment variables (see each package's README)
5. Start services according to each package's instructions

## Prerequisites

-   Node.js (v20 or higher, use [nvm](https://github.com/nvm-sh/nvm) to ensure you're on the correct version)
-   Python 3.12+
-   AWS CLI (for deployments)
-   Docker (for containerized deployments)

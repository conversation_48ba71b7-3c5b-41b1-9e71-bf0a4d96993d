AWSTemplateFormatVersion: '2010-09-09'
Description: Jenkins Agent Role Setup
Parameters:
  Environment:
    Description: The environment in which the stack is deployed
    Type: String
    Default: dev
    AllowedValues: [dev, int, uat, prod]
  PermissionsBoundaryPolicy:
    Type: String
    Default: VA-PB-Standard
  BaseJenkinsAgentPolicyArn:
    Description: "The Arn of the base jenkins agent policy arn fetched from parameter store"
    Type: "AWS::SSM::Parameter::Value<String>"
    Default: "/dev/liveservices/jenkins-ecs-agents/base-jenkins-agent-policy-arn"

Resources:
  DeploymentRole:
    Type: AWS::IAM::Role
    Properties:
      AssumeRolePolicyDocument:
        Version: '2012-10-17'
        Statement:
          - Effect: Allow
            Principal:
              Service: 'ecs-tasks.amazonaws.com'
            Action: ['sts:AssumeRole']
      Path: /
      ManagedPolicyArns:
        - !Ref BaseJenkinsAgentPolicyArn
      PermissionsBoundary: !Sub 'arn:aws:iam::${AWS::AccountId}:policy/${PermissionsBoundaryPolicy}'
      Policies:
        - PolicyName: !Sub 'wmlgv-assistant-deploy-policy-${Environment}'
          PolicyDocument:
            Version: '2012-10-17'
            Statement:
              - Sid: CloudformationPermissions
                Effect: Allow
                Action:
                  - cloudformation:Create*
                  - cloudformation:Describe*
                  - cloudformation:Execute*
                  - cloudformation:Get*
                Resource:
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stack/wmlgv-assistant-addin-*/*'
                  - !Sub 'arn:aws:cloudformation:${AWS::Region}:${AWS::AccountId}:stackset/wmlgv-assistant-addin-*/*'
              - Sid: S3DeployArtifacts
                Effect: Allow
                Action:
                  - s3:CreateBucket
                  - s3:PutBucketTagging
                  - s3:PutEncryptionConfiguration
                  - s3:PutBucketPublicAccessBlock
                  - s3:PutBucketPolicy
                  - s3:DeleteBucketPolicy
                  - s3:GetBucketPolicy
                  - s3:ListBucket
                  - s3:PutObject
                  - s3:DeleteObject
                Resource: 
                  - arn:aws:s3:::wmlgv-*-assistant-addin
                  - arn:aws:s3:::wmlgv-*-assistant-addin/*
                  - arn:aws:s3:::wmim-*-valuations-excel-use1
                  - arn:aws:s3:::wmim-*-valuations-excel-use1/*
              - Sid: InvalidateCloudfrontCache
                Effect: Allow
                Action:
                  - cloudfront:CreateInvalidation
                  - cloudfront:GetInvalidation
                  - cloudfront:ListDistributions
                Resource: 
                  - '*' # eeef
              - Sid: OACPermissions
                Effect: Allow
                Action:
                  - cloudfront:GetOriginAccessControl
                  - cloudfront:ListOriginAccessControls
                Resource: 
                  - '*' # eeef
              - Sid: ManageOACPermissions
                Effect: Allow
                Action:
                    - cloudfront:CreateOriginAccessControl
                    - cloudfront:UpdateOriginAccessControl
                    - cloudfront:DeleteOriginAccessControl
                Resource: 
                  -  !Sub 'arn:aws:cloudfront::${AWS::AccountId}:origin-access-control/*'
              - Sid: CloudfrontPermissions
                Effect: Allow
                Action:
                  - cloudfront:CreateDistribution
                  - cloudfront:CreateInvalidation
                  - cloudfront:GetDistribution
                  - cloudfront:TagResource
                  - cloudfront:UpdateDistribution
                  - cloudfront:DeleteDistribution
                  - cloudfront:ListTagsForResource
                Resource: 
                  !Sub arn:aws:cloudfront::${AWS::AccountId}:distribution/*
              - Sid: Route53Permissions
                Effect: Allow
                Action:
                  - route53:GetHostedZone
                  - route53:GetChange
                  - route53:ChangeResourceRecordSets
                Resource:
                  - arn:aws:route53:::hostedzone/*
                  - arn:aws:route53:::change/*
              - Sid: FetchKongSecret
                Effect: Allow
                Action:
                  - secretsmanager:GetSecretValue
                  - secretsmanager:DescribeSecret
                  - secretsmanager:ListSecretVersionIds
                Resource:
                  - !Sub 'arn:aws:secretsmanager:${AWS::Region}:${AWS::AccountId}:secret:/api-gateway/configuration/cp-woodmac-valuations*'
              - Sid: Waf2ListPermissions
                Effect: Allow
                Action:
                  - wafv2:ListIPSets
                  - wafv2:ListWebACLs
                Resource: "*"
              - Sid: Waf2ManageAclPermissions
                Effect: Allow
                Action:
                  - wafv2:CreateWebACL
                  - wafv2:UpdateWebACL
                  - wafv2:DeleteWebACL
                  - wafv2:GetWebACL
                  - wafv2:ListTagsForResource
                  - wafv2:TagResource
                Resource: 
                  - !Sub 'arn:aws:wafv2:${AWS::Region}:${AWS::AccountId}:global/webacl/WMLGVAssistantWebACL*'
                  - !Sub 'arn:aws:wafv2:${AWS::Region}:${AWS::AccountId}:global/ipset/incapsula-whitelist-cf-v2/*'
                  - !Sub 'arn:aws:wafv2:${AWS::Region}:${AWS::AccountId}:global/ipset/woodmac-internal-cf-v2/*'


  AgentRoleParameter:
    Type: AWS::SSM::Parameter
    Properties:
      Description: The name of the Agent Role
      Type: String
      Name: !Sub /${Environment}/wmlgv-assistant-agent/deployment-agent-role-name
      Value: !Ref DeploymentRole

Outputs:
  DeploymentRole:
    Description: Role for deploying resources
    Value: !Ref AgentRoleParameter

import * as React from "react";
import { LoginCallback } from "@okta/okta-react";
import { oktaClient } from "../security";

const notify = () => {
    const authState = oktaClient.authStateManager.getAuthState();

    if(authState?.isAuthenticated) {
        Office.context.ui.messageParent(JSON.stringify(authState?.isAuthenticated ? authState : {}));
    } else {
        // sometimes 500ms is not long enough!
        setTimeout(notify, 100);
    }
};

const Callback = () => {
    React.useEffect(() => {
        setTimeout(notify, 500);
    }, []);

    return (<><LoginCallback /><div>Logged in</div></>);
};

export { Callback };

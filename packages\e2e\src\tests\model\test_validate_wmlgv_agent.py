from .helpers.llm_comparison_bedrock import find_llm_similarity
from .helpers.semantic_comparison_local import find_semantic_similarity


def test_identical_outputs_score_identical():
    model_output = "The value of Clair is approx $1.5 Billion USD"
    expected_output = "The value of Clair is approx $1.5 Billion USD"
    semantic_similarity_score = find_semantic_similarity(
        model_output=model_output,
        expected_output=expected_output,
    )
    llm_similarity_score = find_llm_similarity(
        model_output=model_output,
        expected_output=expected_output,
        model_name="amazon.nova-micro-v1:0",
    )
    assert f"{semantic_similarity_score:.1f}" == "1.0"
    assert f"{llm_similarity_score:.1f}" == "1.0"


def test_similar_sentences_with_high_number_diff_llm_score_low():
    model_output = "The value of Clair is approx $1.5 Million USD"
    expected_output = "The value of Clair is approx $1.5 Billion USD"
    llm_similarity_score = find_llm_similarity(
        model_output=model_output,
        expected_output=expected_output,
        model_name="amazon.nova-micro-v1:0",
    )
    assert f"{llm_similarity_score:.1f}" < "0.3"


def test_similar_sentences_with_high_number_diff_semantic_score_high():
    model_output = "The value of Clair is approx $1.5 Million USD"
    expected_output = "The value of Clair is approx $1.5 Billion USD"
    semantic_similarity_score = find_semantic_similarity(
        model_output=model_output,
        expected_output=expected_output,
    )
    assert f"{semantic_similarity_score:.1f}" > "0.8"

def test_similar_diff_text_with_similar_numbers_llm_score_high():
    model_output = "The value of the asset called Clair, based on the BASE price scenario, is as follows:\n\n┌────────────┬──────────────┬──────────────┬─────────────┬─────────────┬─────────────┬─────────────┐\n│ asset_name │ price_scenar │ Total PV     │ Total PV    │ Remaining   │ Remaining   │ Total       │\n│            │ io           │ Post-tax     │ Pre-tax     │ PV Post-tax │ PV Pre-tax  │ Remaining   │\n│            │              │              │             │             │             │ Reserves WI │\n│            │              │              │             │             │             │ (mmboe)     │\n╞════════════╪══════════════╪══════════════╪═════════════╪═════════════╪═════════════╪═════════════╡\n│ Clair      │ BASE         │ 4845.0836    │ 14579.9613  │ 6638.6896   │ 11766.4999  │ 661.5872    │\n└────────────┴──────────────┴──────────────┴─────────────┴─────────────┴─────────────┴─────────────┘\n\nThe total post-tax value (Total PV Post-tax) of Clair is approximately $4,845 million, while its pre-tax value (Total PV Pre-tax) is about $14,580 million. The remaining post-tax value is $6,639 million, and the remaining pre-tax value is $11,766 million. The asset has total remaining reserves of about 662 million barrels of oil equivalent (mmboe)"
    expected_output = "The value of the asset called Clair, based on the BASE price scenario, is as follows:\n\n- Total Post-tax Present Value: $4,845.08 million\n- Total Pre-tax Present Value: $14,579.96 million\n- Remaining Post-tax Present Value: $6,638.69 million\n- Remaining Pre-tax Present Value: $11,766.50 million\n- Total Remaining Reserves (Working Interest): 661.59 million barrels of oil equivalent (mmboe)"
    llm_similarity_score = find_llm_similarity(
        model_output=model_output,
        expected_output=expected_output,
        model_name="amazon.nova-micro-v1:0",
    )
    assert f"{llm_similarity_score:.1f}" >= "0.9"

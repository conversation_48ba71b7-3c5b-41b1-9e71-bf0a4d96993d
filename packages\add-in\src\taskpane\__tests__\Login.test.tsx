import React from "react";
import { render, screen, fireEvent } from "@testing-library/react";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { Login } from "../Login";

jest.mock("../login-dialog", () => ({
  displayLoginDialog: jest.fn(),
}));

const { displayLoginDialog } = require("../login-dialog");

const renderWithProvider = (component: React.ReactElement) => {
  return render(<FluentProvider theme={webLightTheme}>{component}</FluentProvider>);
};

describe("Login", () => {
  beforeEach(() => {
    displayLoginDialog.mockClear();
  });

  it("renders login button", () => {
    renderWithProvider(<Login />);

    expect(screen.getByRole("button", { name: "Login" })).toBeInTheDocument();
  });

  it("calls displayLoginDialog when button is clicked", () => {
    renderWithProvider(<Login />);

    const loginButton = screen.getByRole("button", { name: "Login" });
    fireEvent.click(loginButton);

    expect(displayLoginDialog).toHaveBeenCalledTimes(1);
  });
});

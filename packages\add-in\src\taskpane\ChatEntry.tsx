import React, { useEffect } from "react";
import { SendRegular } from "@fluentui/react-icons";
import { <PERSON><PERSON>, <PERSON>, CardFooter, Textarea } from "@fluentui/react-components";
import { useStyles, useTextareaStyles } from "./use-styles";

const ChatEntry = ({ onChatMessageEntered }: { onChatMessageEntered: (message: string) => void }) => {
    const [message, setMessage] = React.useState<string>("");
    const { chatEntryContainer, charEntryCard, chatButton, textInputWrapper, sendBtnWrapper} = useStyles();
    const textInput = useTextareaStyles();
    const sendMessage = () => {
        onChatMessageEntered(message);
        setMessage("");
    };

    useEffect(() => {
        const listener = (event: KeyboardEvent) => {
          if (event.code === "Enter" || event.code === "NumpadEnter") {
            event.preventDefault();
            sendMessage();
          }
        };
        
        document.addEventListener("keydown", listener);

        return () => {
          document.removeEventListener("keydown", listener);
        };
      }, [sendMessage, message]);

    return (
      <div className={chatEntryContainer}>
        <Card className={charEntryCard}>
            <Textarea className={textInputWrapper} textarea={{ className: textInput.textArea }}  placeholder="Ask question here" value={message} onChange={(_, { value }) => setMessage(value)}/>
            <div className={sendBtnWrapper}>
              <Button className={chatButton} disabled={!message.trim()} icon={<SendRegular fontSize={20} />} onClick={sendMessage} />
            </div>
        </Card>
    </div> 
    );
}
export { ChatEntry };

type FlexDirection = 'row' | 'row-reverse' | 'column' | 'column-reverse';
export const flexStyles = {
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
  };
export const colors = {
    primary: "#005A8B",
    primary2: "#006BA6",
    grey: "#e6eef2",
    secondary100: "#00A9E0",
    secondary90: "#4DC3E9",
    active:"#00a9e0",
    nu60: "#93B0BF",
    nu90: "#0E374D",
    offWhite: "#f5f5f5",
    warning:"#F6C342",
    white: "#FFFFFF",
    black: "#000000",
}
export const font = {
        ty120: '20px',
        ty110: '18px',
        ty100: '16px',
        ty90: '14px',
        ty80: '12px',
        ty70: '10px',
}
export const borderRadius ={
    radiusM: 4,
    radiusL: 8,
}
export const gap ={
    gap10: '10px'
}
export const space ={
    spaceDefault: '4px',
    space8: '8px',
    space10: '10px'
}
export const flexDir: { row: FlexDirection; col: FlexDirection } ={
    row:'row',
    col:'column'
}
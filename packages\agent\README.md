# WoodMac GV Assistant Agent

This package contains an AWS Lambda function that handles AI agent requests for data queries. It integrates with AWS Bedrock Agent and provides tools for querying valuation data.

## Features

-   Query valuation datasets with SQL
-   Get schema information for different data tables
-   Returns structured data for the AI agent to process

## Local Development

### Prerequisites

-   Python 3.12+
-   pip

### Setup

1. Install dependencies:

```bash
pip install -r requirements.txt
```

2. Configure environment variables (or use AWS Lambda environment variables):

```
LOG_LEVEL=INFO
ASSET_TABLE_KEY=path/to/asset-table.parquet
COMPANY_ASSET_TABLE_KEY=path/to/company-asset-table.parquet
HYDROGEN_ASSET_TABLE_KEY=path/to/hydrogen-asset-table.parquet
COMPANY_ASSET_LAST_YEAR_TABLE_KEY=path/to/company-asset-last-year-table.parquet
```

3. Test locally:

```bash
python lambda_function.py
```

## Deployment

Package the Lambda function:

```bash
./build.sh
```

## API Endpoints

The Lambda function handles the following API endpoints:

-   `/api/get-company-asset-table-schema`: Returns the schema for the company asset table
-   `/api/get-asset-table-schema`: Returns the schema for the asset table
-   `/api/get-hydrogen-asset-table-schema`: Returns the schema for the hydrogen asset table
-   `/api/company-asset-last-year-table-schema`: Returns the schema for the company asset last year table
-   `/api/query_dataset`: Executes a SQL query against the dataset

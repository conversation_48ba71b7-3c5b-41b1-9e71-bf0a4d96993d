import logger from '../logger';

const TOKEN_CACHE = {
    token: null as string | null,
    expiryTime: null as Date | null,
};

export async function getToken(): Promise<string | null> {
    const currentTime = new Date();

    if (
        TOKEN_CACHE.token &&
        TOKEN_CACHE.expiryTime &&
        TOKEN_CACHE.expiryTime > currentTime
    ) {
        const remainingMinutes =
            (TOKEN_CACHE.expiryTime.getTime() - currentTime.getTime()) /
            (1000 * 60);
        logger.debug(
            `Using cached token. Valid for ${remainingMinutes.toFixed(
                1,
            )} more minutes`,
        );
        return TOKEN_CACHE.token;
    }

    try {
        const authUrl = process.env.LENS_IDENTITY_URL;
        const username = process.env.LENS_USERNAME;
        const password = process.env.LENS_PASSWORD;

        if (!authUrl || !username || !password) {
            logger.error(
                'Missing required environment variables for authentication',
            );
            return null;
        }

        const url = new URL(`${authUrl}/openam/identity/authenticate`);
        url.searchParams.append('username', username);
        url.searchParams.append('password', password);

        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 10000);

        const response = await fetch(url, {
            method: 'GET',
            signal: controller.signal,
        });

        clearTimeout(timeoutId);

        if (!response.ok) {
            throw new Error(`HTTP error! Status: ${response.status}`);
        }

        const responseText = await response.text();

        // the token here is okta's access_token
        const tokenMatch = /token\.id=([^&\s]+)/.exec(responseText);
        if (tokenMatch) {
            const token = tokenMatch[1];
            logger.debug('Authentication token retrieved successfully');
            TOKEN_CACHE.token = token;
            const expiryTime = new Date();
            expiryTime.setMinutes(expiryTime.getMinutes() + 60);
            TOKEN_CACHE.expiryTime = expiryTime;

            return token;
        } else {
            logger.error(
                `Failed to extract token from response: ${responseText}`,
            );
            return null;
        }
    } catch (error) {
        if (error instanceof Error) {
            if (error.name === 'AbortError') {
                logger.error('Request timed out');
            } else {
                logger.error(
                    error,
                    `Error getting authentication token: ${error.message}`,
                );
            }
        } else {
            logger.error(
                { error: error as Error },
                `Unexpected error during authentication: ${error}`,
            );
        }
        return null;
    }
}

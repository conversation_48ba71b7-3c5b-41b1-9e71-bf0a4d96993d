const mockOffice = {
  context: {
    ui: {
      displayDialogAsync: jest.fn(),
    },
  },
  EventType: {
    DialogMessageReceived: "DialogMessageReceived",
  },
};

global.Office = mockOffice as any;

const mockLocation = {
  origin: "http://localhost:3000",
  pathname: "/test",
};

Object.defineProperty(window, "location", {
  value: mockLocation,
  writable: true,
});

const mockOktaClient = {
  tokenManager: {
    setTokens: jest.fn(),
  },
};

jest.mock("../../security/okta-client", () => ({
  oktaClient: mockOktaClient,
}));

describe("login-dialog", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  describe("displayLoginDialog", () => {
    it("calls Office.context.ui.displayDialogAsync with correct parameters for non-wmlgv-assistant path", () => {
      mockLocation.pathname = "/test";

      const { displayLoginDialog } = require("../login-dialog");
      const mockDialog = {
        addEventHandler: jest.fn(),
        close: jest.fn(),
      };

      mockOffice.context.ui.displayDialogAsync.mockImplementation((_url, _options, callback) => {
        callback({ value: mockDialog });
      });

      displayLoginDialog();

      expect(mockOffice.context.ui.displayDialogAsync).toHaveBeenCalledWith(
        "http://localhost:3000/login.html",
        {
          height: 75,
          width: 40,
          displayInIframe: false,
          promptBeforeOpen: false,
        },
        expect.any(Function)
      );
    });

    it("calls Office.context.ui.displayDialogAsync with correct parameters for wmlgv-assistant path", () => {
      mockLocation.pathname = "/wmlgv-assistant/test";

      const { displayLoginDialog } = require("../login-dialog");

      displayLoginDialog();

      expect(mockOffice.context.ui.displayDialogAsync).toHaveBeenCalledWith(
        "http://localhost:3000/wmlgv-assistant/login.html",
        {
          height: 75,
          width: 40,
          displayInIframe: false,
          promptBeforeOpen: false,
        },
        expect.any(Function)
      );
    });

    it("sets up dialog event handler when dialog is created", () => {
      const { displayLoginDialog } = require("../login-dialog");
      const mockDialog = {
        addEventHandler: jest.fn(),
        close: jest.fn(),
      };

      displayLoginDialog();

      const callback = mockOffice.context.ui.displayDialogAsync.mock.calls[0][2];
      callback({ value: mockDialog });

      expect(mockDialog.addEventHandler).toHaveBeenCalledWith("DialogMessageReceived", expect.any(Function));
    });

    it("handles dialog message with tokens correctly", () => {
      const { displayLoginDialog } = require("../login-dialog");
      const mockDialog = {
        addEventHandler: jest.fn(),
        close: jest.fn(),
      };

      displayLoginDialog();

      // Get the callback that was passed to displayDialogAsync
      const dialogCallback = mockOffice.context.ui.displayDialogAsync.mock.calls[0][2];
      dialogCallback({ value: mockDialog });

      // Get the event handler that was added to the dialog
      const eventHandler = mockDialog.addEventHandler.mock.calls[0][1];

      const tokens = { accessToken: "test-token", idToken: "test-id-token" };
      const messageArgs = { message: JSON.stringify(tokens) };

      eventHandler(messageArgs);

      expect(mockOktaClient.tokenManager.setTokens).toHaveBeenCalledWith(tokens);
      expect(mockDialog.close).toHaveBeenCalledTimes(1);
    });

    it("handles dialog message without tokens", () => {
      const { displayLoginDialog } = require("../login-dialog");
      const mockDialog = {
        addEventHandler: jest.fn(),
        close: jest.fn(),
      };

      displayLoginDialog();

      const dialogCallback = mockOffice.context.ui.displayDialogAsync.mock.calls[0][2];
      dialogCallback({ value: mockDialog });

      const eventHandler = mockDialog.addEventHandler.mock.calls[0][1];
      const messageArgs = { message: undefined };

      eventHandler(messageArgs);

      expect(mockOktaClient.tokenManager.setTokens).not.toHaveBeenCalled();
      expect(mockDialog.close).toHaveBeenCalledTimes(1);
    });

    it("handles dialog error correctly", () => {
      const { displayLoginDialog } = require("../login-dialog");
      const mockDialog = {
        addEventHandler: jest.fn(),
        close: jest.fn(),
      };

      displayLoginDialog();

      const dialogCallback = mockOffice.context.ui.displayDialogAsync.mock.calls[0][2];
      dialogCallback({ value: mockDialog });

      const eventHandler = mockDialog.addEventHandler.mock.calls[0][1];
      const errorArgs = { error: 12345 };

      eventHandler(errorArgs);

      expect(mockOktaClient.tokenManager.setTokens).not.toHaveBeenCalled();
      expect(mockDialog.close).toHaveBeenCalledTimes(1);
    });
  });
});

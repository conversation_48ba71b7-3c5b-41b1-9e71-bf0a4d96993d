# Valuations API Proxy (Orchestrator)

This package serves as an API proxy/orchestrator for the wmlgv assistant, handling authentication, routing requests to the appropriate "agents", and managing responses.

## Features

-   API routing for valuation-related requests
-   API routing for Lens direct requests (via <PERSON><PERSON> Director)
-   Authentication via Okta JWT
-   Error handling and logging
-   Integrates with AWS Bedrock and Amazon Comprehend

## Local Development

### Prerequisites

-   Node.js v20 or higher
-   npm

### Setup

1. Create a `.env` file in the root directory from `.env.dist`:

```bash
cp .env.dist .env
```

2. Install dependencies:

```bash
npm install
```

3. Build the project:

```bash
npm run build
```

or if you want the build to run continuously checking for changes (use separate window, CMD+C to cancel)

```bash
npm run build:watch
```

NOTE: To debug locally in VS code (hitting breackpoints). You need to create a launch.json at the root of the project simialr to this: This is checked into the code already.
{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "Launch API",
            "program": "${workspaceFolder}/packages/orchestrator/dist/server.cjs",
            "request": "launch",
            "outFiles": [
                "${workspaceFolder}/**/*.cjs",
                "!**/node_modules/**"
            ],
            "skipFiles": [
                "<node_internals>/**"
            ],
            "type": "node"
        }
    ]
}

The in the vs code debugger window run the 'Launch API' profile 

4. Run the API server:

```bash
npm run api
```

The server will be available at http://localhost:3000 by default.

## Testing

```bash
npm test
```

## Build for Production

```bash
npm run build:ci
```

## Deployment

This service is deployed as a containerized application in ECS. The Docker image can be built with:

```bash
docker build -t valuations-api-proxy .
```

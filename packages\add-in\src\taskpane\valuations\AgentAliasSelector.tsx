import React from 'react';
import { Dropdown, Option } from '@fluentui/react-components';
import { AvailableAlias } from './valuations-types';

type AgentAliasSelectorProps = {
    availableAliases: AvailableAlias[], 
    selectedAlias?: AvailableAlias, 
    onSetSelectedAlias: (selectedAliasId: string) => void
};

const AgentAliasSelector = ({ availableAliases, selectedAlias, onSetSelectedAlias }: AgentAliasSelectorProps) => availableAliases.length ?  (
    <Dropdown
        placeholder="Select agent alias"
        selectedOptions={[selectedAlias?.agentAliasId]}
        // set to null if no alias is selected to keep component controlled and prevent warnings
        value={selectedAlias?.agentAliasName ?? null}
        onOptionSelect={(_, data) => onSetSelectedAlias(data.optionValue)}
    >
        {availableAliases.map(({ agentAliasId, agentAliasName }) => (
            <Option key={agentAliasId} value={agentAliasId}>{agentAliasName}</Option>
        ))}
    </Dropdown>
) : null;

export { AgentAliasSelector };

import { makeStyles, tokens } from "@fluentui/react-components";
import { colors , font , borderRadius , gap, flexStyles ,flexDir, space} from "./styleContants";

const useTextareaStyles = makeStyles({
    textArea:{
        minHeight:  `calc(${tokens.spacingVerticalM} * 6)`,
        height:  `calc(${tokens.spacingVerticalM} * 6)`,
        paddingRight: `calc(${tokens.spacingVerticalM} * 3)`,
        width:'100%',
        border:'none',
        overflow: 'hidden',
    },
  });

const useStyles = makeStyles({
    layoutWrapper:{
        backgroundColor:colors.offWhite,
        height:'100vh',
        ...flexStyles,
    },
    primaryButton:{     
        backgroundColor: colors.primary2,
        color:colors.white,
        height:  `calc(${tokens.spacingVerticalM} * 3)`,
        borderRadius: `${borderRadius.radiusM}px`,
        
        ':hover': {
            backgroundColor:colors.primary,
            color:colors.white,
        },
        ':focus': {
            backgroundColor:colors.primary,
            color:colors.white,
        },
        ':active':{
            backgroundColor: colors.active,
        }
    },
    btnIcon:{
        color:colors.black,
    },
    btnWithIcon:{
        backgroundColor:colors.white,
        borderRadius: `${borderRadius.radiusM}px`,
        color:colors.black,
        height:`calc(${tokens.spacingVerticalM} * 3)`,
        border:`2px solid ${colors.grey}`,
        ':hover': {
            backgroundColor:colors.grey,
            color:colors.black,
        },
        ':focus': {
            backgroundColor:colors.grey,
        },
        ':active':{
            backgroundColor: colors.active,
            color:colors.white
        },
    },
    chatPane: {
        display: "flex", 
        flexDirection: flexDir.col, 
        height: '96vh',
        padding: '2vh 5%',
        width:'100%',
        maxWidth: '100%',
        rowGap: gap.gap10,
        boxSizing: "border-box",
    },
    chatButton: {
        background: "transparent",
        border: "none",
        color: colors.primary,
    },
    botMessageContainer: {
        display: "flex",
        maxWidth: "100%",
    },
    userMessageContainer: {
        display: "flex",
        flexDirection: "row",
        justifyContent: "flex-end",
    },
    botMessage: {
        width: "100%",
        maxWidth: "100%",
        overflowWrap: "break-word",
        height: "fit-content",
        color: colors.black,
        backgroundColor:colors.white,
    },
    userMessage: {
        width: "auto",
        maxWidth: "80%",
        height: "fit-content",
        backgroundColor: colors.primary2,
        color: colors.white,
        justifyContent: "flex-end",
    },
    textInputWrapper:{
        height: `calc(${tokens.spacingVerticalM} * 6)`,
        maxHeight: `calc(${tokens.spacingVerticalM} * 6)`,
        overflow:'hidden',
        width:'100%',
        border:'none',
        ':after': {
            display:'none',
        },
        ':focus-within':{
            border:'none',
        },
        ':active': {
            border:'none',
        },
        ':hover':{
            border:'none',
        },
    },
    whatDoYouWantButton: {
        display: "flex", 
        justifyContent: "flex-start",
        width:"100%",
        ':focus': {
            border: `2px solid ${colors.active}`,
            outline: "none",
        },
        ':active':{
            backgroundColor: colors.active,
            color: colors.white,
        },
    },
    messageArea: {
        display: "flex", 
        flex: 1, 
        flexDirection: flexDir.col, 
        rowGap: gap.gap10,
        overflowY:'auto',
        padding: space.spaceDefault,
    },
    aiWarning: {
        fontSize: font.ty70, 
        backgroundColor: colors.offWhite,
        color: colors.nu90,
        borderRadius: `${borderRadius.radiusM}px`,
        padding: `${space.spaceDefault} ${space.space8}`, 
        textAlign: "center",
    },
    cardFooter: {
        display: "flex", 
        flexDirection: flexDir.col,
        alignItems: "flex-start",
    },
    chatEntryContainer: {
        flexShrink: 0,
        ...flexStyles,
        bottom:tokens.spacingVerticalXL,
        width:"100%",
    },
    sendBtnWrapper: {
        display: "flex",
        flexDirection: flexDir.col,
        flexFlow: "column-reverse",
        paddingBottom: space.spaceDefault,
        paddingRight: space.spaceDefault,
    },
    charEntryCard: {
        width: "100%", 
        flexDirection:flexDir.row,
        padding:0,
    },
    messageText: { 
        whiteSpace: "pre-wrap",
        '& p': {
            margin: 0,
        },
        '& ul': {
            margin: 0,
            display: "flex",
            flexDirection: flexDir.col,
        },
    },
    instructions: {
        display: "flex",
        backgroundColor: colors.white,
        flexDirection: flexDir.col, 
        alignItems: "center",
        padding:space.space10,
        boxShadow: tokens.shadow8,
    },
    instructionsText: {
        width: "100%",
    },
    suggestedQuestion: { 
        textAlign: "justify",
        maxWidth: "80%", 
        backgroundColor: "transparent", 
        fontWeight: "normal", 
        color: colors.secondary100,
        border: `1px solid ${colors.secondary100}`,
        ':hover': {
            color: colors.primary,
            border: `1px solid ${colors.primary}`,
        },
    },
    buttonIcon: {
        paddingRight: space.spaceDefault,
    },
});

const tableCellStyles = {
    headerCell: {
        fontWeight: "bold", 
        backgroundColor: colors.secondary100, 
        color: colors.white,
    },
    emptyColumn: {
        width: space.space10,
    },
};

const useTableStyles = makeStyles({
    table: {
        backgroundColor: colors.grey,
    },
    tableContainer: {
        overflowX: "auto",
    },
    ...tableCellStyles,
    emptyHeaderColumn: {
        ...tableCellStyles.headerCell,
        ...tableCellStyles.emptyColumn,
    },
    tableNotes: {
        display: "flex",
        flexDirection: "column",
    },
});

const useFooterStyles =  makeStyles({
    footer: {
        display: "flex", 
        justifyContent: "center",
        alignItems: "center", 
        fontSize: font.ty70,
        color: colors.primary,
    },
    link: {
        padding: space.spaceDefault,
        color: colors.primary,
    },
});

export { useStyles, useTextareaStyles, useTableStyles, useFooterStyles };

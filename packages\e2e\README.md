# End-to-End Tests

This package contains end-to-end tests for the wmlgv-assistant application.

It is structured in 2 parts:

* UI tests using [Playwright](https://playwright.dev/)
* Service/Model tests using [pytest](https://docs.pytest.org/en/stable/)

## Running UI Tests

The UI tests are written in TypeScript using the Playwright framework.

### Prerequisites

- Node.js v20 installed
- Dependencies installed (`npm install`)

### Running the tests

Run all UI tests:

```bash
npm run test:e2e
```

Run tests with UI mode for debugging:

```bash
npm run test:e2e -- --ui
```

Run tests in a specific browser:

```bash
npm run test:e2e -- --project=chromium
npm run test:e2e -- --project=firefox
```

### Test Reports

After test execution, reports are available in the `playwright-report` directory.

To view the last report:

```bash
npx playwright show-report
```

## Running Model Tests
This project uses LLM models to compare values so before running the tests make 
sure you've [logged in to aws cli](https://woodmac.atlassian.net/wiki/spaces/WMTECH/pages/*********/Accessing+AWS+accounts+-+AWS+Identity+Center) with an account that can invoke bedrock models.

The tests also invoke 3rd-party clients which requires your system to be setup, you can find more details on this for [Mac](https://woodmac.atlassian.net/wiki/spaces/WMTECH/pages/********/Mac+Setup) and [WSL2](https://woodmac.atlassian.net/wiki/spaces/WMTECH/pages/********/WSL2+Setup+Instructions) confluence pages.


First, install dependencies:

For Mac or WSL2/linux:
```bash
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
```

For Windows:
Depending on your aws sso set up on your local machine this may differ but if you use profiles for different aws accounts.
Ensure your equivalent variables are set as environment variables.
Eg: 
AWS_PROFLE=Name of profile you set up
AWS_REGION=us-east-1

Example powershell command for this
[System.Environment]::SetEnvironmentVariable("AWS_PROFILE", "Name of profile", [System.EnvironmentVariableTarget]::User)
[System.Environment]::SetEnvironmentVariable("AWS_REGION", "us-east-1", [System.EnvironmentVariableTarget]::User)

Also for Zscaler it will need to know the path your zscaler certificate. Set this up as an environment variable too.
CURL_CA_BUNDLE=path to certificate Eg C:\Users\<USER>\zscalercert.crt

open a git bash terminal (or one of your choice such as powershell and just convert commands below)
cd to the 'e2e' folder

```bash
python3 -m venv .venv
source .venv/Scripts/activate
pip install -r requirements.txt
pip install pytest
```
Then run the end-to-end tests, by invoking:

```bash
pytest
```
If you are using VS Code you can set up these tests to run in you test explorer for a visual aid and to debug



## Adding New Tests

When adding new tests:

1. Create new test files with the prefix `test_` to ensure pytest discovers them
2. Use appropriate fixtures from `conftest.py`
3. Group tests logically by functionality

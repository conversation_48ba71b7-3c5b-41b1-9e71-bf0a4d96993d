import * as React from "react";
import { LDProvider } from "launchdarkly-react-client-sdk";
import { FluentProvider, webLightTheme } from "@fluentui/react-components";
import { oktaClient, Secured } from "../security";
import { Security, useOktaAuth } from "@okta/okta-react";
import { Layout } from "./Layout";
import { ChatPane } from "./ChatPane";
import { launchDarkly } from "../config";

const FeatureFlagsProvider = ({ children }) => {
  const { authState } = useOktaAuth();

  if (!authState?.isAuthenticated) {
    return null;
  }

  const user = authState.idToken?.claims;
  const userEmail = (user.primaryemail ?? user.email ?? user.login)?.toString();
  const ldContext = {
    kind: "user",
    key: user.sub,
    name: `${user.firstName} ${user.lastName}`,
    email: userEmail,
    ...(!userEmail?.endsWith("@woodmac.com") && { privateAttributes: ["name", "email"] }),
  };

  return (
    <LDProvider clientSideID={launchDarkly.clientId} context={ldContext}>
      {children}
    </LDProvider>
  );
};

const App = () => (
  <FluentProvider theme={webLightTheme}>
    <Security oktaAuth={oktaClient} restoreOriginalUri={() => {}}>
      <Layout>
        <Secured>
          <FeatureFlagsProvider>
            <ChatPane />
          </FeatureFlagsProvider>
        </Secured>
      </Layout>
    </Security>
  </FluentProvider>
);

export default App;

AWSTemplateFormatVersion: '2010-09-09'
Description: Deployment infra for VAI add-in
Parameters:
  Environment:
    Description: The environment in which the stack is deployed
    Type: String
    Default: dev
    AllowedValues: [dev, int, uat, prod]
  BusinessUnit:
    Type: String
    Default: Woodmac
  Contact:
    Description: Contact for stack owner(s)
    Type: String
    Default: '<EMAIL>'
    ConstraintDescription: Must be a valid email address.
  ProductCode:
    Description: Product code
    Type: String
    Default: 'wmlgv'
  ProjectCode:
    Description: Project code
    Type: String
    Default: 'DEV-VAL-SERV'
  IncapsulaIpSetArn:
    Description: ARN of the Incapsula IP set to use for whitelisting # not available from env mapping lambda
    Type: String
  WoodmacInternalIpSetArn:
    Description: ARN of the Woodmac Internal IP set to use for whitelisting # not available from env mapping lambda
    Type: String

Conditions:
  IsDev: !Equals [!Ref Environment, dev]

Resources:
  CustomMapping:
    Type: Custom::Lookup
    Properties:
      ServiceToken: !Join
        - ":"
        - - "arn:aws:lambda"
          - !Ref "AWS::Region"
          - !Ref "AWS::AccountId"
          - "function:liveservices-environment-mappings-lambda"
      environment: !Ref Environment
      region: !Ref "AWS::Region"
  
  AddInS3Bucket:
    Type: AWS::S3::Bucket
    Properties:
      BucketEncryption:
        ServerSideEncryptionConfiguration:
          - ServerSideEncryptionByDefault:
              SSEAlgorithm: AES256
      BucketName: !Sub "${ProductCode}-${Environment}-assistant-addin"
      PublicAccessBlockConfiguration:
        BlockPublicAcls: true
        BlockPublicPolicy: true
        IgnorePublicAcls: true
        RestrictPublicBuckets: true
      Tags:
        - Key: BusinessUnit
          Value:
            Ref: BusinessUnit
        - Key: Contact
          Value:
            Ref: Contact
        - Key: Environment
          Value:
            Ref: Environment
        - Key: ProductCode
          Value:
            Ref: ProductCode
        - Key: ProjectCode
          Value: 
            Ref: ProjectCode
    UpdateReplacePolicy: Retain
    DeletionPolicy: Delete

  AddInOriginAccessControl:
    Type: AWS::CloudFront::OriginAccessControl
    Properties:
      OriginAccessControlConfig:
        Name: !Sub "${ProductCode}-${Environment}-addin-oac"
        Description: "OAC for S3 origin"
        SigningBehavior: always
        SigningProtocol: sigv4
        OriginAccessControlOriginType: s3
        

  AddInS3BucketS3BucketPolicy:
    Type: AWS::S3::BucketPolicy
    Properties:
      Bucket:
        Ref: AddInS3Bucket
      PolicyDocument:
        Statement:
          - Action: s3:GetObject
            Effect: Allow
            Principal:
              Service: cloudfront.amazonaws.com
            Resource: !Sub "arn:aws:s3:::${ProductCode}-${Environment}-assistant-addin/*"
            Condition:
              StringEquals:
                AWS:SourceArn: !Sub "arn:aws:cloudfront::${AWS::AccountId}:distribution/${AddInCF}"
        Version: "2012-10-17"

  AddInWebACL:
    Type: AWS::WAFv2::WebACL
    Properties:
      Name: !Sub "WMLGVAssistantWebACL${Environment}"
      Scope: CLOUDFRONT
      DefaultAction:
        Block: {}
      VisibilityConfig:
        SampledRequestsEnabled: true
        CloudWatchMetricsEnabled: true
        MetricName: !Sub "WMLGVAssistantWebACLMetric${Environment}"
      Rules:
        - Name: !Sub "WMLGVAssistantIncapsulaWhitelistRule${Environment}"
          Priority: 0
          Action:
            Allow: {}
          VisibilityConfig:
            SampledRequestsEnabled: true
            CloudWatchMetricsEnabled: true
            MetricName: !Sub "WMLGVAssistantIncapsulaWhitelistMetric${Environment}"
          Statement:
            IPSetReferenceStatement:
              Arn: !Ref IncapsulaIpSetArn 
        -  !If 
            - IsDev 
            - Name: !Sub "WMLGVAssistantWoodmacInternalWhitelistRule${Environment}"
              Priority: 1
              Action:
                Allow: {}
              VisibilityConfig:
                SampledRequestsEnabled: true
                CloudWatchMetricsEnabled: true
                MetricName: !Sub "WMLGVAssistantWoodmacInternalWhitelistMetric${Environment}"
              Statement:
                IPSetReferenceStatement:
                  Arn: !Ref WoodmacInternalIpSetArn
            - !Ref "AWS::NoValue"
      Tags:
        - Key: BusinessUnit
          Value:
            Ref: BusinessUnit
        - Key: Contact
          Value:
            Ref: Contact
        - Key: Environment
          Value:
            Ref: Environment
        - Key: ProductCode
          Value:
            Ref: ProductCode
        - Key: ProjectCode
          Value: 
            Ref: ProjectCode

  AddInCF:
    Type: AWS::CloudFront::Distribution
    Properties:
      DistributionConfig:
        Aliases: 
          - !Sub "${ProductCode}-assistant-addin.${Environment}.woodmac.com"
        Comment: !Sub "${ProductCode}-assistant-addin.${Environment}.woodmac.com"
        CustomErrorResponses:
          - ErrorCode: 400
            ResponseCode: 200
            ResponsePagePath: /taskpane.html
          - ErrorCode: 403
            ResponseCode: 200
            ResponsePagePath: /taskpane.html
        DefaultCacheBehavior:
          AllowedMethods:
            - GET
            - HEAD
          ForwardedValues:
            Cookies:
              Forward: none
            QueryString: false
          TargetOriginId: !Sub "${ProductCode}-${Environment}-assistant-addin.s3.${AWS::Region}.amazonaws.com"
          ViewerProtocolPolicy: https-only
        DefaultRootObject: /taskpane.html
        Enabled: true
        Origins:
          - DomainName: !Sub "${ProductCode}-${Environment}-assistant-addin.s3.${AWS::Region}.amazonaws.com"
            Id: !Sub "${ProductCode}-${Environment}-assistant-addin.s3.${AWS::Region}.amazonaws.com"
            OriginAccessControlId: !GetAtt AddInOriginAccessControl.Id
            S3OriginConfig:
              OriginAccessIdentity: "" # needs to be empty for OAC
        PriceClass: PriceClass_100
        ViewerCertificate:
          AcmCertificateArn: !Sub
            - 'arn:aws:acm:${AWS::Region}:${AWS::AccountId}:certificate/${certificateid}'
            - certificateid: !GetAtt CustomMapping.sslCertificateId
          MinimumProtocolVersion: TLSv1.2_2018
          SslSupportMethod: sni-only
        WebACLId: !GetAtt AddInWebACL.Arn
      Tags:
        - Key: BusinessUnit
          Value:
            Ref: BusinessUnit
        - Key: Contact
          Value:
            Ref: Contact
        - Key: Environment
          Value:
            Ref: Environment
        - Key: ProductCode
          Value:
            Ref: ProductCode
        - Key: ProjectCode
          Value: 
            Ref: ProjectCode

  AddInPublicRecordSet:
    Type: AWS::Route53::RecordSet
    Properties:
      Name: !Sub "${ProductCode}-assistant-addin.${Environment}.woodmac.com"
      HostedZoneId: !GetAtt CustomMapping.publicHostedZoneId
      Type: A
      AliasTarget:
        DNSName: !GetAtt AddInCF.DomainName
        HostedZoneId: Z2FDTNDATAQYW2 # Cloudfront hosted zone

  AddInPrivateRecordSet:
    Type: AWS::Route53::RecordSet
    Properties:
      Name: !Sub "${ProductCode}-assistant-addin.${Environment}.woodmac.com"
      HostedZoneId: !GetAtt CustomMapping.privateHostedZoneId
      Type: A
      AliasTarget:
        DNSName: !GetAtt AddInCF.DomainName
        HostedZoneId: Z2FDTNDATAQYW2 # Cloudfront hosted zone

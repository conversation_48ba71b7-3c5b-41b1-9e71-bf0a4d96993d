#!/usr/bin/env groovy
@Library('utils') _
def sys = "NA"

AWS_REGION = 'us-east-1'
BUILD_ENVIRONMENT = woodmac.getJenkinsEnvironment()

String getEnvironment() {
    if (params.TARGET_ENVIRONMENT != null) {
        return params.TARGET_ENVIRONMENT
    }
    if (woodmac.getJenkinsEnvironment() == 'prod') {
        return 'aiint'
    }

    return 'aidev'
}

def getAvailableEnvironments() {
    if (woodmac.getJenkinsEnvironment() == 'dev') {
        return ['aidev']
    }
    return ['aiint', 'aiuat', 'aiprod']
}

def handleCloudFormationError(stackName) {
    echo "CloudFormation deployment failed for stack: ${stackName}"
    
    def stackEvents = sh(
        script: """
        aws cloudformation describe-stack-events --stack-name ${stackName} --region ${AWS_REGION} --query "reverse(sort_by(StackEvents[?contains(ResourceStatus, 'FAILED')], &Timestamp))[:10].[{Status:ResourceStatus, Reason:ResourceStatusReason, LogicalID:LogicalResourceId, Timestamp:Timestamp}]" --output json
        """,
        returnStdout: true
    ).trim()
    
    echo "Stack failure details:"
    echo stackEvents
    
    def stackStatus = sh(
        script: """
        aws cloudformation describe-stacks --stack-name ${stackName} --region ${AWS_REGION} --query "Stacks[0].StackStatus" --output text""",
        returnStdout: true
    ).trim()
    
    echo "Overall stack status: ${stackStatus}"
    
    error "CloudFormation stack deployment failed. See above for error details."
}

environmentName = getEnvironment()
AGENT_IAM_ROLE_PARAMETER_NAME = "/${environmentName}/wmlgv-assistant-agent/deployment-agent-role-name"
jenkinsAgent = "nexus.prod.woodmac.com/lens/deploy:2.9.3"

properties([
    parameters([
        choice(name: 'TARGET_ENVIRONMENT', description: 'Environment to deploy', choices: getAvailableEnvironments()),
        string(name: 'LAMBDA_VERSION', description: 'Version of Lambda function to deploy (required)', required: true, trim: true)
    ])
])

pipeline {
    agent {
        ecs {
            inheritFrom "dynamic-us-east-1-${getEnvironment()}"
            taskrole woodmac.getAgentRole(
                region: "us-east-1",
                environment: "${environmentName}",
                parameterName: "${AGENT_IAM_ROLE_PARAMETER_NAME}",
            )
            image "${jenkinsAgent}"
        }
    }
    environment {
        AWS_ENVIRONMENT = getEnvironment()
    }
    options {
        disableConcurrentBuilds()
        ansiColor('xterm')
    }
    stages {
        stage('Bedrock agent') {
            environment {
                AWS_ENVIRONMENT = getEnvironment()
                STACK_NAME = "wmlgv-assistant-agent-${getEnvironment()}"
                TEMPLATE_FILE = "../infra/ai-deployment-resources.yaml"
                BUCKET_NAME = "wmlgv-${environmentName}-assistant-lambda-deployment"
            }

            stages {
                stage('Download action group zip to s3') {
                    steps {
                        script {
                            def lambdaVersion = params.LAMBDA_VERSION
                            echo "Lambda version: ${lambdaVersion}"
                            
                            def zipFileName = "wmlgv-assistant-agent-${lambdaVersion}.zip"
                            echo "Zip file name: ${zipFileName}"
                            
                            echo "Pulling ${zipFileName} from Nexus"
                            
                            sh(
                                label: 'Pull packages from Nexus',
                                script: """
                                    curl -o "./${zipFileName}" "https://nexus.prod.woodmac.com/repository/lens-internal-modelling/wmlgv/assistant-agent-action-group/${zipFileName}"
                                """
                            )

                            echo "Unzipping ${zipFileName}"

                            sh(
                                label: 'Unzip agent code',
                                script: """
                                    unzip ${zipFileName} -d agent-lambda
                                """
                            )

                            echo "Uploading lambda zip and agent opi schema to S3 Bucket: ${BUCKET_NAME}"

                            sh(
                                label: 'Upload to S3 deployment bucket',
                                script: """
                                    aws s3 cp ${zipFileName} s3://${BUCKET_NAME}/wmlgv-assistant-agent-${lambdaVersion}.zip
                                    aws s3 cp agent-lambda/wmlgv-assistant-agent-schema.yaml s3://${BUCKET_NAME}/wmlgv-assistant-agent-schema.yaml
                                """
                            )
                        }
                    }
                }
                stage('Deploy agent resources') {
                    steps {
                        dir('jenkins') {
                            script {
                                def lambdaVersion = params.LAMBDA_VERSION
                                sh 'make lint'
                                try {
                                    sh "make deploy EXTRA_PARAMS=\"LambdaVersion=${lambdaVersion}\""
                                } catch (Exception e) {
                                    handleCloudFormationError(env.STACK_NAME)
                                }
                            }
                        }
                    }
                }
                stage('Create new agent version if required') {
                    agent {
                        ecs {
                            inheritFrom "dynamic-us-east-1-${getEnvironment()}"
                            taskrole woodmac.getAgentRole(
                                region: "us-east-1",
                                environment: "${environmentName}",
                                parameterName: "${AGENT_IAM_ROLE_PARAMETER_NAME}",
                            )
                            image "harbor.prod.woodmac.com/liveservices/jenkins-node-jnlp-cloudformation:3283.v92c105e0f819-1-alpine-jdk21-aws2"
                        }
                    }
                    steps {
                        dir('packages/agent-version-updater') {
                            script {
                                def agentId = sh(
                                    script: "aws bedrock-agent list-agents | jq -r '.agentSummaries[] | select(.agentName == \"wmlgv-${AWS_ENVIRONMENT}-assistant-agent\") | .agentId'",
                                    returnStdout: true
                                ).trim()
                                
                                sh """
                                    python3 -m venv venv
                                    source venv/bin/activate
                                    pip install -r requirements.txt
                                    python create_agent_version.py --agent_id ${agentId}
                                """
                            }
                        }
                    }
                }
            }
        }
    }
}

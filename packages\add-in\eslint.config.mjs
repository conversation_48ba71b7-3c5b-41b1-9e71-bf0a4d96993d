import tseslint from 'typescript-eslint';
import officeAddinRules from 'eslint-plugin-office-addins';

export default tseslint.config(
  {
    files: ['src/**/*.ts'],
    extends: [
      tseslint.configs.strict,
      ...tseslint.configs.stylistic,
    ],
    plugins: {
      'eslint-plugin-office-addins': officeAddinRules,
    },
  },
  {
    files: ['src/**/__tests__/**/*.{ts,tsx}'],
    extends: [
      tseslint.configs.strict,
      ...tseslint.configs.stylistic,
    ],
    plugins: {
      'eslint-plugin-office-addins': officeAddinRules,
    },
    rules: {
      '@typescript-eslint/no-explicit-any': 'off',
      '@typescript-eslint/no-require-imports': 'off',
    },
  },
  {
    ignores: ['dist/**', 'coverage/**', 'node_modules/**']
  }
);

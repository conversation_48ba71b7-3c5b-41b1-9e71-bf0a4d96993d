const mockOktaAuth = jest.fn();
const mockTokenManager = {
  getTokens: jest.fn(),
  hasExpired: jest.fn(),
  renew: jest.fn(),
  setTokens: jest.fn(),
};

jest.mock("@okta/okta-auth-js", () => ({
  OktaAuth: mockOktaAuth.mockImplementation(() => ({
    tokenManager: mockTokenManager,
  })),
}));

Object.defineProperty(window, "AppConfig", {
  value: {
    okta: {
      issuer: "https://test.okta.com",
      clientId: "test-client-id",
    },
  },
  writable: true,
});

const locationMock = {
  origin: "http://localhost:3000",
  pathname: "/test",
};

Object.defineProperty(window, "location", {
  value: locationMock,
  writable: true,
});

describe("okta-client", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    jest.resetModules();
  });

  describe("oktaClient creation", () => {
    it("creates OktaAuth with correct config for non-wmlgv-assistant path", () => {
      locationMock.pathname = "/test";

      require("../okta-client");

      expect(mockOktaAuth).toHaveBeenCalledWith({
        issuer: "https://test.okta.com",
        clientId: "test-client-id",
        redirectUri: "http://localhost:3000/login/callback",
        scopes: ["openid"],
        pkce: true,
      });
    });

    it("creates OktaAuth with correct config for wmlgv-assistant path", () => {
      locationMock.pathname = "/wmlgv-assistant/test";

      require("../okta-client");

      expect(mockOktaAuth).toHaveBeenCalledWith({
        issuer: "https://test.okta.com",
        clientId: "test-client-id",
        redirectUri: "http://localhost:3000/wmlgv-assistant/login/callback",
        scopes: ["openid"],
        pkce: true,
      });
    });
  });

  describe("getAccessToken", () => {
    beforeEach(() => {
      jest.resetModules();
    });

    it("returns access token when token exists and is not expired", async () => {
      const mockAccessToken = { accessToken: "test-token" };
      mockTokenManager.getTokens.mockResolvedValue({ accessToken: mockAccessToken });
      mockTokenManager.hasExpired.mockResolvedValue(false);

      const { getAccessToken } = require("../okta-client");
      const result = await getAccessToken();

      expect(result).toBe("test-token");
      expect(mockTokenManager.getTokens).toHaveBeenCalledTimes(1);
      expect(mockTokenManager.hasExpired).toHaveBeenCalledWith(mockAccessToken);
      expect(mockTokenManager.renew).not.toHaveBeenCalled();
    });

    it("renews and returns access token when token is expired", async () => {
      const mockAccessToken = { accessToken: "old-token" };
      const mockRenewedToken = { accessToken: "new-token" };

      mockTokenManager.getTokens.mockResolvedValue({ accessToken: mockAccessToken });
      mockTokenManager.hasExpired.mockResolvedValue(true);
      mockTokenManager.renew.mockResolvedValue(mockRenewedToken);

      const { getAccessToken } = require("../okta-client");
      const result = await getAccessToken();

      expect(result).toBe("new-token");
      expect(mockTokenManager.getTokens).toHaveBeenCalledTimes(1);
      expect(mockTokenManager.hasExpired).toHaveBeenCalledWith(mockAccessToken);
      expect(mockTokenManager.renew).toHaveBeenCalledWith("accessToken");
    });

    it("returns undefined when no access token exists", async () => {
      mockTokenManager.getTokens.mockResolvedValue({});

      const { getAccessToken } = require("../okta-client");
      const result = await getAccessToken();

      expect(result).toBeUndefined();
      expect(mockTokenManager.getTokens).toHaveBeenCalledTimes(1);
      expect(mockTokenManager.hasExpired).not.toHaveBeenCalled();
      expect(mockTokenManager.renew).not.toHaveBeenCalled();
    });
  });
});

import { expect } from '@playwright/test';

const delay = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

export default async ({ page }) => {
    await page.goto(
        'https://lens-internal-modelling-dev.woodmac.com/wmlgv-assistant/login.html',
    );
    const slowExpect = expect.configure({ timeout: 90 * 1000 });
    await slowExpect(page.getByText('Logged in')).toBeVisible();
    await delay(2000); // to store the token
};

import { useEffect, useState } from "react";
import { AvailableAlias } from "./valuations-types";
import { getAvailableAliases } from "./requests";
import { useFlags } from "launchdarkly-react-client-sdk";

const loadAvailableAliases = async (setAvailableAliases: (agentAliases: AvailableAlias[]) => void) => {
    try {        
        const aliases = await getAvailableAliases();
        setAvailableAliases(aliases);
    } catch (error) {
        console.error("Error loading available aliases:", error);
    }
};

const useAvailableAliases = (onAliasChange: () => void) => {
    const { selectAgentAlias } = useFlags();
    const [availableAliases, setAvailableAliases] = useState<AvailableAlias[]>([]);
    const [selectedAlias, setSelectedAlias] = useState<AvailableAlias | undefined>();

    useEffect(() => {
        if(selectAgentAlias) {
            loadAvailableAliases(setAvailableAliases);
        }
    }, [selectAgentAlias]);
    

    const onSetSelectedAlias = (selectedAliasId: string) => {
        setSelectedAlias(availableAliases.find(({ agentAliasId }) => agentAliasId === selectedAliasId));
        onAliasChange();
    };

    return {
        availableAliases,
        selectedAlias,
        onSetSelectedAlias,
    }
};

export { useAvailableAliases };
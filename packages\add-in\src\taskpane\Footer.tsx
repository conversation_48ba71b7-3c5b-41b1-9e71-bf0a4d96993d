import React from "react";
import { Link } from "@fluentui/react-components";
import { useFooterStyles } from "./use-styles";

const FooterLink = ({ href, children }: { href: string; children: React.ReactNode }) => {
    const { link } = useFooterStyles();

    return (
        <Link className={link} href={href} as="a" target="_blank" rel="noopener noreferrer">
            {children}
        </Link>
    );
};

const Footer = () => {
    const { footer } = useFooterStyles();

    return <div className={footer}>
        <FooterLink href="https://www.woodmac.com/conditions-of-use/">Conditions of use</FooterLink>
        <span role="separator" aria-hidden="true">|</span>
        <FooterLink href="https://www.woodmac.com/privacy-policy-centre/">Privacy policy</FooterLink>
      </div>;
};

export { Footer };
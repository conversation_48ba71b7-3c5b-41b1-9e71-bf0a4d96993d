openapi: 3.0.1
info:
    title: Valuations Data Query
    version: '1.0'
    description: 'This tool allows to query the Valuations dataset to understand the value of individual assets, hydrogen assets, companies or companies last year value'
paths:
    /api/get-company-asset-discrete-table-schema:
        get:
            summary: 'Gets the schema for company asset discrete table'
            description: 'Returns the schema of the company discrete metrics table. Refer to this schema to understand the structure of the table and what columns it has. Once the schema is known, you can call generate SQL query and call query_dataset to get the data.'
            operationId: 'company_discrete_table_schema'
            responses:
                '200':
                    description: 'Successfully retrieved schema'
                    content:
                        application/json:
                            schema:
                                type: object
                '400':
                    description: 'Bad request'
                '500':
                    description: 'Internal server error'
    /api/get-asset-discrete-table-schema:
        get:
            summary: 'Gets the schema for the non-company asset discrete table'
            description: 'Returns the schema of the asset discrete metrics table. Refer to this schema to understand the structure of the table and what columns it has. Once the schema is known, you can call generate SQL query and call query_dataset to get the data.'
            operationId: 'get_asset_discrete_schema'
            responses:
                '200':
                    description: 'Successfully retrieved schema'
                    content:
                        application/json:
                            schema:
                                type: object
                '400':
                    description: 'Bad request'
                '500':
                    description: 'Internal server error'
    /api/get-company-asset-timeseries-schema:
        get:
            summary: 'Gets the schema for company asset timeseries table'
            description: 'Returns the schema of the company timeseries metrics table. Refer to this schema to understand the structure of the table and what columns it has. Once the schema is known, you can call generate SQL query and call query_dataset to get the data.'
            operationId: 'company_timeseries_schema'
            responses:
                '200':
                    description: 'Successfully retrieved schema'
                    content:
                        application/json:
                            schema:
                                type: object
                '400':
                    description: 'Bad request'
                '500':
                    description: 'Internal server error'
    /api/get-asset-timeseries-schema:
        get:
            summary: 'Gets the schema for the non-company asset timeseries table'
            description: 'Returns the schema of the asset timeseries metrics table. Refer to this schema to understand the structure of the table and what columns it has. Once the schema is known, you can call generate SQL query and call query_dataset to get the data.'
            operationId: 'get_asset_timeseries_schema'
            responses:
                '200':
                    description: 'Successfully retrieved schema'
                    content:
                        application/json:
                            schema:
                                type: object
                '400':
                    description: 'Bad request'
                '500':
                    description: 'Internal server error'

    /api/query_dataset:
        get:
            summary: 'Queries valuation dataset'
            description: |
                ## Data Access Instructions
                You have access to oil and gas asset valuation data through this specialized data retrieval endpoint. To retrieve information, formulate precise SQL queries internally, but present natural language insights to users.

                ### Data Sources
                The data is structured in 2 ways: using discrete (single-number metrics) and timeseries (annualized metrics). There are also 2 splits of datasets for generic asset-level metrics and company-asset metrics where each asset is associated with one or more companies. The schema for these tables looks like this:

                - All tables have the following common columns:
                  'record_id': String, 'country': String, 'business_area': String, 'regime': String, 'asset_name': String, 'asset_type': String (contains values: FIELD, PLANT, TRANSPORT, PLAY_COMPANY_GEM), 'valuation_type': String, 'price_scenario': String, 'discount_date': Int64, 'discount_rate': Int64

                - **Asset data (discrete)** is stored in `asset_discrete_table` with schema:
                  'total_pv_post_tax': Float64, 'total_pv_pre_tax': Float64, 'remaining_pv_post_tax': Float64, 'remaining_pv_pre_tax': Float64, 'remaining_pv_boe_post_tax': Float64, 'remaining_pv_boe_pre_tax': Float64, 'total_government_take_value': Float64, 'total_government_take_percentage': Float64, 'remaining_government_take_value': Float64, 'remaining_government_take_percentage': Float64, 'p_i_ratio': Float64, 'capex_boe': Float64, 'opex_boe': Float64, 'remaining_liquid_reserves_wi_mmbbls': Float64, 'remaining_gas_reserves_wi_bcf': Float64, 'total_remaining_reserves_wi_mmboe': Float64, 'total_reserves_wi_mmboe': Float64, 'post_tax_irr': Float64, 'pre_tax_irr': Float64, 'payback_period_years': Float64, 'reserve_life_at_current_production_years': Float64, 'corporate_income_tax_loss_position': Int64, 'other_tax_loss_position': Int64, 'tax_overhang': Float64, 'remaining_liquid_reserves_ent_mmbbls': Float64, 'remaining_gas_reserves_ent_bcf': Float64, 'total_remaining_reserves_ent_mmboe': Float64, 'total_reserves_ent_mmboe': Float64, 'total_liquid_reserves_ent_mmbbls': Float64, 'total_gas_reserves_ent_bcf': Float64, 'total_liquid_reserves_wi_mmbbls': Float64, 'total_gas_reserves_wi_bcf': Float64, 'taxation': String, 'basin': String, 'country_region': String, 'region': String, 'super_region': String, 'onshore_offshore_breakdown': String, 'primary_resource_theme': String, 'prms_classified_breakdown': String, 'maturity': String, 'development_type': String, 'hydrocarbon_type': String, 'asset_commerciality': String, 'operator': String, 'discovery_date': String, 'first_capex_year': Int64, 'production_end_year': Int64, 'partners': String
                - Asset data (timeseries) is stored in `asset_timeseries_table` with schema:
                  'year': Int64, 'production_liquids': Float64, 'production_gas': Float64, 'gross_revenue': Float64, 'operating_costs': Float64, 'capital_expenditure': Float64, 'royalty': Float64, 'government_take': Float64, 'state_carry': Float64, 'cash_flow': Float64, 'opex_per_boe': Float64, 'production_total_mmboe': Float64, 'production_gas_mmboe': Float64, 'production_liquids_mmboe': Float64, 'production_liquids_000boe_d': Float64, 'production_gas_000boe_d': Float64, 'production_total_000boe_d': Float64, 'taxation': String, 'basin': String, 'country_region': String, 'region': String, 'super_region': String, 'onshore_offshore_breakdown': String, 'primary_resource_theme': String, 'prms_classified_breakdown': String, 'maturity': String, 'development_type': String, 'hydrocarbon_type': String, 'asset_commerciality': String, 'operator': String, 'discovery_date': String, 'first_capex_year': Int64, 'production_end_year': Int64, 'partners': String

                - **Company-specific asset data (discrete)** is stored in `company_asset_discrete_table` with identical schema to assets plus a 'company' column.
                - **Company-specific asset data (timeseries)** is stored in `company_asset_timeseries_table` with identical schema to assets plus a 'company' column.

                ### Key Metric Definitions
                - **asset_commerciality**: Discovery classification is an indication of whether the discovery is commercial or technical.
                - **asset_name**: A unique name or alpha-numerical identifier assigned to an oil or gas discovery or field
                - **basin**: The designation given to a sedimentary basin associated with hydrocarbon exploration. Names are based upon Robertson's internationally recognised system.
                - **business_area**: A business area refers to a specific industry, sector, or field where a business operates, such as Upstream, LNG, Hydrogen etc.
                - **capex_boe**: The total capital expenditure divided by commercial proved plus probable reserves (oil equivalent basis). Can be reported in real terms or nominal terms. Usually reported undiscounted. Can be calculated over life of field or point forward.
                - **corporate_income_tax_loss_position**: Losses that can be carried forward to deduct from future profits
                - **country**: A country is a geopolitical area, often a sovereign state, with its own government, laws, and borders
                - **country_region**: The designation given to a subdivided area of a country, such as a province, state or otherwise recognised geography such as an upstream planning area.
                - **development_type**: Categorises the field development into the following: artificial island;  coal bed methane; compliant tower; extended reach drilling; fixed platform; FLNG; FPSO; mini tension leg platform; onshore; production jack-up; production semi-sub; spar; subsea; tension leg platform.
                - **discount_date**: The date at which the valuation of a discounted cashflow starts
                - **discount_rate**: A discount rate is an interest rate used to calculate the present value of a future cash flow or liability. In essence, it's the rate used to determine how much a future sum of money is worth today, acknowledging the time value of money.
                - **discovery_date**: Discovery date is the date on which the drilling rig suspended operations on the discovery well, confirming the field discovery. (DD/MM/YYYY)
                - **first_capex_year**: The year in which the first capital expenditure was incurred on the field. This can be a useful proxy for FID date where none is known.
                - **hydrocarbon_type**: Hydrocarbon type reports the field reservoir product: oil, oil & gas, gas, gas/condensate.
                - **maturity**: Classification of maturity for each field. Mature: <=33% reserves remaining. Mid-life: >33% and <66% remaining reserves. Early-life: >=66% remaining reserves. Undeveloped: contingent, good technical, probable. Pre-production: under development. Ceased: ceased, abandoned.
                - **onshore_offshore_breakdown**: Assigns field a category based on the water depth at the wellhead facilities: onshore, shallow water/shelf (1-399m), deepwater (400-1,499m), ultra-deepwater (>1,500m).
                - **operator**: Unique Wood Mackenzie code identifying the operator of a field entry on the dim_field table. The numeric code is persistent and will not change between data updates. Joins on dim_company.
                - **opex_boe**: The total field operating expenditure divided by commercial proved plus probable reserves (oil equivalent basis) Can be reported in real terms or nominal terms in US$. Can be quoted over the full or remaining life of a field or for a particular year. Operating costs are day-to-day costs incurred in producing oil and gas. These include, where appropriate, personnel costs, costs of materials and supplies, fixed and variable costs for field operations, transportation (non-tariff), leasing, insurance and G&A. Additionally, they include any tariffs paid to other assets for transportation and/or processing production.
                - **other_tax_loss_position**: The sum of tax losses resulting from a second form of tax which has a different tax base to, for example, standard corporate income tax
                - **p_i_ratio**: The profitability index (P/I) ratio is the ratio between the present value of future expected cash flows and the initial amount invested in the project
                - **partners**: Partner names are the names of companies with current equity/working interest in the field.
                - **payback_period_years**: The length of time required for an investment to recover its initial outlay in terms of profits
                - **post_tax_irr**: Post-tax IRR (Internal Rate of Return) is a financial metric that calculates the discount rate at which the net present value of a project or investment equals zero, using post-tax cash flows. In essence, it determines the rate of return an investment is expected to yield after taxes are factored in.
                - **pre_tax_irr**: Pre-tax IRR (Internal Rate of Return) is a financial metric that calculates the discount rate at which the net present value of a project or investment equals zero, using pre-tax cash flows. In essence, it determines the rate of return an investment is expected to yield before taxes are factored in.
                - **price_scenario**: A set of specific commodity prices used to value oil and gas production
                - **primary_resource_theme**: Primary resource theme reflects a single development challenge associated with a field. These are assigned one per field in order of importance: GTL, LNG, Deepwater, Oil Sands, Heavy Oil, Tight Oil, Acid/Sour Gas, Tight Gas, Shale Gas, CBM, Conventional Shelf, Conventional Onshore.
                - **prms_classified_breakdown**: Uses Petroleum Resource Management System (PRMS) definitions to divide Commercial fields into On production (onstream), Approved for development (under development), Justified for development (probable), Ceased, Abandoned, and Sub-commercial fields into Economically viable (could be developed in the future) and Not viable (unlikely to be developed).
                - **production_end_year**: Ceased or abandoned year of field, where field is not yet abandoned the projected final year of production is used.
                - **regime**: In the context of tax, a regime refers to the set of rules and laws that govern how taxes are collected and managed within a specific country, region, or jurisdiction
                - **region**: A group of countries representing a geographically contiguous area.
                - **remaining_gas_reserves_ent_bcf**: The sum of remaining entitlement gas production at the discount date. Entitlement gas production is that which the contractor or concession holder is entitled to physically receive. In a concession, this equates to concessionaire's working interest production excluding any royalty payments. In a PSC, this equates to the contractor's share of cost oil/gas and profit oil/gas. In a risk service contact, it is the contractor cash flow divided by the prevailing price.
                - **remaining_gas_reserves_wi_bcf**: The sum of remaining working interest gas production at the discount date. Working interest gas production is the product of multiplying gross field gas production by the company's equity interest in the field.
                - **remaining_government_take_percentage**: The governement share of NPV as a percentage of pre-share NPV at the discount date
                - **remaining_government_take_value**: The sum of annual governemnt cashflow at the discount date
                - **remaining_liquid_reserves_ent_mmbbls**: The sum of remaining entitlement production at the discount date. Entitlement production is that which the contractor or concession holder is entitled to physically receive. In a concession, this equates to concessionaire's working interest production excluding any royalty payments. In a PSC, this equates to the contractor's share of cost oil/gas and profit oil/gas. In a risk service contact, it is the contractor cash flow divided by the prevailing oil price.
                - **remaining_liquid_reserves_wi_mmbbls**: The sum of working interest liquids production at the discount date. Working interest production is the product of multiplying gross field liquids production by the company's equity interest in the field.
                - **remaining_pv_boe_post_tax**: The NPV of a remaining (or future) set of cash flows at the discount date after the deduction of royalty and tax, divided by total remaining reserves in barrels of oil equivalent
                - **remaining_pv_boe_pre_tax**: The NPV of a remaining (or future) set of cash flows at the discount date before the deduction of royalty and tax, divided by total remaining reserves in barrels of oil equivalent
                - **remaining_pv_post_tax**: The NPV of a remaining (or future) set of cash flows at the discount date after the deduction of royalty and tax.
                - **remaining_pv_pre_tax**: The NPV of a remaining (or future) set of cash flows at the discount date before the deduction of royalty and tax.
                - **reserve_life_at_current_production_years**: Total remaining reserves divided by current year production rate
                - **super_region**: Large group of countries representing a continent.
                - **tax_overhang**: Tax incurred, but not due to be paid until a point after the discount date of a valuation
                - **taxation**: Describes the tax regime which a field is subject to: concession, production sharing contract, risk contract.
                - **total_gas_reserves_ent_bcf**: The sum of entitlement gas production over the lifetime of the asset. Entitlement production is that which the contractor or concession holder is entitled to physically receive. In a concession, this equates to concessionaire's working interest production excluding any royalty payments. In a PSC, this equates to the contractor's share of cost oil/gas and profit oil/gas. In a risk service contact, it is the contractor cash flow divided by the prevailing price.
                - **total_gas_reserves_wi_bcf**: The sum of working interest gas production over the lifetime of an asset. Working interest production is the product of multiplying gross field gas production by the company's equity interest in the field.
                - **total_government_take_percentage**: The governement share of NPV as a percentage of pre-share NPV over the entire life of an asset
                - **total_government_take_value**: The sum of governemnt cashflow over the entire life of an asset
                - **total_liquid_reserves_ent_mmbbls**: The sum of entitlement liquids production over the lifetime of the asset. Entitlement production is that which the contractor or concession holder is entitled to physically receive. In a concession, this equates to concessionaire's working interest production excluding any royalty payments. In a PSC, this equates to the contractor's share of cost oil/gas and profit oil/gas. In a risk service contact, it is the contractor cash flow divided by the prevailing oil price.
                - **total_liquid_reserves_wi_mmbbls**: The sum of working interest liquids production over the lifetime of an asset. Working interest production is the product of multiplying gross field liquids production by the company's equity interest in the field.
                - **total_pv_post_tax**: The NPV of a cash flow over the lifetime of an asset after the deduction of royalty and tax.
                - **total_pv_pre_tax**: The NPV of a cash flow over the lifetime of an asset before the deduction of royalty and tax.
                - **total_remaining_reserves_ent_mmboe**: The sum of remaining entitlement production at the discount date. Entitlement production is that which the contractor or concession holder is entitled to physically receive. In a concession, this equates to concessionaire's working interest production excluding any royalty payments. In a PSC, this equates to the contractor's share of cost oil/gas and profit oil/gas. In a risk service contact, it is the contractor cash flow divided by the prevailing oil price.
                - **total_remaining_reserves_wi_mmboe**: The sum of remaining working interest production at the discount date.
                - **total_reserves_ent_mmboe**: The sum of entitlement production over the lifetime of the asset. Entitlement production is that which the contractor or concession holder is entitled to physically receive. In a concession, this equates to concessionaire's working interest production excluding any royalty payments. In a PSC, this equates to the contractor's share of cost oil/gas and profit oil/gas. In a risk service contact, it is the contractor cash flow divided by the prevailing oil price.
                - **total_reserves_wi_mmboe**: The sum of working interest production over the lifetime of an asset. Working interest production is the product of multiplying gross field production by the company's equity interest in the field.

                ### Query Processing Guidelines

                1. **SQL Query Construction**
                  - Generate read-only SQL queries only
                  - Include appropriate WHERE clauses to limit data retrieval
                  - Be opinionated about the columns and include columns that might be useful for a follow up query even when not explicitly asked for but never retrieve more than 10 columns
                  - For discrete metrics limit to max 50 rows
                  - For timeseries metrics always apply a year range and limit it to max 100 years/rows. Usually the data will be for 100 hundred years and will start at 1965 and end 2064 but it might be different for each asset. If not specified otherwise use the current_year - 5 as the starting point and current_year + 25 as the end point. The current_year is 2025.

                2. **Default Behaviors**
                  - Use 'BASE' price scenario when unspecified (alternatives: 'LOW', 'HIGH')
                  - For valuation questions, default to "remaining_pv_post_tax" and "remaining_pv_pre_tax"
                  - Include contextually relevant columns and identifying information
                  - Return asset_name and country for context in all queries

                3. **Error Handling**
                  - For zero results, broaden search with ILIKE operator
                  - Handle spelling variations in asset/company names, tables use them their official names
                  - For ambiguous requests, ask more information before querying

                4. **Table Selection Logic**
                  - Default to `asset_table` for asset-focused queries
                  - Use `company_asset_table` when company names mentioned
                  - Use `company_asset_table` for cross-company asset comparisons

                5. **Response Formatting**
                  - Data returned as valid JSON objects
                  - Format numeric values appropriately

                ### Example Mappings

                **Example 1: Asset Valuation**
                - Input: "What's the value of Clair?"
                - Internal SQL: `SELECT asset_name, remaining_pv_post_tax, remaining_pv_pret_tax FROM asset_discrete_table WHERE asset_name = 'Clair' AND price_scenario = 'BASE' LIMIT 25`
                - Output is a JSON object in this format: `{"status":"ok","result": [{"asset_name":"Clair","remaining_pv_post_tax":3055.3574,"remaining_pv_pre_tax":6412.9758}]}`

                **Example 2: Comparative Analysis**
                - Input: "Compare the top 5 assets in Norway by remaining reserves"
                - Internal SQL: `SELECT asset_name, country, remaining_pv_post_tax, total_remaining_reserves_wi_mmboe FROM asset_discrete_table WHERE country = 'Norway' AND price_scenario = 'BASE' ORDER BY total_remaining_reserves_wi_mmboe DESC LIMIT 5`

                **Example 3: Company Portfolio**
                - Input: "What assets does exxon own in Brazil?"
                - Internal SQL: `SELECT asset_name, country, remaining_pv_post_tax FROM company_asset_discrete_table WHERE company = 'ExxonMobil' AND country = 'Brazil' AND price_scenario = 'BASE' LIMIT 50`

                 **Example 4: Asset's gross revenues using a date-range**
                - Input: "What are projected thunder horse gross revenues for the next 5 years?"
                - Internal SQL: `SELECT asset_name, gross_revenue FROM asset_timeseries_table WHERE price_scenario = 'BASE' AND asset_name = 'Thunder Horse' AND year >= 2025 AND year <= 2030`
            operationId: 'queryDataset'
            parameters:
                - name: sql_query
                  description: 'A query to run on the valuation datasets'
                  in: query
                  required: true
                  schema:
                      type: string
            responses:
                '200':
                    description: 'Successfully retrieved data'
                    content:
                        application/json:
                            schema:
                                type: object
                                properties:
                                    status:
                                        type: string
                                        enum: ['ok', 'Query failed']
                                        description: 'Indicates if the query was successful or not'
                                    result:
                                        type: array
                                        description: 'Array of result objects when query executes successfully'
                                        items:
                                            type: object
                                    error:
                                        type: string
                                        description: 'Error message when query fails'
                                oneOf:
                                    - required: ['status', 'result']
                                    - required: ['status', 'error']
                            examples:
                                successExample:
                                    value:
                                        {
                                            'status': 'ok',
                                            'result':
                                                [
                                                    {
                                                        'asset_name': 'Clair',
                                                        'remaining_pv_post_tax': 3055.3574,
                                                        'remaining_pv_pre_tax': 6412.9758,
                                                    },
                                                ],
                                        }
                '400':
                    description: 'Bad request'
                '500':
                    description: 'Internal server error'
                    content:
                        application/json:
                            examples:
                                errorExample:
                                    value:
                                        {
                                            'status': 'Query failed',
                                            'error': 'Invalid syntax in SELECT statement',
                                        }
